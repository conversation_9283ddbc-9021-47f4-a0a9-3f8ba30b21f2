#include "feature_operators/panel_ops.hpp"
#include "feature_operators/types.hpp" // Added
#include <Eigen/Dense> // Kept for other Eigen types
#include <vector>
#include <algorithm> // For std::sort, std::stable_sort
#include <numeric>   // For std::iota
#include <cmath>     // For std::isnan, std::floor
#include <limits>    // For std::numeric_limits
#include <boost/math/distributions/normal.hpp> // For normal quantile

namespace feature_operators {

const double NaN = std::numeric_limits<double>::quiet_NaN();
// CLAMP_EPSILON is now defined in panel_ops.hpp
// Helper function for row-wise NaN-aware mean
Eigen::ArrayXd rowwise_nanmean(const DataFrame& data) {
    Eigen::ArrayXd means = Eigen::ArrayXd::Constant(data.rows(), std::numeric_limits<double>::quiet_NaN());
    // #pragma omp parallel for
    for (Eigen::Index i = 0; i < data.rows(); ++i) {
        double current_sum = 0.0;
        int count = 0;
        for (Eigen::Index k = 0; k < data.cols(); ++k) {
            double val = data(i, k);
            if (!std::isnan(val)) {
                current_sum += val;
                count++;
            }
        }
        if (count > 0) {
            means(i) = current_sum / count;
        }
    }
    return means;
}

Eigen::ArrayXd rowwise_nanmedian(const DataFrame& data) {
    Eigen::ArrayXd medians = Eigen::ArrayXd::Constant(data.rows(), std::numeric_limits<double>::quiet_NaN());

    // 如果行数很多，可以考虑在此处并行化
    // #pragma omp parallel for // 需要确保 std::vector<double> row_values; 在循环内，线程局部
    for (Eigen::Index i = 0; i < data.rows(); ++i) {
        std::vector<double> row_values;
        row_values.reserve(data.cols()); // 预分配空间，提高 push_back 效率

        // 一次遍历，收集非 NaN 值
        for (Eigen::Index k = 0; k < data.cols(); ++k) {
            double val = data(i, k); // 直接访问 data(i,k)
            if (!std::isnan(val)) {
                row_values.push_back(val);
            }
        }

        if (row_values.empty()) {
            continue; // 该行全为 NaN，medians(i) 保持 NaN
        }

        size_t n = row_values.size();
        if (n % 2 == 0) { // 偶数个数
            // 需要排序后的第 (n/2 - 1) 和第 n/2 个元素
            // 找到第 (n/2 - 1) 个元素 (0-indexed)
            std::nth_element(row_values.begin(), row_values.begin() + (n / 2 - 1), row_values.end());
            double lower_median = row_values[n / 2 - 1];

            // 找到原本应在第 n/2 位置的元素，它是 [row_values.begin() + n/2, row_values.end()) 区间中的最小值
            double upper_median = *std::min_element(row_values.begin() + (n / 2), row_values.end());
            medians(i) = (lower_median + upper_median) / 2.0;
        } else { // 奇数个数
            std::nth_element(row_values.begin(), row_values.begin() + n / 2, row_values.end());
            medians(i) = row_values[n / 2];
        }
    }
    return medians;
}
Eigen::ArrayXd rowwise_nanstd(const DataFrame& data, int ddof = 1) {
    Eigen::ArrayXd means = rowwise_nanmean(data); // 调用上面高效的 nanmean
    Eigen::ArrayXd std_devs = Eigen::ArrayXd::Constant(data.rows(), std::numeric_limits<double>::quiet_NaN());

    // #pragma omp parallel for
    for (Eigen::Index i = 0; i < data.rows(); ++i) {
        if (std::isnan(means(i))) { // 如果均值是NaN，标准差也是NaN
            continue;
        }

        double sum_sq_diff = 0.0;
        int non_nan_count = 0;

        // 一次遍历计算离差平方和及非NaN计数
        for (Eigen::Index k = 0; k < data.cols(); ++k) {
            double val = data(i, k);
            if (!std::isnan(val)) {
                double diff = val - means(i);
                sum_sq_diff += diff * diff;
                non_nan_count++;
            }
        }

        if (non_nan_count > ddof) {
            std_devs(i) = std::sqrt(sum_sq_diff / (non_nan_count - ddof));
        }
        // else std_devs(i) 保持 NaN
    }
    return std_devs;
}
// Implementation for pn_Mean
DataFrame pn_Mean(const DataFrame& data) {
    DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);
    for (Eigen::Index i = 0; i < data.rows(); ++i) {
        auto current_row_expr = data.row(i); // Get a view of the current row

        // Calculate the count of non-NaN elements in the row
        // .array() is used for coefficient-wise operations like isNaN()
        int non_nan_count = (!current_row_expr.array().isNaN()).count();

        if (non_nan_count > 0) {
            // Calculate the sum of non-NaN elements
            // NaNs are replaced by 0.0 for the sum, then divided by the actual non_nan_count
            double sum_of_non_nans = current_row_expr.array().isNaN().select(0.0, current_row_expr.array()).sum();
            result.row(i).setConstant(sum_of_non_nans / non_nan_count);
        }
        // If non_nan_count is 0, the row in result remains NaN (as initialized)
    }
    return result;
}



DataFrame pn_Winsor(const DataFrame& data, double multiplier) {
    // It's good practice to assert preconditions.
    // The problem description mentions Multiplier > 10 is handled by caller,
    // but multiplier <=0 would be problematic. For now, assume valid multiplier.
    // if (multiplier <= 0) {
    //     throw std::invalid_argument("Multiplier must be positive for pn_Winsor.");
    // }

    DataFrame result = data; // Start with a copy
    Eigen::ArrayXd panel_std = rowwise_nanstd(data, 1);

    for (Eigen::Index i = 0; i < data.rows(); ++i) {
        if (std::isnan(panel_std(i))) { // If std is NaN, row is likely all NaN or insufficient data
            result.row(i).setConstant(NaN); // Set the whole row to NaN
            continue;
        }
        double threshold = panel_std(i) * multiplier;
        if (std::isnan(threshold)) { // If threshold became NaN (e.g. multiplier is NaN or panel_std was NaN)
            result.row(i).setConstant(NaN);
            continue;
        }

        // Replace inner loop with unaryExpr
        // feature_operators::NaN is available in this namespace
        result.row(i) = result.row(i).array().unaryExpr([threshold](double val) {
            if (std::isnan(val)) return val; // Preserve NaNs
            if (val > 0.0 && val > threshold) return threshold;
            if (val < 0.0 && val < -threshold) return -threshold;
            return val;
        }); // Eigen handles assignment from array expression back to matrix block
    }
    return result;
}

DataFrame pn_TransStd(const DataFrame& data) {
    Eigen::ArrayXd mean_data = rowwise_nanmean(data);
    Eigen::ArrayXd std_data = rowwise_nanstd(data, 1);
    DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

    for (Eigen::Index i = 0; i < data.rows(); ++i) {
        if (std::isnan(mean_data(i)) || std::isnan(std_data(i)) || std_data(i) == 0) {
            // If mean or std is NaN, or std is zero, result row remains NaN
            continue;
        }
        result.row(i) = (data.row(i) - mean_data(i)) / std_data(i);
    }
    return result;
}

DataFrame pn_Stand(const DataFrame& data) {
    Eigen::ArrayXd median_data = rowwise_nanmedian(data);
    Eigen::ArrayXd std_data = rowwise_nanstd(data, 1); // ddof=1
    DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

    for (Eigen::Index i = 0; i < data.rows(); ++i) {
        if (std::isnan(median_data(i)) || std::isnan(std_data(i)) || std_data(i) == 0) {
            // If median or std is NaN, or std is zero, result row remains NaN
            continue;
        }
        result.row(i) = (data.row(i) - median_data(i)) / std_data(i);
    }
    return result;
}


DataFrame pn_FillMin(const DataFrame& data) {
    // 使用 std::numeric_limits<double>::quiet_NaN() 进行初始化更标准
    DataFrame result = DataFrame::Constant(data.rows(), data.cols(), std::numeric_limits<double>::quiet_NaN());

    if (data.rows() == 0 || data.cols() == 0) { // 处理空 DataFrame 的情况
        return result;
    }

    for (Eigen::Index i = 0; i < data.rows(); ++i) {
        // data.row(i) 对于 ArrayXXd 类型的 data 已经是 Array 表达式
        // 直接在其上进行操作，避免显式创建 temp_array
        double row_min = data.row(i)  // 这是一个 Array 表达式
                             .isNaN() // 返回一个布尔类型的 Array 表达式
                             .select(std::numeric_limits<double>::infinity(), // 如果 isNaN() 为 true (是 NaN)，则选择 infinity
                                     data.row(i))                            // 如果 isNaN() 为 false (不是 NaN)，则选择原始值
                             .minCoeff();    // 在结果表达式上找到最小值

        // 如果 row_min 不是 +infinity，说明至少找到了一个非 NaN 值
        if (row_min != std::numeric_limits<double>::infinity()) {
            result.row(i).setConstant(row_min);
        }
        // 否则 (行中所有值都是 NaN 或行为空)，result.row(i) 保持初始化时的 NaN
    }
    return result;
}
DataFrame pn_FillMax(const DataFrame& data) {
    // 使用 std::numeric_limits<double>::quiet_NaN() 进行初始化更标准
    DataFrame result = DataFrame::Constant(data.rows(), data.cols(), std::numeric_limits<double>::quiet_NaN());

    if (data.rows() == 0 || data.cols() == 0) { // 处理空 DataFrame 的情况
        return result;
    }

    for (Eigen::Index i = 0; i < data.rows(); ++i) {
        // data.row(i) 对于 ArrayXXd 类型的 data 已经是 Array 表达式
        // 直接在其上进行操作，避免显式创建 temp_array
        double row_max = data.row(i)  // 这是一个 Array 表达式
                             .isNaN() // 返回一个布尔类型的 Array 表达式
                             .select(-std::numeric_limits<double>::infinity(), // 如果 isNaN() 为 true (是 NaN)，则选择 -infinity
                                     data.row(i))                             // 如果 isNaN() 为 false (不是 NaN)，则选择原始值
                             .maxCoeff();    // 在结果表达式上找到最大值

        // 如果 row_max 不是 -infinity，说明至少找到了一个非 NaN 值
        if (row_max != -std::numeric_limits<double>::infinity()) {
            result.row(i).setConstant(row_max);
        }
        // 否则 (行中所有值都是 NaN 或行为空)，result.row(i) 保持初始化时的 NaN
    }
    return result;
}
// Helper struct for ranking
struct ValueIndexPair {
    double value;
    Eigen::Index original_index;
    double rank; // Can store absolute or percentile rank
};

// Helper function to rank a single row (Eigen::VectorXd or Eigen::ArrayXd)
// Handles NaNs and assigns average rank for ties.
Eigen::ArrayXd rank_row_absolute(const Eigen::ArrayXd& row_data) {
    std::vector<ValueIndexPair> non_nan_values;
    for (Eigen::Index j = 0; j < row_data.size(); ++j) {
        if (!std::isnan(row_data(j))) {
            non_nan_values.push_back({row_data(j), j, 0.0});
        }
    }

    if (non_nan_values.empty()) {
        return Eigen::ArrayXd::Constant(row_data.size(), NaN);
    }

    std::stable_sort(non_nan_values.begin(), non_nan_values.end(),
                     [](const ValueIndexPair& a, const ValueIndexPair& b) {
        return a.value < b.value;
    });

    for (size_t j = 0; j < non_nan_values.size(); ) {
        size_t k = j;
        while (k < non_nan_values.size() && non_nan_values[k].value == non_nan_values[j].value) {
            k++;
        }
        double sum_ranks = 0;
        for (size_t l = j; l < k; ++l) {
            sum_ranks += (l + 1); // 1-based rank
        }
        double average_rank = sum_ranks / (k - j);
        for (size_t l = j; l < k; ++l) {
            non_nan_values[l].rank = average_rank;
        }
        j = k;
    }

    Eigen::ArrayXd result_row = Eigen::ArrayXd::Constant(row_data.size(), NaN);
    for (const auto& pair : non_nan_values) {
        result_row(pair.original_index) = pair.rank;
    }
    return result_row;
}

DataFrame pn_Rank2(const DataFrame& data) {
    DataFrame result(data.rows(), data.cols());
    for (Eigen::Index i = 0; i < data.rows(); ++i) {
        result.row(i) = rank_row_absolute(data.row(i)); // Uses helper rank_row_absolute
    }
    return result;
}

// Corrected helper function for percentile ranking with custom normalization for a single row
// This helper is used by pn_Rank, so its name doesn't need to change.
Eigen::ArrayXd rank_row_custom_norm(const Eigen::ArrayXd& row_data) {
    std::vector<ValueIndexPair> non_nan_values;
    for (Eigen::Index j = 0; j < row_data.size(); ++j) {
        if (!std::isnan(row_data(j))) {
            non_nan_values.push_back({row_data(j), j, 0.0});
        }
    }

    if (non_nan_values.empty()) {
        return Eigen::ArrayXd::Constant(row_data.size(), NaN);
    }

    int count_non_nan = non_nan_values.size();

    std::stable_sort(non_nan_values.begin(), non_nan_values.end(),
                     [](const ValueIndexPair& a, const ValueIndexPair& b) {
        return a.value < b.value;
    });

    // Calculate initial percentile ranks
    for (size_t j = 0; j < non_nan_values.size(); ) {
        size_t k = j;
        while (k < non_nan_values.size() && non_nan_values[k].value == non_nan_values[j].value) {
            k++;
        }
        double sum_ranks = 0;
        for (size_t l = j; l < k; ++l) {
            sum_ranks += (l + 1); // 1-based rank
        }
        double average_rank = sum_ranks / (k - j);
        for (size_t l = j; l < k; ++l) {
            non_nan_values[l].rank = average_rank / count_non_nan; // Percentile rank
        }
        j = k;
    }

    double min_pr = std::numeric_limits<double>::max();
    if (count_non_nan > 0) { // Should always be true if non_nan_values is not empty
        for(const auto& pair : non_nan_values) {
            min_pr = std::min(min_pr, pair.rank);
        }
    } else { // Should not be reached if non_nan_values was not empty
        return Eigen::ArrayXd::Constant(row_data.size(), NaN);
    }

    double cut_val = (count_non_nan > 0) ? (min_pr / 2.0) : 0.0; // Avoid division by zero if min_pr is from empty list (though guarded)

    Eigen::ArrayXd result_row = Eigen::ArrayXd::Constant(row_data.size(), NaN);
    for (const auto& pair : non_nan_values) {
        result_row(pair.original_index) = pair.rank - cut_val;
    }
    return result_row;
}


DataFrame pn_Rank(const DataFrame& data) {
    DataFrame result(data.rows(), data.cols());
    for (Eigen::Index i = 0; i < data.rows(); ++i) {
        result.row(i) = rank_row_custom_norm(data.row(i)); // Uses helper rank_row_custom_norm
    }
    return result;
}


DataFrame pn_Cut(const DataFrame& data, double lower_bound_pct, double upper_bound_pct) {
    DataFrame rank_result = pn_Rank(data); // Now calls pn_Rank

    double lower_b = lower_bound_pct / 100.0;
    double upper_b = upper_bound_pct / 100.0;

    DataFrame nan_array = DataFrame::Constant(data.rows(), data.cols(), NaN);

    // Condition is applied on the custom normalized ranks
    Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> condition =
        (rank_result >= lower_b) && (rank_result <= upper_b) && !rank_result.isNaN();

    return condition.select(data, nan_array);
}

DataFrame pn_TransNorm(const DataFrame& data) {
    boost::math::normal dist(0.0, 1.0); // Standard normal distribution

    // Step 1: Get the custom normalized ranks using pn_Rank
    DataFrame rank_data = pn_Rank(data);

    // Step 2: Apply normal quantile function to the ranked data
    return rank_data.unaryExpr([&](double x) {
        if (std::isnan(x)) {
            return NaN;
        }
        // Clamp x to (CLAMP_EPSILON, 1-CLAMP_EPSILON) to avoid boundary issues
        double clamped_x = std::max(CLAMP_EPSILON, std::min(x, 1.0 - CLAMP_EPSILON));

        // If after clamping, x is still problematic, return NaN
        if (clamped_x <= 0.0 || clamped_x >= 1.0) {
            return NaN;
        }
        return boost::math::quantile(dist, clamped_x);
    });
}

// Helper function for row-wise NaN-aware sum
Eigen::ArrayXd rowwise_nansum(const DataFrame& data) {
    Eigen::ArrayXd result = Eigen::ArrayXd::Constant(data.rows(), NaN);
    for (Eigen::Index i = 0; i < data.rows(); ++i) {
        auto current_row_expr = data.row(i);

        // Calculate the count of non-NaN elements in the row
        int non_nan_count = (!current_row_expr.array().isNaN()).count();

        if (non_nan_count > 0) {
            // Calculate the sum of non-NaN elements
            // NaNs are replaced by 0.0 for the sum.
            double sum_val = current_row_expr.array().isNaN().select(0.0, current_row_expr.array()).sum();
            result(i) = sum_val;
        }
        // If non_nan_count is 0 (all NaNs or empty row), result(i) remains NaN.
    }
    return result;
}




DataFrame pn_CrossFit(const DataFrame& x, const DataFrame& y) {
    if (x.rows() != y.rows() || x.cols() != y.cols()) {
        throw std::invalid_argument("Input DataFrames x and y must have the same dimensions for pn_CrossFit.");
    }

    DataFrame result = DataFrame::Constant(x.rows(), x.cols(), NaN);

    Eigen::ArrayXd x_mean_row = rowwise_nanmean(x); // Optimized helper
    Eigen::ArrayXd y_mean_row = rowwise_nanmean(y); // Optimized helper

    Eigen::ArrayXd a = Eigen::ArrayXd::Constant(x.rows(), NaN);
    Eigen::ArrayXd b = Eigen::ArrayXd::Constant(x.rows(), NaN);

    for (Eigen::Index i = 0; i < x.rows(); ++i) {
        double x_mean = x_mean_row(i);
        double y_mean = y_mean_row(i);

        if (std::isnan(x_mean) || std::isnan(y_mean)) {
            // a(i) and b(i) remain NaN
            continue;
        }

        // auto x_row_arr = x.row(i).array();
        // auto y_row_arr = y.row(i).array();
        Eigen::ArrayXd x_row_arr = x.row(i).array();
        Eigen::ArrayXd y_row_arr = y.row(i).array();

        // Create boolean arrays for NaN locations in original x and y rows
        auto x_is_nan = x_row_arr.isNaN();
        auto y_is_nan = y_row_arr.isNaN();
        auto either_is_nan = x_is_nan || y_is_nan; // True if x or y (or both) are NaN

        // Demeaned expressions - NaNs will propagate
        auto x_demeaned_expr = x_row_arr - x_mean;
        auto y_demeaned_expr = y_row_arr - y_mean;

        // Products - NaNs will propagate
        auto term_xy_expr = x_demeaned_expr * y_demeaned_expr;
        auto term_xx_expr = x_demeaned_expr.square();

        // Calculate sum_xy_for_row: sum of term_xy_expr where neither x nor y were NaN
        double sum_xy_for_row = 0.0;
        int count_xy = 0;
        for(Eigen::Index k=0; k<term_xy_expr.size(); ++k) {
            if (!either_is_nan(k)) { // Only consider if original x and y were not NaN
                sum_xy_for_row += term_xy_expr(k); // term_xy_expr(k) is not NaN here
                count_xy++;
            }
        }
        if (count_xy == 0) sum_xy_for_row = NaN; // All valid pairs resulted in NaN product or no valid pairs

        // Calculate sum_xx_for_row: sum of term_xx_expr where x was not NaN
        double sum_xx_for_row = 0.0;
        int count_xx = 0;
        for(Eigen::Index k=0; k<term_xx_expr.size(); ++k) {
             if (!x_is_nan(k)) { // Only consider if original x was not NaN
                sum_xx_for_row += term_xx_expr(k); // term_xx_expr(k) is not NaN here
                count_xx++;
            }
        }
        if (count_xx == 0) sum_xx_for_row = NaN;

        if (!std::isnan(sum_xy_for_row) && !std::isnan(sum_xx_for_row) && sum_xx_for_row != 0) {
            b(i) = sum_xy_for_row / sum_xx_for_row;
        }
        // else b(i) remains NaN

        if (!std::isnan(b(i))) { // y_mean and x_mean are already confirmed not NaN here
            a(i) = y_mean - b(i) * x_mean;
        }
        // else a(i) remains NaN
    }

    for (Eigen::Index i = 0; i < x.rows(); ++i) {
        if (std::isnan(a(i)) || std::isnan(b(i))) {
            // result.row(i) is already NaN from initialization
            continue;
        } else {
            result.row(i) = y.row(i).array() - (a(i) + b(i) * x.row(i).array());
        }
    }
    return result;
}

DataFrame pn_RankCentered(const DataFrame& data) {
    DataFrame ranked_data = pn_Rank(data); // Now calls pn_Rank
    // Formula: newData = _pn_Rank(data) * 2 - 1
    // _pn_Rank is pn_rank_custom_norm
    return ranked_data.unaryExpr([](double x) {
        if (std::isnan(x)) {
            return NaN;
        }
        return x * 2.0 - 1.0;
    });
}

} // namespace feature_operators
