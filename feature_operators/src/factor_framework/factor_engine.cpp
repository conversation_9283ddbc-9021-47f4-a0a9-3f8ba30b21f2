#include "factor_framework/factor_engine.hpp"
#include <iostream>
#include <thread>
#include <future>
#include <algorithm>

namespace factor_framework {

factor_engine::factor_engine(std::shared_ptr<factor_manager> factor_manager,
                          std::shared_ptr<data_interface> data_interface)
    : factor_manager_(factor_manager)
    , data_interface_(data_interface)
    , num_threads_(std::thread::hardware_concurrency())
    , enable_performance_stats_(true)
    , timeout_ms_(30000)
    , verbose_logging_(false) {

    if (num_threads_ == 0) {
        num_threads_ = 4;  // 默认4线程
    }

    log("FactorEngine initialized with " + std::to_string(num_threads_) + " threads");
}

calculation_result factor_engine::calculate_selected_factors() {
    auto selected_ids = factor_manager_->get_selected_factor_ids();
    if (selected_ids.empty()) {
        calculation_result result;
        result.success = false;
        result.error_message = "No factors selected";
        return result;
    }

    return calculate_factors_internal(selected_ids);
}

calculation_result factor_engine::calculate_factors(const std::vector<int>& factor_ids) {
    return calculate_factors_internal(factor_ids);
}

calculation_result factor_engine::calculate_factors(const std::vector<std::string>& factor_names) {
    std::vector<int> factor_ids;
    factor_ids.reserve(factor_names.size());

    for (const auto& name : factor_names) {
        auto factor = factor_manager_->get_factor(name);
        if (factor) {
            factor_ids.push_back(factor->get_id());
        } else {
            log("Warning: Factor not found: " + name);
        }
    }

    return calculate_factors_internal(factor_ids);
}

calculation_result factor_engine::calculate_single_factor(int factor_id) {
    return calculate_factors_internal({factor_id});
}

calculation_result factor_engine::calculate_all_factors() {
    auto all_ids = factor_manager_->get_all_factor_ids();
    return calculate_factors_internal(all_ids);
}

void factor_engine::set_num_threads(int num_threads) {
    if (num_threads <= 0) {
        num_threads_ = std::thread::hardware_concurrency();
        if (num_threads_ == 0) {
            num_threads_ = 4;
        }
    } else {
        num_threads_ = num_threads;
    }

    log("Set number of threads to " + std::to_string(num_threads_));
}

void factor_engine::reset_performance_stats() {
    last_performance_stats_ = PerformanceStats{};
}

std::pair<bool, std::string> factor_engine::validate_environment() const {
    // 检查因子管理器
    if (!factor_manager_) {
        return {false, "Factor manager is null"};
    }

    if (factor_manager_->getFactorCount() == 0) {
        return {false, "No factors loaded"};
    }

    // 检查数据接口
    if (!data_interface_) {
        return {false, "Data interface is null"};
    }

    auto field_names = data_interface_->getFieldNames();
    if (field_names.empty()) {
        return {false, "No data loaded"};
    }

    // 检查数据维度一致性
    if (!data_interface_->validateDataDimensions()) {
        return {false, "Data dimensions are inconsistent"};
    }

    // 检查选中因子的数据依赖
    auto selected_ids = factor_manager_->get_selected_factor_ids();
    if (!selected_ids.empty()) {
        auto required_fields = factor_manager_->get_required_fields();
        for (const auto& field : required_fields) {
            if (!data_interface_->hasField(field)) {
                return {false, "Required field not found: " + field};
            }
        }
    }

    return {true, "Environment validation passed"};
}

void factor_engine::warmUp() {
    log("Warming up factor engine...");

    // 预分配一些内存，预热缓存
    if (!data_interface_->getFieldNames().empty()) {
        auto field_name = data_interface_->getFieldNames()[0];
        auto dims = data_interface_->getDataDimensions(field_name);

        // 创建一些临时数据来预热内存分配器
        feature_operators::DataFrame temp(std::min(100, dims.first), std::min(10, dims.second));
        temp.setRandom();

        verboseLog("Warmed up with " + std::to_string(temp.rows()) + "x" + std::to_string(temp.cols()) + " data");
    }

    log("Engine warm-up completed");
}

void factor_engine::printEngineStatus() const {
    std::cout << "\n=== Factor Engine Status ===" << std::endl;
    std::cout << "Threads: " << num_threads_ << std::endl;
    std::cout << "Performance stats: " << (enable_performance_stats_ ? "enabled" : "disabled") << std::endl;
    std::cout << "Timeout: " << timeout_ms_ << " ms" << std::endl;
    std::cout << "Verbose logging: " << (verbose_logging_ ? "enabled" : "disabled") << std::endl;

    if (factor_manager_) {
        std::cout << "Total factors: " << factor_manager_->getFactorCount() << std::endl;
        std::cout << "Selected factors: " << factor_manager_->getSelectedFactorCount() << std::endl;
    }

    if (data_interface_) {
        std::cout << "Data fields: " << data_interface_->getFieldNames().size() << std::endl;
    }

    auto [valid, error_msg] = validateEnvironment();
    std::cout << "Environment: " << (valid ? "✓ Valid" : "❌ " + error_msg) << std::endl;
}

calculation_result factor_engine::calculateFactorsInternal(const std::vector<int>& factor_ids) {
    calculation_result result;

    if (factor_ids.empty()) {
        result.success = false;
        result.error_message = "No factors to calculate";
        return result;
    }

    auto start_time = std::chrono::high_resolution_clock::now();

    try {
        // 验证环境
        auto [valid, error_msg] = validateEnvironment();
        if (!valid) {
            result.success = false;
            result.error_message = "Environment validation failed: " + error_msg;
            return result;
        }

        verboseLog("Starting calculation of " + std::to_string(factor_ids.size()) + " factors");

        // 重置性能统计
        if (enable_performance_stats_) {
            resetPerformanceStats();
        }

        // 获取数据映射
        const auto& data_map = data_interface_->getAllData();

        // 选择计算策略
        if (num_threads_ == 1 || factor_ids.size() == 1) {
            result = calculateFactorsSingleThread(factor_ids);
        } else {
            result = calculateFactorsMultiThread(factor_ids);
        }

        auto end_time = std::chrono::high_resolution_clock::now();
        result.calculation_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        if (enable_performance_stats_) {
            last_performance_stats_.total_time = result.calculation_time;
            last_performance_stats_.factors_calculated = result.factor_results.size();

            // 计算处理的数据点数量
            if (!result.factor_results.empty()) {
                const auto& first_result = result.factor_results.begin()->second;
                last_performance_stats_.data_points_processed =
                    first_result.rows() * first_result.cols() * result.factor_results.size();
            }
        }

        verboseLog("Calculation completed in " + std::to_string(result.calculation_time.count()) + " ms");

    } catch (const std::exception& e) {
        auto end_time = std::chrono::high_resolution_clock::now();
        result.calculation_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        result.success = false;
        result.error_message = "Calculation failed: " + std::string(e.what());

        log("Calculation failed: " + std::string(e.what()));
    }

    return result;
}

calculation_result factor_engine::calculateFactorsSingleThread(const std::vector<int>& factor_ids) {
    calculation_result result;
    result.success = true;

    const auto& data_map = data_interface_->getAllData();

    for (int factor_id : factor_ids) {
        auto factor = factor_manager_->get_factor(factor_id);
        if (!factor) {
            verboseLog("Factor not found: " + std::to_string(factor_id));
            continue;
        }

        try {
            auto factor_start = std::chrono::high_resolution_clock::now();

            verboseLog("Calculating factor: " + factor->get_name());
            auto factor_result = factor->calculate(data_map);

            auto factor_end = std::chrono::high_resolution_clock::now();
            auto factor_duration = std::chrono::duration_cast<std::chrono::milliseconds>(factor_end - factor_start);

            result.factor_results[factor->get_name()] = std::move(factor_result);

            if (enable_performance_stats_) {
                recordFactorTime(factor->get_name(), factor_duration);
            }

            verboseLog("Factor " + factor->get_name() + " completed in " + std::to_string(factor_duration.count()) + " ms");

        } catch (const std::exception& e) {
            log("Error calculating factor " + factor->get_name() + ": " + e.what());
            // 继续计算其他因子
        }
    }

    return result;
}

calculation_result factor_engine::calculateFactorsMultiThread(const std::vector<int>& factor_ids) {
    // 简化实现：目前使用单线程，多线程实现较复杂
    // 在实际应用中，这里会使用线程池和任务队列
    verboseLog("Multi-thread calculation requested, using single-thread for now");
    return calculateFactorsSingleThread(factor_ids);
}

void factor_engine::recordFactorTime(const std::string& factor_name, std::chrono::milliseconds duration) {
    if (enable_performance_stats_) {
        last_performance_stats_.factor_times[factor_name] = duration;
    }
}

void factor_engine::log(const std::string& message) const {
    std::cout << "[FactorEngine] " << message << std::endl;
}

void factor_engine::verboseLog(const std::string& message) const {
    if (verbose_logging_) {
        std::cout << "[FactorEngine][VERBOSE] " << message << std::endl;
    }
}

} // namespace factor_framework
