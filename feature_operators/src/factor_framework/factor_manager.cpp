#include "factor_framework/factor_manager.hpp"
#include <fstream>
#include <sstream>
#include <iostream>
#include <algorithm>

namespace factor_framework {

FactorManager::FactorManager() {
}

bool FactorManager::loadFactorsFromCSV(const std::string& csv_file_path) {
    try {
        std::ifstream file(csv_file_path);
        if (!file.is_open()) {
            std::cerr << "Error: Cannot open CSV file: " << csv_file_path << std::endl;
            return false;
        }

        std::string line;
        // 跳过标题行
        if (std::getline(file, line)) {
            // 处理标题行
        }

        int loaded_count = 0;
        int line_number = 2;

        while (std::getline(file, line)) {
            if (line.empty()) {
                line_number++;
                continue;
            }

            int factor_id;
            std::string factor_name, formula;

            if (parseCSVLine(line, factor_id, factor_name, formula)) {
                if (addFactor(factor_id, factor_name, formula)) {
                    loaded_count++;
                } else {
                    std::cerr << "Warning: Failed to add factor at line " << line_number
                             << ": " << factor_name << std::endl;
                }
            } else {
                std::cerr << "Warning: Failed to parse line " << line_number
                         << ": " << line << std::endl;
            }

            line_number++;
        }

        std::cout << "Loaded " << loaded_count << " factors from " << csv_file_path << std::endl;
        return loaded_count > 0;

    } catch (const std::exception& e) {
        std::cerr << "Error loading factors from CSV: " << e.what() << std::endl;
        return false;
    }
}

bool FactorManager::addFactor(int factor_id, const std::string& factor_name, const std::string& formula) {
    try {
        // 检查因子ID是否已存在
        if (factors_.find(factor_id) != factors_.end()) {
            std::cerr << "Warning: Factor ID " << factor_id << " already exists" << std::endl;
            return false;
        }

        // 检查因子名称是否已存在
        if (name_to_id_.find(factor_name) != name_to_id_.end()) {
            std::cerr << "Warning: Factor name " << factor_name << " already exists" << std::endl;
            return false;
        }

        // 创建因子实例
        auto& registry = FactorRegistry::getInstance();
        auto factor = registry.createFactor(factor_id, factor_name, formula);

        // 添加到管理器
        factors_[factor_id] = std::move(factor);
        name_to_id_[factor_name] = factor_id;

        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error adding factor " << factor_name << ": " << e.what() << std::endl;
        return false;
    }
}

int FactorManager::loadRegisteredFactors(const std::unordered_map<std::string, std::pair<int, std::string>>& factor_configs) {
    auto& registry = FactorRegistry::getInstance();
    auto registered_factors = registry.getRegisteredFactors();

    int loaded_count = 0;

    for (const auto& factor_name : registered_factors) {
        auto config_it = factor_configs.find(factor_name);
        if (config_it != factor_configs.end()) {
            int factor_id = config_it->second.first;
            const std::string& formula = config_it->second.second;

            if (addFactor(factor_id, factor_name, formula)) {
                loaded_count++;
                std::cout << "✓ Loaded registered factor: " << factor_name << " (ID: " << factor_id << ")" << std::endl;
            } else {
                std::cout << "❌ Failed to load factor: " << factor_name << std::endl;
            }
        } else {
            std::cout << "⚠ No config found for registered factor: " << factor_name << std::endl;
        }
    }

    std::cout << "Loaded " << loaded_count << " registered factors" << std::endl;
    return loaded_count;
}

int FactorManager::loadAllRegisteredFactors() {
    auto& registry = FactorRegistry::getInstance();
    auto registered_factors = registry.getRegisteredFactors();

    int loaded_count = 0;

    for (const auto& factor_name : registered_factors) {
        // 使用默认配置：ID为加载顺序，公式为因子名称
        int factor_id = loaded_count;
        std::string formula = "Auto-registered factor: " + factor_name;

        if (addFactor(factor_id, factor_name, formula)) {
            loaded_count++;
            std::cout << "✓ Auto-loaded factor: " << factor_name << " (ID: " << factor_id << ")" << std::endl;
        } else {
            std::cout << "❌ Failed to auto-load factor: " << factor_name << std::endl;
        }
    }

    std::cout << "Auto-loaded " << loaded_count << " registered factors" << std::endl;
    return loaded_count;
}

bool FactorManager::removeFactor(int factor_id) {
    auto it = factors_.find(factor_id);
    if (it == factors_.end()) {
        return false;
    }

    // 从名称映射中移除
    const std::string& factor_name = it->second->getName();
    name_to_id_.erase(factor_name);

    // 从选中列表中移除
    selected_factor_ids_.erase(factor_id);

    // 移除因子
    factors_.erase(it);

    return true;
}

FactorBase* FactorManager::getFactor(int factor_id) const {
    auto it = factors_.find(factor_id);
    return (it != factors_.end()) ? it->second.get() : nullptr;
}

FactorBase* FactorManager::getFactor(const std::string& factor_name) const {
    auto it = name_to_id_.find(factor_name);
    if (it != name_to_id_.end()) {
        return getFactor(it->second);
    }
    return nullptr;
}

std::vector<int> FactorManager::getAllFactorIds() const {
    std::vector<int> ids;
    ids.reserve(factors_.size());

    for (const auto& [id, factor] : factors_) {
        ids.push_back(id);
    }

    return ids;
}

std::vector<std::string> FactorManager::getAllFactorNames() const {
    std::vector<std::string> names;
    names.reserve(factors_.size());

    for (const auto& [name, id] : name_to_id_) {
        names.push_back(name);
    }

    return names;
}

void FactorManager::selectFactors(const std::vector<int>& factor_ids) {
    selected_factor_ids_.clear();

    for (int id : factor_ids) {
        if (factors_.find(id) != factors_.end()) {
            selected_factor_ids_.insert(id);
        } else {
            std::cerr << "Warning: Factor ID " << id << " not found" << std::endl;
        }
    }
}

void FactorManager::selectFactors(const std::vector<std::string>& factor_names) {
    std::vector<int> factor_ids;
    factor_ids.reserve(factor_names.size());

    for (const auto& name : factor_names) {
        auto it = name_to_id_.find(name);
        if (it != name_to_id_.end()) {
            factor_ids.push_back(it->second);
        } else {
            std::cerr << "Warning: Factor name " << name << " not found" << std::endl;
        }
    }

    selectFactors(factor_ids);
}

std::vector<int> FactorManager::getSelectedFactorIds() const {
    return std::vector<int>(selected_factor_ids_.begin(), selected_factor_ids_.end());
}

void FactorManager::clearSelection() {
    selected_factor_ids_.clear();
}

std::unordered_set<std::string> FactorManager::getRequiredFields() const {
    std::unordered_set<std::string> required_fields;

    for (int factor_id : selected_factor_ids_) {
        auto factor = getFactor(factor_id);
        if (factor) {
            auto factor_fields = factor->getRequiredFields();
            for (const auto& field : factor_fields) {
                required_fields.insert(field);
            }
        }
    }

    return required_fields;
}

std::unordered_map<std::string, feature_operators::DataFrame> FactorManager::calculateSelectedFactors(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const {

    return calculateFactors(getSelectedFactorIds(), data_map);
}

std::unordered_map<std::string, feature_operators::DataFrame> FactorManager::calculateFactors(
    const std::vector<int>& factor_ids,
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const {

    std::unordered_map<std::string, feature_operators::DataFrame> results;

    for (int factor_id : factor_ids) {
        auto factor = getFactor(factor_id);
        if (!factor) {
            std::cerr << "Warning: Factor ID " << factor_id << " not found" << std::endl;
            continue;
        }

        try {
            auto result = factor->calculate(data_map);
            results[factor->getName()] = std::move(result);
        } catch (const std::exception& e) {
            std::cerr << "Error calculating factor " << factor->getName()
                     << ": " << e.what() << std::endl;
        }
    }

    return results;
}

bool FactorManager::validateData(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const {

    auto required_fields = getRequiredFields();

    // 检查所有必需字段是否存在
    for (const auto& field : required_fields) {
        if (data_map.find(field) == data_map.end()) {
            std::cerr << "Error: Required field not found: " << field << std::endl;
            return false;
        }
    }

    // 检查数据维度是否一致
    if (!required_fields.empty()) {
        auto first_field = *required_fields.begin();
        const auto& first_data = data_map.at(first_field);
        int rows = first_data.rows();
        int cols = first_data.cols();

        for (const auto& field : required_fields) {
            const auto& data = data_map.at(field);
            if (data.rows() != rows || data.cols() != cols) {
                std::cerr << "Error: Data dimension mismatch for field " << field
                         << ": expected (" << rows << "x" << cols
                         << "), got (" << data.rows() << "x" << data.cols() << ")" << std::endl;
                return false;
            }
        }
    }

    return true;
}

void FactorManager::printFactorInfo() const {
    std::cout << "\n=== Factor Manager Info ===" << std::endl;
    std::cout << "Total factors: " << factors_.size() << std::endl;
    std::cout << "Selected factors: " << selected_factor_ids_.size() << std::endl;

    std::cout << "\nAll factors:" << std::endl;
    for (const auto& [id, factor] : factors_) {
        std::cout << "  ID: " << id << ", Name: " << factor->getName() << std::endl;
        std::cout << "    Formula: " << factor->getFormula() << std::endl;

        auto required_fields = factor->getRequiredFields();
        std::cout << "    Required fields: ";
        for (size_t i = 0; i < required_fields.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << required_fields[i];
        }
        std::cout << std::endl;
    }
}

void FactorManager::printSelectedFactorInfo() const {
    std::cout << "\n=== Selected Factors ===" << std::endl;
    std::cout << "Count: " << selected_factor_ids_.size() << std::endl;

    for (int factor_id : selected_factor_ids_) {
        auto factor = getFactor(factor_id);
        if (factor) {
            std::cout << "  ID: " << factor_id << ", Name: " << factor->getName() << std::endl;
        }
    }

    auto required_fields = getRequiredFields();
    std::cout << "\nRequired fields for selected factors: ";
    for (const auto& field : required_fields) {
        std::cout << field << " ";
    }
    std::cout << std::endl;
}

bool FactorManager::parseCSVLine(const std::string& line,
                                int& factor_id,
                                std::string& factor_name,
                                std::string& formula) const {

    std::vector<std::string> parts;
    std::string current_part;
    bool in_quotes = false;

    for (char c : line) {
        if (c == '"') {
            in_quotes = !in_quotes;
        } else if (c == ',' && !in_quotes) {
            parts.push_back(cleanString(current_part));
            current_part.clear();
        } else {
            current_part += c;
        }
    }

    if (!current_part.empty()) {
        parts.push_back(cleanString(current_part));
    }

    if (parts.size() < 3) {
        return false;
    }

    try {
        factor_id = std::stoi(parts[0]);
        factor_name = parts[1];
        formula = parts[2];
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

std::string FactorManager::cleanString(const std::string& str) const {
    std::string result = str;

    // 去除前后空格
    result.erase(0, result.find_first_not_of(" \t\r\n"));
    result.erase(result.find_last_not_of(" \t\r\n") + 1);

    // 去除引号
    if (result.length() >= 2 && result.front() == '"' && result.back() == '"') {
        result = result.substr(1, result.length() - 2);
    }

    return result;
}

} // namespace factor_framework
