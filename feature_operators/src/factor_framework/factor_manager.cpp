#include "factor_framework/factor_manager.hpp"
#include <fstream>
#include <sstream>
#include <iostream>
#include <algorithm>

namespace factor_framework {

factor_manager::factor_manager() {
}

bool factor_manager::load_factors_from_csv(const std::string& csv_file_path) {
    try {
        std::ifstream file(csv_file_path);
        if (!file.is_open()) {
            std::cerr << "Error: Cannot open CSV file: " << csv_file_path << std::endl;
            return false;
        }

        std::string line;
        // 跳过标题行
        if (std::getline(file, line)) {
            // 处理标题行
        }

        int loaded_count = 0;
        int line_number = 2;

        while (std::getline(file, line)) {
            if (line.empty()) {
                line_number++;
                continue;
            }

            int factor_id;
            std::string factor_name, formula;

            if (parse_csv_line(line, factor_id, factor_name, formula)) {
                if (add_factor(factor_id, factor_name, formula)) {
                    loaded_count++;
                } else {
                    std::cerr << "Warning: Failed to add factor at line " << line_number
                             << ": " << factor_name << std::endl;
                }
            } else {
                std::cerr << "Warning: Failed to parse line " << line_number
                         << ": " << line << std::endl;
            }

            line_number++;
        }

        std::cout << "Loaded " << loaded_count << " factors from " << csv_file_path << std::endl;
        return loaded_count > 0;

    } catch (const std::exception& e) {
        std::cerr << "Error loading factors from CSV: " << e.what() << std::endl;
        return false;
    }
}

bool factor_manager::add_factor(int factor_id, const std::string& factor_name, const std::string& formula) {
    try {
        // 检查因子ID是否已存在
        if (factors_.find(factor_id) != factors_.end()) {
            std::cerr << "Warning: Factor ID " << factor_id << " already exists" << std::endl;
            return false;
        }

        // 检查因子名称是否已存在
        if (name_to_id_.find(factor_name) != name_to_id_.end()) {
            std::cerr << "Warning: Factor name " << factor_name << " already exists" << std::endl;
            return false;
        }

        // 创建因子实例
        auto& registry = factor_registry::get_instance();
        auto factor = registry.create_factor(factor_id, factor_name, formula);

        // 添加到管理器
        factors_[factor_id] = std::move(factor);
        name_to_id_[factor_name] = factor_id;

        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error adding factor " << factor_name << ": " << e.what() << std::endl;
        return false;
    }
}

bool factor_manager::register_factor(std::unique_ptr<factor_base> factor) {
    if (!factor) {
        std::cerr << "Error: Cannot register null factor" << std::endl;
        return false;
    }

    int factor_id = factor->get_id();
    std::string factor_name = factor->get_name();

    // 检查ID是否已存在
    if (factors_.find(factor_id) != factors_.end()) {
        std::cerr << "Error: Factor ID " << factor_id << " already exists" << std::endl;
        return false;
    }

    // 检查名称是否已存在
    for (const auto& [id, existing_factor] : factors_) {
        if (existing_factor->get_name() == factor_name) {
            std::cerr << "Error: Factor name '" << factor_name << "' already exists" << std::endl;
            return false;
        }
    }

    try {
        factors_[factor_id] = std::move(factor);
        std::cout << "✓ Registered factor: " << factor_name << " (ID: " << factor_id << ")" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error registering factor " << factor_name << ": " << e.what() << std::endl;
        return false;
    }
}

int factor_manager::register_factors(std::vector<std::unique_ptr<factor_base>> factors) {
    int registered_count = 0;

    for (auto& factor : factors) {
        if (register_factor(std::move(factor))) {
            registered_count++;
        }
    }

    std::cout << "Registered " << registered_count << "/" << factors.size() << " factors" << std::endl;
    return registered_count;
}

bool factor_manager::remove_factor(int factor_id) {
    auto it = factors_.find(factor_id);
    if (it == factors_.end()) {
        return false;
    }

    // 从名称映射中移除
    const std::string& factor_name = it->second->get_name();
    name_to_id_.erase(factor_name);

    // 从选中列表中移除
    selected_factor_ids_.erase(factor_id);

    // 移除因子
    factors_.erase(it);

    return true;
}

factor_base* factor_manager::get_factor(int factor_id) const {
    auto it = factors_.find(factor_id);
    return (it != factors_.end()) ? it->second.get() : nullptr;
}

factor_base* factor_manager::get_factor(const std::string& factor_name) const {
    auto it = name_to_id_.find(factor_name);
    if (it != name_to_id_.end()) {
        return get_factor(it->second);
    }
    return nullptr;
}

std::vector<int> factor_manager::get_all_factor_ids() const {
    std::vector<int> ids;
    ids.reserve(factors_.size());

    for (const auto& [id, factor] : factors_) {
        ids.push_back(id);
    }

    return ids;
}

std::vector<std::string> factor_manager::getAllFactorNames() const {
    std::vector<std::string> names;
    names.reserve(factors_.size());

    for (const auto& [name, id] : name_to_id_) {
        names.push_back(name);
    }

    return names;
}

void factor_manager::select_factors(const std::vector<int>& factor_ids) {
    selected_factor_ids_.clear();

    for (int id : factor_ids) {
        if (factors_.find(id) != factors_.end()) {
            selected_factor_ids_.insert(id);
        } else {
            std::cerr << "Warning: Factor ID " << id << " not found" << std::endl;
        }
    }
}

void factor_manager::select_factors(const std::vector<std::string>& factor_names) {
    std::vector<int> factor_ids;
    factor_ids.reserve(factor_names.size());

    for (const auto& name : factor_names) {
        auto it = name_to_id_.find(name);
        if (it != name_to_id_.end()) {
            factor_ids.push_back(it->second);
        } else {
            std::cerr << "Warning: Factor name " << name << " not found" << std::endl;
        }
    }

    select_factors(factor_ids);
}

std::vector<int> factor_manager::get_selected_factor_ids() const {
    return std::vector<int>(selected_factor_ids_.begin(), selected_factor_ids_.end());
}

void factor_manager::clear_selection() {
    selected_factor_ids_.clear();
}

std::unordered_set<std::string> factor_manager::get_required_fields() const {
    std::unordered_set<std::string> required_fields;

    for (int factor_id : selected_factor_ids_) {
        auto factor = get_factor(factor_id);
        if (factor) {
            auto factor_fields = factor->get_required_fields();
            for (const auto& field : factor_fields) {
                required_fields.insert(field);
            }
        }
    }

    return required_fields;
}

std::unordered_map<std::string, feature_operators::DataFrame> factor_manager::calculate_selected_factors(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const {

    return calculate_factors(get_selected_factor_ids(), data_map);
}

std::unordered_map<std::string, feature_operators::DataFrame> factor_manager::calculate_factors(
    const std::vector<int>& factor_ids,
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const {

    std::unordered_map<std::string, feature_operators::DataFrame> results;

    for (int factor_id : factor_ids) {
        auto factor = get_factor(factor_id);
        if (!factor) {
            std::cerr << "Warning: Factor ID " << factor_id << " not found" << std::endl;
            continue;
        }

        try {
            auto result = factor->calculate(data_map);
            results[factor->get_name()] = std::move(result);
        } catch (const std::exception& e) {
            std::cerr << "Error calculating factor " << factor->get_name()
                     << ": " << e.what() << std::endl;
        }
    }

    return results;
}

bool factor_manager::validateData(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const {

    auto required_fields = get_required_fields();

    // 检查所有必需字段是否存在
    for (const auto& field : required_fields) {
        if (data_map.find(field) == data_map.end()) {
            std::cerr << "Error: Required field not found: " << field << std::endl;
            return false;
        }
    }

    // 检查数据维度是否一致
    if (!required_fields.empty()) {
        auto first_field = *required_fields.begin();
        const auto& first_data = data_map.at(first_field);
        int rows = first_data.rows();
        int cols = first_data.cols();

        for (const auto& field : required_fields) {
            const auto& data = data_map.at(field);
            if (data.rows() != rows || data.cols() != cols) {
                std::cerr << "Error: Data dimension mismatch for field " << field
                         << ": expected (" << rows << "x" << cols
                         << "), got (" << data.rows() << "x" << data.cols() << ")" << std::endl;
                return false;
            }
        }
    }

    return true;
}

void factor_manager::print_factor_info() const {
    std::cout << "\n=== Factor Manager Info ===" << std::endl;
    std::cout << "Total factors: " << factors_.size() << std::endl;
    std::cout << "Selected factors: " << selected_factor_ids_.size() << std::endl;

    std::cout << "\nAll factors:" << std::endl;
    for (const auto& [id, factor] : factors_) {
        std::cout << "  ID: " << id << ", Name: " << factor->get_name() << std::endl;
        std::cout << "    Formula: " << factor->get_formula() << std::endl;

        auto required_fields = factor->get_required_fields();
        std::cout << "    Required fields: ";
        for (size_t i = 0; i < required_fields.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << required_fields[i];
        }
        std::cout << std::endl;
    }
}

void factor_manager::print_selected_factor_info() const {
    std::cout << "\n=== Selected Factors ===" << std::endl;
    std::cout << "Count: " << selected_factor_ids_.size() << std::endl;

    for (int factor_id : selected_factor_ids_) {
        auto factor = get_factor(factor_id);
        if (factor) {
            std::cout << "  ID: " << factor_id << ", Name: " << factor->get_name() << std::endl;
        }
    }

    auto required_fields = get_required_fields();
    std::cout << "\nRequired fields for selected factors: ";
    for (const auto& field : required_fields) {
        std::cout << field << " ";
    }
    std::cout << std::endl;
}

bool factor_manager::parse_csv_line(const std::string& line,
                                int& factor_id,
                                std::string& factor_name,
                                std::string& formula) const {

    std::vector<std::string> parts;
    std::string current_part;
    bool in_quotes = false;

    for (char c : line) {
        if (c == '"') {
            in_quotes = !in_quotes;
        } else if (c == ',' && !in_quotes) {
            parts.push_back(clean_string(current_part));
            current_part.clear();
        } else {
            current_part += c;
        }
    }

    if (!current_part.empty()) {
        parts.push_back(clean_string(current_part));
    }

    if (parts.size() < 3) {
        return false;
    }

    try {
        factor_id = std::stoi(parts[0]);
        factor_name = parts[1];
        formula = parts[2];
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

std::string factor_manager::clean_string(const std::string& str) const {
    std::string result = str;

    // 去除前后空格
    result.erase(0, result.find_first_not_of(" \t\r\n"));
    result.erase(result.find_last_not_of(" \t\r\n") + 1);

    // 去除引号
    if (result.length() >= 2 && result.front() == '"' && result.back() == '"') {
        result = result.substr(1, result.length() - 2);
    }

    return result;
}

} // namespace factor_framework
