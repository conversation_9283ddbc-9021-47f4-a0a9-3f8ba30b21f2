#include "factor_framework/factor_base.hpp"
#include <stdexcept>
#include <sstream>

namespace factor_framework {

FactorBase::FactorBase(int factor_id, const std::string& factor_name, const std::string& formula)
    : factor_id_(factor_id), factor_name_(factor_name), formula_(formula) {
}

bool FactorBase::validateInput(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const {
    
    auto required_fields = getRequiredFields();
    
    // 检查所有必需字段是否存在
    for (const auto& field : required_fields) {
        if (!checkFieldExists(data_map, field)) {
            return false;
        }
    }
    
    // 检查数据维度是否一致
    if (!required_fields.empty()) {
        auto first_field = *required_fields.begin();
        const auto& first_data = getField(data_map, first_field);
        int rows = first_data.rows();
        int cols = first_data.cols();
        
        for (const auto& field : required_fields) {
            const auto& data = getField(data_map, field);
            if (data.rows() != rows || data.cols() != cols) {
                return false;
            }
        }
    }
    
    return true;
}

std::string FactorBase::getDescription() const {
    std::ostringstream oss;
    oss << "Factor ID: " << factor_id_ << "\n";
    oss << "Factor Name: " << factor_name_ << "\n";
    oss << "Formula: " << formula_ << "\n";
    
    auto required_fields = getRequiredFields();
    oss << "Required Fields: ";
    for (size_t i = 0; i < required_fields.size(); ++i) {
        if (i > 0) oss << ", ";
        oss << required_fields[i];
    }
    oss << "\n";
    
    return oss.str();
}

bool FactorBase::checkFieldExists(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map,
    const std::string& field_name) const {
    
    return data_map.find(field_name) != data_map.end();
}

const feature_operators::DataFrame& FactorBase::getField(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map,
    const std::string& field_name) const {
    
    auto it = data_map.find(field_name);
    if (it == data_map.end()) {
        throw std::runtime_error("Required field not found: " + field_name);
    }
    
    return it->second;
}

// FactorRegistry implementation
FactorRegistry& FactorRegistry::getInstance() {
    static FactorRegistry instance;
    return instance;
}

void FactorRegistry::registerFactory(const std::string& factor_name, 
                                    std::unique_ptr<FactorFactory> factory) {
    factories_[factor_name] = std::move(factory);
}

std::unique_ptr<FactorBase> FactorRegistry::createFactor(
    int factor_id,
    const std::string& factor_name,
    const std::string& formula) {
    
    auto it = factories_.find(factor_name);
    if (it == factories_.end()) {
        throw std::runtime_error("Factor not registered: " + factor_name);
    }
    
    return it->second->createFactor(factor_id, factor_name, formula);
}

std::vector<std::string> FactorRegistry::getRegisteredFactors() const {
    std::vector<std::string> factor_names;
    factor_names.reserve(factories_.size());
    
    for (const auto& [name, factory] : factories_) {
        factor_names.push_back(name);
    }
    
    return factor_names;
}

} // namespace factor_framework
