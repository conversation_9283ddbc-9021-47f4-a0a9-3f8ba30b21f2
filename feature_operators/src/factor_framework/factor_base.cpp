#include "factor_framework/factor_base.hpp"
#include <stdexcept>
#include <sstream>

namespace factor_framework {

factor_base::factor_base(int factor_id, const std::string& factor_name, const std::string& formula)
    : factor_id_(factor_id), factor_name_(factor_name), formula_(formula) {
}

bool factor_base::validate_input(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const {

    auto required_fields = get_required_fields();

    // 检查所有必需字段是否存在
    for (const auto& field : required_fields) {
        if (!check_field_exists(data_map, field)) {
            return false;
        }
    }

    // 检查数据维度是否一致
    if (!required_fields.empty()) {
        auto first_field = *required_fields.begin();
        const auto& first_data = get_field(data_map, first_field);
        int rows = first_data.rows();
        int cols = first_data.cols();

        for (const auto& field : required_fields) {
            const auto& data = get_field(data_map, field);
            if (data.rows() != rows || data.cols() != cols) {
                return false;
            }
        }
    }

    return true;
}

std::string factor_base::get_description() const {
    std::ostringstream oss;
    oss << "Factor ID: " << factor_id_ << "\n";
    oss << "Factor Name: " << factor_name_ << "\n";
    oss << "Formula: " << formula_ << "\n";

    auto required_fields = get_required_fields();
    oss << "Required Fields: ";
    for (size_t i = 0; i < required_fields.size(); ++i) {
        if (i > 0) oss << ", ";
        oss << required_fields[i];
    }
    oss << "\n";

    return oss.str();
}

bool factor_base::check_field_exists(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map,
    const std::string& field_name) const {

    return data_map.find(field_name) != data_map.end();
}

const feature_operators::DataFrame& factor_base::get_field(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map,
    const std::string& field_name) const {

    auto it = data_map.find(field_name);
    if (it == data_map.end()) {
        throw std::runtime_error("Required field not found: " + field_name);
    }

    return it->second;
}

// factor_registry implementation
factor_registry& factor_registry::get_instance() {
    static factor_registry instance;
    return instance;
}

void factor_registry::register_factory(const std::string& factor_name,
                                    std::unique_ptr<factor_factory> factory) {
    factories_[factor_name] = std::move(factory);
}

std::unique_ptr<factor_base> factor_registry::create_factor(
    int factor_id,
    const std::string& factor_name,
    const std::string& formula) {

    auto it = factories_.find(factor_name);
    if (it == factories_.end()) {
        throw std::runtime_error("Factor not registered: " + factor_name);
    }

    return it->second->create_factor(factor_id, factor_name, formula);
}

std::vector<std::string> factor_registry::get_registered_factors() const {
    std::vector<std::string> factor_names;
    factor_names.reserve(factories_.size());

    for (const auto& [name, factory] : factories_) {
        factor_names.push_back(name);
    }

    return factor_names;
}

// 全局因子管理器
std::shared_ptr<factor_manager> g_global_factor_manager = nullptr;

// 因子注册列表
std::vector<factor_creator_function>& get_factor_registration_list() {
    static std::vector<factor_creator_function> registration_list;
    return registration_list;
}

} // namespace factor_framework
