#include "generated_factors/all_factors.hpp"

namespace generated_factors {

std::vector<std::string> getAllGeneratedFactorNames() {
    return {"p1_corrs0", "p1_corrs1", "p1_corrs2", "p1_corrs3", "p1_corrs4"};
}

std::vector<int> getAllGeneratedFactorIds() {
    return {0, 1, 2, 3, 4};
}

void initializeAllFactors() {
    // 因子注册在各自的实现文件中通过REGISTER_FACTOR宏自动完成
    // 这个函数确保所有因子类被链接器包含
    static bool initialized = false;
    if (!initialized) {
        // 强制链接所有因子类
        auto& registry = factor_framework::FactorRegistry::getInstance();
        auto registered_factors = registry.getRegisteredFactors();

        // 验证所有因子都已注册
        auto expected_factors = getAllGeneratedFactorNames();
        for (const auto& factor_name : expected_factors) {
            bool found = std::find(registered_factors.begin(), registered_factors.end(), factor_name)
                        != registered_factors.end();
            if (!found) {
                throw std::runtime_error("Factor not registered: " + factor_name);
            }
        }

        initialized = true;
    }
}

} // namespace generated_factors