#include "generated_factors/factorp1corrs0.hpp"
#include <stdexcept>

namespace generated_factors {

FactorP1corrs0::FactorP1corrs0(int factor_id, const std::string& factor_name, const std::string& formula)
    : factor_base(factor_id, factor_name, formula) {
}

feature_operators::DataFrame FactorP1corrs0::calculate(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) {

    // 验证输入数据
    if (!validate_input(data_map)) {
        throw std::runtime_error("Invalid input data for factor " + get_name());
    }

    try {
        // 执行因子计算
        auto result = feature_operators::ts_Corr(get_field(data_map, "Close"),get_field(data_map, "Volume"),60);
        return result;
    } catch (const std::exception& e) {
        throw std::runtime_error("Error calculating factor " + get_name() + ": " + e.what());
    }
}

std::vector<std::string> FactorP1corrs0::get_required_fields() const {
    return {"Volume", "Close"};
}

} // namespace generated_factors

// 注册因子
REGISTER_FACTOR(generated_factors::FactorP1corrs0, "p1_corrs0")