[{"id": 0, "name": "p1_corrs0", "formula": "ts_Corr(Close,Volume,60)", "class_name": "FactorP1corrs0", "required_fields": ["Close", "Volume"]}, {"id": 1, "name": "p1_corrs1", "formula": "ts_<PERSON>rr(Close/ts_Delay(Close,1)-1,Volume,60)", "class_name": "FactorP1corrs1", "required_fields": ["Close", "Volume"]}, {"id": 2, "name": "p1_corrs2", "formula": "ts_<PERSON>rr(ts_<PERSON>ay(Close,1),Volume,60)", "class_name": "FactorP1corrs2", "required_fields": ["Close", "Volume"]}, {"id": 3, "name": "p1_corrs3", "formula": "ts_<PERSON><PERSON>(Close,ts_Delay(Volume,1),60)", "class_name": "FactorP1corrs3", "required_fields": ["Close", "Volume"]}, {"id": 4, "name": "p1_corrs4", "formula": "ts_<PERSON><PERSON>(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)", "class_name": "FactorP1corrs4", "required_fields": ["Close"]}]