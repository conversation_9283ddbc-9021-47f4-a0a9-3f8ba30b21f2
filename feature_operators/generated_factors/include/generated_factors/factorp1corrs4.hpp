#ifndef GENERATED_FACTORS_FACTORP1CORRS4_HPP
#define GENERATED_FACTORS_FACTORP1CORRS4_HPP

#include "factor_framework/factor_base.hpp"
#include "feature_operators.hpp"

namespace generated_factors {

/**
 * p1_corrs4 因子
 * ID: 4
 * 公式: ts_Corr(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)
 */
class FactorP1corrs4 : public factor_framework::FactorBase {
public:
    /**
     * 构造函数
     */
    FactorP1corrs4(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 计算因子值
     */
    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override;

    /**
     * 获取所需数据字段
     */
    std::vector<std::string> get_required_fields() const override;
};

} // namespace generated_factors

#endif // GENERATED_FACTORS_FACTORP1CORRS4_HPP