# 🏗️ 因子框架架构指南

## 📋 目录
1. [架构概述](#架构概述)
2. [核心组件](#核心组件)
3. [设计原则](#设计原则)
4. [使用指南](#使用指南)
5. [代码生成](#代码生成)
6. [性能优化](#性能优化)
7. [扩展开发](#扩展开发)

## 🎯 架构概述

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    因子框架 (Factor Framework)                │
├─────────────────────────────────────────────────────────────┤
│  用户层 (User Layer)                                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Python脚本    │  │   C++应用程序   │  │   配置文件   │ │
│  │ (代码生成器)    │  │  (因子计算)     │  │  (CSV配置)   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  应用层 (Application Layer)                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  factor_engine  │  │ factor_manager  │  │ 生成的因子   │ │
│  │   (计算引擎)    │  │  (因子管理)     │  │  (自动生成)  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  核心层 (Core Layer)                                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   factor_base   │  │ data_interface  │  │ 工具函数库   │ │
│  │   (因子基类)    │  │  (数据接口)     │  │ (utilities)  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  基础层 (Foundation Layer)                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │feature_operators│  │     Eigen3      │  │   标准库     │ │
│  │  (数学运算库)   │  │   (矩阵运算)    │  │  (STL/C++)   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 设计哲学
- **简洁性**: 用户只需关注因子选择和计算，无需处理复杂的注册和管理
- **自动化**: 从CSV配置到C++代码全自动生成，无需手动编写因子代码
- **高性能**: 基于Eigen3的高效矩阵运算，支持并行计算
- **可扩展**: 模块化设计，易于添加新的操作符和因子类型
- **类型安全**: 强类型检查，编译时错误检测

## 🧩 核心组件

### 1. factor_base (因子基类)
```cpp
class factor_base {
public:
    // 构造函数
    factor_base(int factor_id, const std::string& factor_name, const std::string& formula);

    // 核心接口
    virtual feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) = 0;

    // 属性访问
    int get_id() const;
    const std::string& get_name() const;
    const std::string& get_formula() const;
    virtual std::vector<std::string> get_required_fields() const = 0;

    // 数据验证
    virtual bool validate_input(const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const;
};
```

**职责**:
- 定义所有因子的统一接口
- 提供基础的属性管理和数据验证
- 确保类型安全和一致性

### 2. factor_manager (因子管理器)
```cpp
class factor_manager {
public:
    // 因子注册
    bool register_factor(std::unique_ptr<factor_base> factor);
    int register_factors(std::vector<std::unique_ptr<factor_base>> factors);

    // 因子选择
    void select_factors(const std::vector<int>& factor_ids);
    void select_factors(const std::vector<std::string>& factor_names);
    void clear_selection();

    // 因子访问
    std::shared_ptr<factor_base> get_factor(int factor_id) const;
    std::shared_ptr<factor_base> get_factor(const std::string& factor_name) const;
    std::vector<std::shared_ptr<factor_base>> get_selected_factors() const;

    // 信息查询
    size_t get_factor_count() const;
    size_t get_selected_factor_count() const;
    std::vector<std::string> get_required_fields() const;
};
```

**职责**:
- 管理所有注册的因子实例
- 提供因子选择和查询功能
- 维护因子的生命周期

### 3. data_interface (数据接口)
```cpp
class data_interface {
public:
    // 数据加载
    bool load_data_from_csv(const std::string& field_name, const std::string& file_path);
    void create_standard_market_data(int rows, int cols, double base_price = 100.0);

    // 数据访问
    const std::unordered_map<std::string, feature_operators::DataFrame>& get_all_data() const;
    feature_operators::DataFrame get_field_data(const std::string& field_name) const;

    // 数据验证
    bool has_field(const std::string& field_name) const;
    bool validate_data_dimensions() const;
    std::vector<std::string> get_field_names() const;

    // 数据信息
    struct data_info {
        int rows, cols;
        int valid_count, nan_count;
    };
    std::unordered_map<std::string, data_info> get_data_info() const;
};
```

**职责**:
- 统一管理所有市场数据
- 提供数据加载和验证功能
- 确保数据一致性和完整性

### 4. factor_engine (计算引擎)
```cpp
class factor_engine {
public:
    // 构造函数
    factor_engine(std::shared_ptr<factor_manager> factor_manager,
                  std::shared_ptr<data_interface> data_interface);

    // 计算接口
    calculation_result calculate_selected_factors();
    calculation_result calculate_factors(const std::vector<int>& factor_ids);
    calculation_result calculate_single_factor(int factor_id);

    // 配置管理
    void set_num_threads(int num_threads);
    void enable_performance_stats(bool enable);
    void set_timeout(int timeout_ms);

    // 环境管理
    std::pair<bool, std::string> validate_environment() const;
    void warm_up();
};
```

**职责**:
- 执行因子计算任务
- 管理并行计算和性能优化
- 提供计算环境验证和监控

## 🎨 设计原则

### 1. 单一职责原则 (SRP)
每个类都有明确的单一职责：
- `factor_base`: 定义因子接口
- `factor_manager`: 管理因子实例
- `data_interface`: 管理数据
- `factor_engine`: 执行计算

### 2. 开闭原则 (OCP)
- 对扩展开放：可以轻松添加新的因子类型和操作符
- 对修改封闭：核心框架代码无需修改即可支持新功能

### 3. 依赖倒置原则 (DIP)
- 高层模块不依赖低层模块，都依赖抽象
- `factor_engine` 依赖 `factor_base` 抽象，而不是具体实现

### 4. 接口隔离原则 (ISP)
- 提供最小化的接口
- 用户只需要了解必要的接口，无需关心内部实现

## 📖 使用指南

### 基本使用流程

#### 1. 初始化框架
```cpp
#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"

// 初始化框架
FACTOR_FRAMEWORK_INIT();
generated_factors::initialize_all_factors();
```

#### 2. 创建核心组件
```cpp
// 创建数据接口
auto data_interface = std::make_shared<factor_framework::data_interface>();
data_interface->create_standard_market_data(100, 50);  // 100天，50只股票

// 创建因子管理器
auto factor_manager = std::make_shared<factor_framework::factor_manager>();

// 批量注册生成的因子
generated_factors::register_all_factors_to_manager(factor_manager);
```

#### 3. 选择和计算因子
```cpp
// 创建计算引擎
factor_framework::factor_engine engine(factor_manager, data_interface);

// 选择要计算的因子
factor_manager->select_factors({0, 1, 2});  // 按ID选择
// 或者
factor_manager->select_factors({"p1_corrs0", "p1_corrs1"});  // 按名称选择

// 执行计算
auto result = engine.calculate_selected_factors();

// 处理结果
if (result.success) {
    for (const auto& [factor_name, data] : result.factor_results) {
        std::cout << "因子 " << factor_name << " 计算完成" << std::endl;
        std::cout << "维度: " << data.rows() << "x" << data.cols() << std::endl;
    }
}
```

#### 4. 清理资源
```cpp
FACTOR_FRAMEWORK_CLEANUP();
```

### 高级用法

#### 性能优化
```cpp
// 设置并行线程数
engine.set_num_threads(8);

// 启用性能统计
engine.enable_performance_stats(true);

// 预热引擎
engine.warm_up();

// 设置超时时间
engine.set_timeout(30000);  // 30秒
```

#### 环境验证
```cpp
// 验证计算环境
auto [valid, error_msg] = engine.validate_environment();
if (!valid) {
    std::cerr << "环境验证失败: " << error_msg << std::endl;
    return 1;
}
```

#### 性能监控
```cpp
// 获取性能统计
const auto& stats = engine.get_last_performance_stats();
std::cout << "总计算时间: " << stats.total_time.count() << " ms" << std::endl;
std::cout << "计算因子数量: " << stats.factors_calculated << std::endl;
std::cout << "处理数据点: " << stats.data_points_processed << std::endl;
```

## 🔧 代码生成

### 自动化代码生成流程

#### 1. CSV配置文件
```csv
factor_id,factor_name,formula,description
0,p1_corrs0,ts_Corr(Close,Volume,60),价格与成交量60日相关性
1,p1_corrs1,ts_Corr(Close/ts_Delay(Close,1)-1,Volume,60),收益率与成交量60日相关性
2,p1_corrs2,ts_Corr(ts_Delay(Close,1),Volume,60),滞后价格与成交量60日相关性
```

#### 2. 代码生成命令
```bash
cd scripts
python3 factor_code_generator.py \
    --input ../feature.csv \
    --output_dir ../generated_factors_full \
    --max_factors 10 \
    --verbose
```

#### 3. 生成的文件结构
```
generated_factors_full/
├── include/generated_factors/
│   ├── factorp1corrs0.hpp          # 因子头文件
│   ├── factorp1corrs1.hpp
│   ├── factorp1corrs2.hpp
│   └── all_factors.hpp             # 统一头文件
├── src/
│   ├── factorp1corrs0.cpp          # 因子实现文件
│   ├── factorp1corrs1.cpp
│   ├── factorp1corrs2.cpp
│   └── all_factors.cpp             # 统一实现文件
├── CMakeLists.txt                  # 构建配置
├── factor_info.json               # 因子元数据
└── README.md                       # 使用说明
```

#### 4. 自动注册机制
生成的因子会自动注册到框架中：
```cpp
// 在生成的代码中自动包含
REGISTER_FACTOR_SIMPLE(FactorP1corrs0, 0, "p1_corrs0", "ts_Corr(Close,Volume,60)")

// 用户只需调用
generated_factors::register_all_factors_to_manager(factor_manager);
```

### 代码生成器特性

#### 智能公式解析
- 自动识别数学表达式中的操作符
- 智能提取所需的数据字段
- 生成优化的C++计算代码

#### 依赖分析
- 自动分析因子对数据字段的依赖
- 生成 `get_required_fields()` 方法
- 确保数据完整性

#### 错误处理
- 生成完整的异常处理代码
- 提供详细的错误信息
- 支持计算失败恢复

## ⚡ 性能优化

### 计算性能
- **Eigen3优化**: 使用高度优化的矩阵运算库
- **并行计算**: 支持多线程并行因子计算
- **内存优化**: 智能内存管理，减少不必要的拷贝
- **缓存友好**: 优化数据访问模式

### 编译优化
- **模板特化**: 针对常用操作进行模板特化
- **内联函数**: 关键路径使用内联优化
- **编译时计算**: 尽可能在编译时完成计算

### 运行时优化
- **预热机制**: 预分配内存，预热缓存
- **批量计算**: 支持批量因子计算
- **懒加载**: 按需加载数据和因子

## 🔧 扩展开发

### 添加新的操作符
1. 在 `feature_operators` 库中实现新操作符
2. 更新代码生成器的操作符映射
3. 添加相应的测试用例

### 添加新的数据源
1. 继承 `data_interface` 或扩展现有实现
2. 实现特定的数据加载逻辑
3. 确保数据格式一致性

### 自定义因子类型
1. 继承 `factor_base` 基类
2. 实现必要的虚函数
3. 注册到 `factor_manager`

### 性能监控扩展
1. 扩展 `performance_stats` 结构
2. 添加新的性能指标
3. 实现自定义监控逻辑

## 📊 最佳实践

### 代码组织
- 保持因子类的简洁性
- 使用有意义的命名
- 添加充分的文档注释

### 性能考虑
- 避免不必要的数据拷贝
- 合理使用并行计算
- 监控内存使用情况

### 错误处理
- 提供详细的错误信息
- 实现优雅的错误恢复
- 记录关键操作日志

### 测试策略
- 编写单元测试验证因子正确性
- 进行性能基准测试
- 测试边界条件和异常情况

## 🚀 快速开始

### 完整示例程序
```cpp
#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"

int main() {
    try {
        // 1. 初始化框架
        FACTOR_FRAMEWORK_INIT();
        generated_factors::initialize_all_factors();

        // 2. 创建核心组件
        auto data_interface = std::make_shared<factor_framework::data_interface>();
        auto factor_manager = std::make_shared<factor_framework::factor_manager>();

        // 3. 加载数据和注册因子
        data_interface->create_standard_market_data(100, 50);
        generated_factors::register_all_factors_to_manager(factor_manager);

        // 4. 选择和计算因子
        factor_manager->select_factors({0, 1, 2});

        // 5. 获取结果
        auto selected_factors = factor_manager->get_selected_factors();
        for (const auto& factor : selected_factors) {
            std::cout << "因子: " << factor->get_name() << std::endl;
        }

        // 6. 清理
        FACTOR_FRAMEWORK_CLEANUP();
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
}
```

### 编译和运行
```bash
# 生成因子代码
cd scripts
python3 factor_code_generator.py --input ../feature.csv --output_dir ../generated_factors

# 编译项目
cd ../build
cmake ..
make

# 运行演示
./architecture_demo
```

## 📋 项目文件结构

```
feature_operators/
├── include/                          # 头文件目录
│   ├── factor_framework/             # 框架核心头文件
│   │   ├── factor_base.hpp          # 因子基类
│   │   ├── factor_manager.hpp       # 因子管理器
│   │   ├── data_interface.hpp       # 数据接口
│   │   ├── factor_engine.hpp        # 计算引擎
│   │   └── factor_framework.hpp     # 统一头文件
│   └── feature_operators/           # 数学运算库
├── src/                             # 源文件目录
│   └── factor_framework/            # 框架实现
├── scripts/                         # 工具脚本
│   ├── factor_code_generator.py     # 代码生成器
│   └── refactor_to_snake_case.py    # 重构工具
├── generated_factors/               # 生成的因子代码
├── examples/                        # 示例程序
│   ├── architecture_demo.cpp        # 架构演示
│   └── snake_case_demo.cpp         # 命名规范演示
├── feature.csv                     # 因子配置文件
├── ARCHITECTURE_GUIDE.md           # 架构指南 (本文档)
└── SNAKE_CASE_REFACTOR_REPORT.md   # 重构报告
```

## 🎯 总结

这个架构设计实现了您的所有需求：
- ✅ **通用因子接口**: `factor_base` 统一管理所有feature
- ✅ **选择性计算**: `factor_manager` 支持选择部分feature计算
- ✅ **自动化生成**: Python脚本自动生成C++因子代码
- ✅ **自动注册**: 因子自动注册，用户只需选择和计算
- ✅ **现代化设计**: snake_case命名，符合C++最佳实践
- ✅ **高性能**: 基于Eigen3优化，支持并行计算
- ✅ **易扩展**: 模块化设计，易于添加新功能
- ✅ **易维护**: 清晰的架构分层和职责分离

### 核心价值
1. **简化开发**: 从CSV配置到可用因子，全自动化流程
2. **提升性能**: 高效的矩阵运算和并行计算能力
3. **降低门槛**: 用户只需关注因子逻辑，无需处理技术细节
4. **保证质量**: 强类型检查和完善的错误处理机制
5. **支持扩展**: 灵活的架构设计支持各种扩展需求

通过这个架构，您可以轻松管理和计算大量的金融因子，同时保持代码的简洁性和高性能。无论是量化研究、生产环境还是教学实验，这个框架都能提供强大的支持。
