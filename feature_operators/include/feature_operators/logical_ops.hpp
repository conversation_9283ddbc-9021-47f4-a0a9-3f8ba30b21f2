// #ifndef FEATURE_OPERATORS_LOGICAL_OPS_HPP
// #define FEATURE_OPERATORS_LOGICAL_OPS_HPP

// #include "feature_operators/types.hpp" // Added
// #include <Eigen/Dense> // Kept for other Eigen types

// namespace feature_operators {

// // if_then_else_val
// DataFrame IfThen(const DataFrame& condition_data, const DataFrame& then_val, const DataFrame& else_val);
// DataFrame IfThen(const DataFrame& condition_data, double then_scalar, const DataFrame& else_val);
// DataFrame IfThen(const DataFrame& condition_data, const DataFrame& then_val, double else_scalar);
// DataFrame IfThen(const DataFrame& condition_data, double then_scalar, double else_scalar);

// // and_val
// DataFrame And(const DataFrame& a, const DataFrame& b);
// DataFrame And(const DataFrame& a, double b_scalar);

// // or_val
// DataFrame Or(const DataFrame& a, const DataFrame& b);
// DataFrame Or(const DataFrame& a, double b_scalar);

// // not_val
// DataFrame Not(const DataFrame& a);

// // xor_val
// DataFrame Xor(const DataFrame& a, const DataFrame& b);
// DataFrame Xor(const DataFrame& a, double b_scalar);

// } // namespace feature_operators
// #endif // FEATURE_OPERATORS_LOGICAL_OPS_HPP

// #include "feature_operators/logical_ops.hpp" // This file (potentially for declarations if used elsewhere)
#include "feature_operators/types.hpp"     // Defines DataFrame, or this file now provides generic Eigen ops
#include <Eigen/Dense>                     // For Eigen::ArrayBase and core Eigen functionalities
#include <limits>                          // For std::numeric_limits
#include <cmath>                           // For std::isnan, std::isinf (if needed)
#include <cassert>                         // For assert
#include <type_traits>                     // For std::is_floating_point

namespace feature_operators {

// Note: The original `const double NaN` global variable is generally avoided in templated
// code. Instead, std::numeric_limits<Scalar>::quiet_NaN() is used directly.

// Helper to create a constant array. This replaces the specific scalar_to_array for DataFrame.
// It's generally better to handle scalar arguments directly in functions using Eigen's broadcasting.
// However, if an explicit array is needed from a scalar with 'like' dimensions:
template<typename Derived>
auto create_constant_array_like(typename Derived::Scalar scalar_val, const Eigen::ArrayBase<Derived>& like) {
    using Scalar = typename Derived::Scalar;
    // Ensure we are working with floating point types if NaN logic is critical
    static_assert(std::is_floating_point<Scalar>::value,
                  "This function is intended for floating-point scalar types due to NaN handling.");
    return Eigen::Array<Scalar, Derived::RowsAtCompileTime, Derived::ColsAtCompileTime, Derived::Options>::Constant(
        like.rows(),
        like.cols(),
        scalar_val
    );
}


// if_then_else: condition > 0 ? then_val : (condition < 0 ? else_val : NaN_val)
template<typename DerivedCond, typename DerivedThen, typename DerivedElse>
auto IfThen(const Eigen::ArrayBase<DerivedCond>& condition_data,
            const Eigen::ArrayBase<DerivedThen>& then_val,
            const Eigen::ArrayBase<DerivedElse>& else_val) {
    // Assertions for dimensions
    assert(condition_data.rows() == then_val.rows() && "IfThen: condition_data and then_val row counts differ.");
    assert(condition_data.cols() == then_val.cols() && "IfThen: condition_data and then_val column counts differ.");
    assert(condition_data.rows() == else_val.rows() && "IfThen: condition_data and else_val row counts differ.");
    assert(condition_data.cols() == else_val.cols() && "IfThen: condition_data and else_val column counts differ.");

    using Scalar = typename DerivedCond::Scalar;
    static_assert(std::is_floating_point<Scalar>::value,
                  "IfThen condition_data must have a floating-point scalar type.");
    static_assert(std::is_same<Scalar, typename DerivedThen::Scalar>::value,
                  "IfThen: then_val must have the same scalar type as condition_data.");
    static_assert(std::is_same<Scalar, typename DerivedElse::Scalar>::value,
                  "IfThen: else_val must have the same scalar type as condition_data.");

    auto cond_is_nan = condition_data.isNaN();
    auto select_then_branch = (condition_data > Scalar(0)) && !cond_is_nan;
    auto select_else_branch = (condition_data < Scalar(0)) && !cond_is_nan;

    Scalar nan_scalar_fill = std::numeric_limits<Scalar>::quiet_NaN();
    auto nan_fill_expr = Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        condition_data.rows(), condition_data.cols(), nan_scalar_fill
    );

    return select_then_branch.select(then_val,
                                     select_else_branch.select(else_val, nan_fill_expr));
}

// Overload for: IfThen(condition_data, scalar then_val, array else_val)
template<typename DerivedCond, typename DerivedElse>
auto IfThen(const Eigen::ArrayBase<DerivedCond>& condition_data,
            typename DerivedCond::Scalar then_scalar,
            const Eigen::ArrayBase<DerivedElse>& else_val) {
    assert(condition_data.rows() == else_val.rows() && "IfThen: condition_data and else_val row counts differ.");
    assert(condition_data.cols() == else_val.cols() && "IfThen: condition_data and else_val column counts differ.");
    using Scalar = typename DerivedCond::Scalar;
    static_assert(std::is_floating_point<Scalar>::value, "IfThen condition_data must have a floating-point scalar type.");
    static_assert(std::is_same<Scalar, typename DerivedElse::Scalar>::value, "IfThen: else_val must have the same scalar type as condition_data.");

    auto cond_is_nan = condition_data.isNaN();
    auto select_then_branch = (condition_data > Scalar(0)) && !cond_is_nan;
    auto select_else_branch = (condition_data < Scalar(0)) && !cond_is_nan;

    Scalar nan_scalar_fill = std::numeric_limits<Scalar>::quiet_NaN();
    auto nan_fill_expr = Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        condition_data.rows(), condition_data.cols(), nan_scalar_fill
    );
    auto then_scalar_expr = Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        condition_data.rows(), condition_data.cols(), then_scalar
    );

    return select_then_branch.select(then_scalar_expr,
                                     select_else_branch.select(else_val, nan_fill_expr));
}

// Overload for: IfThen(condition_data, array then_val, scalar else_val)
template<typename DerivedCond, typename DerivedThen>
auto IfThen(const Eigen::ArrayBase<DerivedCond>& condition_data,
            const Eigen::ArrayBase<DerivedThen>& then_val,
            typename DerivedCond::Scalar else_scalar) {
    assert(condition_data.rows() == then_val.rows() && "IfThen: condition_data and then_val row counts differ.");
    assert(condition_data.cols() == then_val.cols() && "IfThen: condition_data and then_val column counts differ.");
    using Scalar = typename DerivedCond::Scalar;
    static_assert(std::is_floating_point<Scalar>::value, "IfThen condition_data must have a floating-point scalar type.");
    static_assert(std::is_same<Scalar, typename DerivedThen::Scalar>::value, "IfThen: then_val must have the same scalar type as condition_data.");

    auto cond_is_nan = condition_data.isNaN();
    auto select_then_branch = (condition_data > Scalar(0)) && !cond_is_nan;
    auto select_else_branch = (condition_data < Scalar(0)) && !cond_is_nan;

    Scalar nan_scalar_fill = std::numeric_limits<Scalar>::quiet_NaN();
    auto nan_fill_expr = Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        condition_data.rows(), condition_data.cols(), nan_scalar_fill
    );
    auto else_scalar_expr = Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        condition_data.rows(), condition_data.cols(), else_scalar
    );

    return select_then_branch.select(then_val,
                                     select_else_branch.select(else_scalar_expr, nan_fill_expr));
}

// Overload for: IfThen(condition_data, scalar then_val, scalar else_val)
template<typename DerivedCond>
auto IfThen(const Eigen::ArrayBase<DerivedCond>& condition_data,
            typename DerivedCond::Scalar then_scalar,
            typename DerivedCond::Scalar else_scalar) {
    using Scalar = typename DerivedCond::Scalar;
    static_assert(std::is_floating_point<Scalar>::value, "IfThen condition_data must have a floating-point scalar type.");

    auto cond_is_nan = condition_data.isNaN();
    auto select_then_branch = (condition_data > Scalar(0)) && !cond_is_nan;
    auto select_else_branch = (condition_data < Scalar(0)) && !cond_is_nan;

    Scalar nan_scalar_fill = std::numeric_limits<Scalar>::quiet_NaN();
    auto nan_fill_expr = Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        condition_data.rows(), condition_data.cols(), nan_scalar_fill
    );
    auto then_scalar_expr = Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        condition_data.rows(), condition_data.cols(), then_scalar
    );
    auto else_scalar_expr = Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        condition_data.rows(), condition_data.cols(), else_scalar
    );

    return select_then_branch.select(then_scalar_expr,
                                     select_else_branch.select(else_scalar_expr, nan_fill_expr));
}


// Common logic for binary logical operators (And, Or, Xor)
// Returns 1.0 if condition met and inputs not NaN, 0.0 if inputs NaN or condition not met.
template<typename DerivedA, typename DerivedB, typename BooleanConditionExpr>
auto apply_logical_binary_op(const Eigen::ArrayBase<DerivedA>& a,
                             const Eigen::ArrayBase<DerivedB>& b,
                             const BooleanConditionExpr& main_condition) {
    assert(a.rows() == b.rows() && "Binary logical op: a and b row counts differ.");
    assert(a.cols() == b.cols() && "Binary logical op: a and b column counts differ.");
    using Scalar = typename DerivedA::Scalar;
    static_assert(std::is_floating_point<Scalar>::value, "Logical operations require floating-point scalar types for NaN handling and 0.0/1.0 results.");
    static_assert(std::is_same<Scalar, typename DerivedB::Scalar>::value, "Binary logical op: a and b must have the same scalar type.");

    auto valid_inputs_mask = !a.isNaN() && !b.isNaN();
    Scalar zero_fill = Scalar(0);
    auto zero_fill_expr = Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        a.rows(), a.cols(), zero_fill
    );

    return valid_inputs_mask.select(main_condition.template cast<Scalar>(), zero_fill_expr);
}

// Overload for binary logical operators with one scalar argument
template<typename DerivedA, typename BooleanConditionFunctor>
auto apply_logical_binary_op_scalar_rhs(const Eigen::ArrayBase<DerivedA>& a,
                                        typename DerivedA::Scalar b_scalar,
                                        BooleanConditionFunctor get_main_condition) {
    using Scalar = typename DerivedA::Scalar;
    static_assert(std::is_floating_point<Scalar>::value, "Logical operations require floating-point scalar types for NaN handling and 0.0/1.0 results.");

    using BoolArrayType = Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic>;

    auto a_is_not_nan = !a.isNaN();
    bool b_scalar_is_not_nan_val = !std::isnan(b_scalar);
    auto b_scalar_is_not_nan_arr = BoolArrayType::Constant(a.rows(), a.cols(), b_scalar_is_not_nan_val);
    auto valid_inputs_mask = a_is_not_nan && b_scalar_is_not_nan_arr;

    Scalar zero_fill = Scalar(0);
    auto zero_fill_expr = Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        a.rows(), a.cols(), zero_fill
    );
    auto main_condition_result = get_main_condition(a, b_scalar);

    return valid_inputs_mask.select(main_condition_result.template cast<Scalar>(), zero_fill_expr);
}


// And: Result is 0.0 if a or b is NaN. Otherwise, (a != 0 && b != 0) ? 1.0 : 0.0
template<typename DerivedA, typename DerivedB>
auto And(const Eigen::ArrayBase<DerivedA>& a, const Eigen::ArrayBase<DerivedB>& b) {
    using Scalar = typename DerivedA::Scalar;
    auto condition = (a != Scalar(0)) && (b != Scalar(0));
    return apply_logical_binary_op(a, b, condition);
}

template<typename DerivedA>
auto And(const Eigen::ArrayBase<DerivedA>& a, typename DerivedA::Scalar b_scalar) {
    using Scalar = typename DerivedA::Scalar;
    using BoolArrayType = Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic>;
    return apply_logical_binary_op_scalar_rhs(a, b_scalar,
        [=](const auto& arr, Scalar val) { // Pass by value for scalar 'val' in lambda if appropriate
            bool val_cond_bool = (val != Scalar(0));
            auto val_cond_arr = BoolArrayType::Constant(arr.rows(), arr.cols(), val_cond_bool);
            return (arr != Scalar(0)) && val_cond_arr;
        });
}

// Or: Result is 0.0 if a or b is NaN. Otherwise, (a != 0 || b != 0) ? 1.0 : 0.0
template<typename DerivedA, typename DerivedB>
auto Or(const Eigen::ArrayBase<DerivedA>& a, const Eigen::ArrayBase<DerivedB>& b) {
    using Scalar = typename DerivedA::Scalar;
    auto condition = (a != Scalar(0)) || (b != Scalar(0));
    return apply_logical_binary_op(a, b, condition);
}

template<typename DerivedA>
auto Or(const Eigen::ArrayBase<DerivedA>& a, typename DerivedA::Scalar b_scalar) {
    using Scalar = typename DerivedA::Scalar;
    using BoolArrayType = Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic>;
    return apply_logical_binary_op_scalar_rhs(a, b_scalar,
        [=](const auto& arr, Scalar val) {
            bool val_cond_bool = (val != Scalar(0));
            auto val_cond_arr = BoolArrayType::Constant(arr.rows(), arr.cols(), val_cond_bool);
            return (arr != Scalar(0)) || val_cond_arr;
        });
}

// Not: Result is 0.0 if a is NaN. Otherwise, (a == 0) ? 1.0 : 0.0
template<typename DerivedA>
auto Not(const Eigen::ArrayBase<DerivedA>& a) {
    using Scalar = typename DerivedA::Scalar;
    static_assert(std::is_floating_point<Scalar>::value, "Not operation requires a floating-point scalar type for NaN handling and 0.0/1.0 results.");

    auto a_is_not_nan = !a.isNaN();
    auto main_condition = (a == Scalar(0));
    Scalar zero_fill = Scalar(0);
    auto zero_fill_expr = Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        a.rows(), a.cols(), zero_fill
    );

    return a_is_not_nan.select(main_condition.template cast<Scalar>(), zero_fill_expr);
}

// Xor: Result is 0.0 if a or b is NaN. Otherwise, ((a != 0 && b == 0) || (a == 0 && b != 0)) ? 1.0 : 0.0
template<typename DerivedA, typename DerivedB>
auto Xor(const Eigen::ArrayBase<DerivedA>& a, const Eigen::ArrayBase<DerivedB>& b) {
    using Scalar = typename DerivedA::Scalar;
    // (a != 0 XOR b != 0) is equivalent to (a != 0) != (b != 0) for boolean arrays
    auto condition = (a != Scalar(0)) != (b != Scalar(0));
    return apply_logical_binary_op(a, b, condition);
}

template<typename DerivedA>
auto Xor(const Eigen::ArrayBase<DerivedA>& a, typename DerivedA::Scalar b_scalar) {
    using Scalar = typename DerivedA::Scalar;
    using BoolArrayType = Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic>;
    return apply_logical_binary_op_scalar_rhs(a, b_scalar,
        [=](const auto& arr, Scalar val) {
            bool val_cond_bool = (val != Scalar(0));
            auto val_cond_arr = BoolArrayType::Constant(arr.rows(), arr.cols(), val_cond_bool);
            return (arr != Scalar(0)) != val_cond_arr; // XOR logic
        });
}

} // namespace feature_operators
