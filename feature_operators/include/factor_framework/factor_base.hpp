#ifndef FACTOR_FRAMEWORK_FACTOR_BASE_HPP
#define FACTOR_FRAMEWORK_FACTOR_BASE_HPP

#include "feature_operators/types.hpp"
#include <string>
#include <memory>
#include <unordered_map>

namespace factor_framework {

/**
 * 因子基类 - 所有因子的抽象基类
 * 提供统一的接口和通用功能
 */
class FactorBase {
public:
    /**
     * 构造函数
     * @param factor_id 因子ID
     * @param factor_name 因子名称
     * @param formula 因子公式
     */
    FactorBase(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 虚析构函数
     */
    virtual ~FactorBase() = default;

    /**
     * 计算因子值 - 纯虚函数，由子类实现
     * @param data_map 输入数据映射 (字段名 -> DataFrame)
     * @return 计算结果
     */
    virtual feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) = 0;

    /**
     * 获取因子ID
     */
    int getId() const { return factor_id_; }

    /**
     * 获取因子名称
     */
    const std::string& getName() const { return factor_name_; }

    /**
     * 获取因子公式
     */
    const std::string& getFormula() const { return formula_; }

    /**
     * 获取因子依赖的数据字段
     */
    virtual std::vector<std::string> getRequiredFields() const = 0;

    /**
     * 验证输入数据是否有效
     * @param data_map 输入数据映射
     * @return 是否有效
     */
    virtual bool validateInput(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const;

    /**
     * 设置因子参数
     * @param params 参数映射
     */
    virtual void setParameters(const std::unordered_map<std::string, double>& params) {}

    /**
     * 获取因子描述信息
     */
    virtual std::string getDescription() const;

protected:
    int factor_id_;           ///< 因子ID
    std::string factor_name_; ///< 因子名称
    std::string formula_;     ///< 因子公式

    /**
     * 检查数据字段是否存在
     * @param data_map 数据映射
     * @param field_name 字段名
     * @return 是否存在
     */
    bool checkFieldExists(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map,
        const std::string& field_name) const;

    /**
     * 获取数据字段
     * @param data_map 数据映射
     * @param field_name 字段名
     * @return 数据引用
     */
    const feature_operators::DataFrame& getField(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map,
        const std::string& field_name) const;
};

/**
 * 因子工厂基类
 */
class FactorFactory {
public:
    virtual ~FactorFactory() = default;

    /**
     * 创建因子实例
     * @param factor_id 因子ID
     * @param factor_name 因子名称
     * @param formula 因子公式
     * @return 因子实例指针
     */
    virtual std::unique_ptr<FactorBase> createFactor(
        int factor_id,
        const std::string& factor_name,
        const std::string& formula) = 0;
};

/**
 * 因子注册器 - 用于注册因子工厂
 */
class FactorRegistry {
public:
    /**
     * 获取单例实例
     */
    static FactorRegistry& getInstance();

    /**
     * 注册因子工厂
     * @param factor_name 因子名称
     * @param factory 工厂指针
     */
    void registerFactory(const std::string& factor_name,
                        std::unique_ptr<FactorFactory> factory);

    /**
     * 创建因子实例
     * @param factor_id 因子ID
     * @param factor_name 因子名称
     * @param formula 因子公式
     * @return 因子实例指针
     */
    std::unique_ptr<FactorBase> createFactor(
        int factor_id,
        const std::string& factor_name,
        const std::string& formula);

    /**
     * 获取所有注册的因子名称
     */
    std::vector<std::string> getRegisteredFactors() const;

private:
    FactorRegistry() = default;
    std::unordered_map<std::string, std::unique_ptr<FactorFactory>> factories_;
};

/**
 * 因子注册宏 - 简化因子注册过程
 * 使用方式: REGISTER_FACTOR(FactorClassName, "factor_name")
 * 注意: 必须在因子类定义的同一个命名空间中使用
 */
#define REGISTER_FACTOR(FactorClass, factor_name) \
    namespace { \
        class FactorClass##Factory : public factor_framework::FactorFactory { \
        public: \
            std::unique_ptr<factor_framework::FactorBase> createFactor( \
                int factor_id, \
                const std::string& name, \
                const std::string& formula) override { \
                return std::make_unique<FactorClass>(factor_id, name, formula); \
            } \
        }; \
        static bool FactorClass##_registered = []() { \
            factor_framework::FactorRegistry::getInstance().registerFactory( \
                factor_name, \
                std::make_unique<FactorClass##Factory>()); \
            return true; \
        }(); \
    }

} // namespace factor_framework

#endif // FACTOR_FRAMEWORK_FACTOR_BASE_HPP
