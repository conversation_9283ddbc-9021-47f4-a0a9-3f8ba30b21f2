#ifndef FACTOR_FRAMEWORK_FACTOR_ENGINE_HPP
#define FACTOR_FRAMEWORK_FACTOR_ENGINE_HPP

#include "factor_manager.hpp"
#include "data_interface.hpp"
#include <chrono>
#include <memory>

namespace factor_framework {

/**
 * 计算结果结构
 */
struct calculation_result {
    std::unordered_map<std::string, feature_operators::DataFrame> factor_results; ///< 因子计算结果
    std::chrono::milliseconds calculation_time;                                   ///< 计算耗时
    bool success;                                                                 ///< 是否成功
    std::string error_message;                                                    ///< 错误信息

    calculation_result() : calculation_time(0), success(false) {}
};

/**
 * 性能统计信息
 */
struct performance_stats {
    std::unordered_map<std::string, std::chrono::milliseconds> factor_times; ///< 各因子计算时间
    std::chrono::milliseconds total_time;                                    ///< 总计算时间
    size_t factors_calculated;                                               ///< 计算的因子数量
    size_t data_points_processed;                                            ///< 处理的数据点数量

    performance_stats() : total_time(0), factors_calculated(0), data_points_processed(0) {}

    /**
     * 获取平均每因子计算时间
     */
    double getAverageTimePerFactor() const {
        return factors_calculated > 0 ?
               static_cast<double>(total_time.count()) / factors_calculated : 0.0;
    }

    /**
     * 获取数据处理速度 (数据点/秒)
     */
    double getDataProcessingRate() const {
        return total_time.count() > 0 ?
               static_cast<double>(data_points_processed) * 1000.0 / total_time.count() : 0.0;
    }
};

/**
 * 因子计算引擎 - 高性能因子计算核心
 */
class factor_engine {
public:
    /**
     * 构造函数
     * @param factor_manager 因子管理器
     * @param data_interface 数据接口
     */
    factor_engine(std::shared_ptr<factor_manager> factor_manager,
                std::shared_ptr<data_interface> data_interface);

    /**
     * 析构函数
     */
    ~factor_engine() = default;

    /**
     * 计算选中的因子
     * @return 计算结果
     */
    CalculationResult calculate_selected_factors();

    /**
     * 计算指定因子
     * @param factor_ids 因子ID列表
     * @return 计算结果
     */
    CalculationResult calculate_factors(const std::vector<int>& factor_ids);

    /**
     * 计算指定因子
     * @param factor_names 因子名称列表
     * @return 计算结果
     */
    CalculationResult calculate_factors(const std::vector<std::string>& factor_names);

    /**
     * 计算单个因子
     * @param factor_id 因子ID
     * @return 计算结果
     */
    CalculationResult calculate_single_factor(int factor_id);

    /**
     * 批量计算所有因子
     * @return 计算结果
     */
    CalculationResult calculateAllFactors();

    /**
     * 设置并行计算线程数
     * @param num_threads 线程数，0表示使用硬件并发数
     */
    void setNumThreads(int num_threads);

    /**
     * 获取当前线程数设置
     */
    int getNumThreads() const { return num_threads_; }

    /**
     * 启用/禁用性能统计
     * @param enable 是否启用
     */
    void enablePerformanceStats(bool enable) { enable_performance_stats_ = enable; }

    /**
     * 获取最近一次计算的性能统计
     */
    const performance_stats& getLastPerformanceStats() const { return last_performance_stats_; }

    /**
     * 重置性能统计
     */
    void resetPerformanceStats();

    /**
     * 验证计算环境
     * @return 验证结果和错误信息
     */
    std::pair<bool, std::string> validateEnvironment() const;

    /**
     * 预热计算引擎（预分配内存等）
     */
    void warmUp();

    /**
     * 设置计算超时时间
     * @param timeout_ms 超时时间（毫秒）
     */
    void setTimeout(int timeout_ms) { timeout_ms_ = timeout_ms; }

    /**
     * 获取计算超时时间
     */
    int getTimeout() const { return timeout_ms_; }

    /**
     * 启用/禁用详细日志
     * @param enable 是否启用
     */
    void enableVerboseLogging(bool enable) { verbose_logging_ = enable; }

    /**
     * 获取因子管理器
     */
    std::shared_ptr<factor_manager> getFactorManager() const { return factor_manager_; }

    /**
     * 获取数据接口
     */
    std::shared_ptr<data_interface> getDataInterface() const { return data_interface_; }

    /**
     * 打印引擎状态信息
     */
    void printEngineStatus() const;

private:
    std::shared_ptr<factor_manager> factor_manager_;   ///< 因子管理器
    std::shared_ptr<data_interface> data_interface_;   ///< 数据接口

    int num_threads_;                                 ///< 并行计算线程数
    bool enable_performance_stats_;                   ///< 是否启用性能统计
    PerformanceStats last_performance_stats_;         ///< 最近一次性能统计
    int timeout_ms_;                                  ///< 计算超时时间
    bool verbose_logging_;                            ///< 是否启用详细日志

    /**
     * 内部计算实现
     * @param factor_ids 因子ID列表
     * @return 计算结果
     */
    CalculationResult calculateFactorsInternal(const std::vector<int>& factor_ids);

    /**
     * 单线程计算因子
     * @param factor_ids 因子ID列表
     * @return 计算结果
     */
    CalculationResult calculateFactorsSingleThread(const std::vector<int>& factor_ids);

    /**
     * 多线程计算因子
     * @param factor_ids 因子ID列表
     * @return 计算结果
     */
    CalculationResult calculateFactorsMultiThread(const std::vector<int>& factor_ids);

    /**
     * 记录因子计算时间
     * @param factor_name 因子名称
     * @param duration 计算时间
     */
    void recordFactorTime(const std::string& factor_name,
                         std::chrono::milliseconds duration);

    /**
     * 日志输出
     * @param message 日志信息
     */
    void log(const std::string& message) const;

    /**
     * 详细日志输出
     * @param message 日志信息
     */
    void verboseLog(const std::string& message) const;
};

} // namespace factor_framework

#endif // FACTOR_FRAMEWORK_FACTOR_ENGINE_HPP
