#ifndef FACTOR_FRAMEWORK_DATA_INTERFACE_HPP
#define FACTOR_FRAMEWORK_DATA_INTERFACE_HPP

#include "feature_operators/types.hpp"
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>

namespace factor_framework {

/**
 * 数据接口 - 处理输入数据的加载和管理
 */
class data_interface {
public:
    /**
     * 构造函数
     */
    data_interface() = default;

    /**
     * 析构函数
     */
    ~data_interface() = default;

    /**
     * 从CSV文件加载数据
     * @param field_name 字段名称
     * @param csv_file_path CSV文件路径
     * @return 是否加载成功
     */
    bool loadDataFromCSV(const std::string& field_name, const std::string& csv_file_path);

    /**
     * 添加数据
     * @param field_name 字段名称
     * @param data 数据
     */
    void addData(const std::string& field_name, const feature_operators::DataFrame& data);

    /**
     * 获取数据
     * @param field_name 字段名称
     * @return 数据引用
     */
    const feature_operators::DataFrame& getData(const std::string& field_name) const;

    /**
     * 检查字段是否存在
     * @param field_name 字段名称
     * @return 是否存在
     */
    bool hasField(const std::string& field_name) const;

    /**
     * 获取所有字段名称
     */
    std::vector<std::string> getFieldNames() const;

    /**
     * 获取所有数据
     */
    const std::unordered_map<std::string, feature_operators::DataFrame>& getAllData() const;

    /**
     * 移除字段
     * @param field_name 字段名称
     * @return 是否移除成功
     */
    bool removeField(const std::string& field_name);

    /**
     * 清空所有数据
     */
    void clear();

    /**
     * 获取数据维度信息
     * @param field_name 字段名称
     * @return 维度信息 (行数, 列数)
     */
    std::pair<int, int> getDataDimensions(const std::string& field_name) const;

    /**
     * 验证所有数据维度是否一致
     * @return 是否一致
     */
    bool validateDataDimensions() const;

    /**
     * 获取数据统计信息
     * @param field_name 字段名称
     * @return 统计信息字符串
     */
    std::string getDataStatistics(const std::string& field_name) const;

    /**
     * 打印数据信息
     */
    void printDataInfo() const;

    /**
     * 创建标准市场数据字段
     * 包括：Open, High, Low, Close, Volume, Amount, VWAP
     * @param rows 行数
     * @param cols 列数
     * @param fill_value 填充值
     */
    void createStandardMarketData(int rows, int cols, double fill_value = 0.0);

    /**
     * 从目录批量加载CSV文件
     * @param directory_path 目录路径
     * @param file_pattern 文件模式（如 "*.csv"）
     * @return 加载的文件数量
     */
    int loadDataFromDirectory(const std::string& directory_path,
                             const std::string& file_pattern = "*.csv");

private:
    /// 数据存储 (字段名 -> 数据)
    std::unordered_map<std::string, feature_operators::DataFrame> data_map_;

    /**
     * 解析CSV文件
     * @param csv_file_path CSV文件路径
     * @return 解析后的数据
     */
    feature_operators::DataFrame parseCSVFile(const std::string& csv_file_path) const;

    /**
     * 从文件路径提取字段名
     * @param file_path 文件路径
     * @return 字段名
     */
    std::string extractFieldNameFromPath(const std::string& file_path) const;

    /**
     * 验证数据有效性
     * @param data 数据
     * @return 是否有效
     */
    bool validateData(const feature_operators::DataFrame& data) const;
};

/**
 * 数据加载器工厂 - 支持不同数据源
 */
class DataLoaderFactory {
public:
    /**
     * 从测试数据目录加载标准数据
     * @param test_data_dir 测试数据目录
     * @return 数据接口实例
     */
    static std::unique_ptr<data_interface> createFromTestData(const std::string& test_data_dir);

    /**
     * 创建随机测试数据
     * @param rows 行数
     * @param cols 列数
     * @param seed 随机种子
     * @return 数据接口实例
     */
    static std::unique_ptr<data_interface> createRandomTestData(int rows, int cols, int seed = 42);

    /**
     * 从配置文件创建数据接口
     * @param config_file 配置文件路径
     * @return 数据接口实例
     */
    static std::unique_ptr<data_interface> createFromConfig(const std::string& config_file);
};

} // namespace factor_framework

#endif // FACTOR_FRAMEWORK_DATA_INTERFACE_HPP
