#ifndef FACTOR_FRAMEWORK_FACTOR_MANAGER_HPP
#define FACTOR_FRAMEWORK_FACTOR_MANAGER_HPP

#include "factor_base.hpp"
#include <vector>
#include <unordered_set>
#include <memory>
#include <string>

namespace factor_framework {

/**
 * 因子管理器 - 管理所有因子的生命周期和计算
 */
class FactorManager {
public:
    /**
     * 构造函数
     */
    FactorManager();

    /**
     * 析构函数
     */
    ~FactorManager() = default;

    /**
     * 从CSV文件加载因子配置
     * @param csv_file_path CSV文件路径
     * @return 是否加载成功
     */
    bool loadFactorsFromCSV(const std::string& csv_file_path);

    /**
     * 添加因子（通过工厂创建，保留兼容性）
     * @param factor_id 因子ID
     * @param factor_name 因子名称
     * @param formula 因子公式
     * @return 是否添加成功
     */
    bool addFactor(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 直接注册因子实例（新的简化方式）
     * @param factor 因子实例的智能指针
     * @return 是否注册成功
     */
    bool registerFactor(std::unique_ptr<FactorBase> factor);

    /**
     * 批量注册因子实例
     * @param factors 因子实例列表
     * @return 成功注册的因子数量
     */
    int registerFactors(std::vector<std::unique_ptr<FactorBase>> factors);

    /**
     * 移除因子
     * @param factor_id 因子ID
     * @return 是否移除成功
     */
    bool removeFactor(int factor_id);

    /**
     * 获取因子
     * @param factor_id 因子ID
     * @return 因子指针，如果不存在返回nullptr
     */
    FactorBase* getFactor(int factor_id) const;

    /**
     * 获取因子
     * @param factor_name 因子名称
     * @return 因子指针，如果不存在返回nullptr
     */
    FactorBase* getFactor(const std::string& factor_name) const;

    /**
     * 获取所有因子ID
     */
    std::vector<int> getAllFactorIds() const;

    /**
     * 获取所有因子名称
     */
    std::vector<std::string> getAllFactorNames() const;

    /**
     * 选择要计算的因子
     * @param factor_ids 因子ID列表
     */
    void selectFactors(const std::vector<int>& factor_ids);

    /**
     * 选择要计算的因子
     * @param factor_names 因子名称列表
     */
    void selectFactors(const std::vector<std::string>& factor_names);

    /**
     * 获取选中的因子ID列表
     */
    std::vector<int> getSelectedFactorIds() const;

    /**
     * 清空选中的因子
     */
    void clearSelection();

    /**
     * 获取所有选中因子需要的数据字段
     */
    std::unordered_set<std::string> getRequiredFields() const;

    /**
     * 计算选中的因子
     * @param data_map 输入数据映射
     * @return 计算结果映射 (因子名称 -> 结果)
     */
    std::unordered_map<std::string, feature_operators::DataFrame> calculateSelectedFactors(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const;

    /**
     * 计算指定因子
     * @param factor_ids 因子ID列表
     * @param data_map 输入数据映射
     * @return 计算结果映射 (因子名称 -> 结果)
     */
    std::unordered_map<std::string, feature_operators::DataFrame> calculateFactors(
        const std::vector<int>& factor_ids,
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const;

    /**
     * 验证数据是否满足计算要求
     * @param data_map 输入数据映射
     * @return 验证结果
     */
    bool validateData(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const;

    /**
     * 获取因子数量
     */
    size_t getFactorCount() const { return factors_.size(); }

    /**
     * 获取选中因子数量
     */
    size_t getSelectedFactorCount() const { return selected_factor_ids_.size(); }

    /**
     * 打印因子信息
     */
    void printFactorInfo() const;

    /**
     * 打印选中因子信息
     */
    void printSelectedFactorInfo() const;

private:
    /// 因子存储 (因子ID -> 因子实例)
    std::unordered_map<int, std::unique_ptr<FactorBase>> factors_;

    /// 因子名称到ID的映射
    std::unordered_map<std::string, int> name_to_id_;

    /// 选中的因子ID集合
    std::unordered_set<int> selected_factor_ids_;

    /**
     * 解析CSV行
     * @param line CSV行内容
     * @param factor_id 输出因子ID
     * @param factor_name 输出因子名称
     * @param formula 输出因子公式
     * @return 是否解析成功
     */
    bool parseCSVLine(const std::string& line,
                     int& factor_id,
                     std::string& factor_name,
                     std::string& formula) const;

    /**
     * 清理字符串（去除引号和空格）
     * @param str 输入字符串
     * @return 清理后的字符串
     */
    std::string cleanString(const std::string& str) const;
};

} // namespace factor_framework

#endif // FACTOR_FRAMEWORK_FACTOR_MANAGER_HPP
