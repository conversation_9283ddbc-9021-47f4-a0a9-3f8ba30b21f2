# 因子框架快速开始指南

## 5分钟快速上手

### 第一步：生成因子代码

```bash
# 进入脚本目录
cd feature_operators/scripts

# 生成前5个因子的代码（用于测试）
python3 factor_code_generator.py \
    --input ../feature.csv \
    --output_dir ../generated_factors \
    --max_factors 5 \
    --verbose

# 或者使用Shell脚本
./generate_factors.sh -i ../feature.csv -o ../generated_factors -m 5 -v
```

### 第二步：查看生成的代码

```bash
# 查看生成的文件结构
ls -la ../generated_factors/

# 查看因子信息
cat ../generated_factors/factor_info.json

# 查看使用说明
cat ../generated_factors/README.md
```

### 第三步：编译测试（可选）

```bash
# 如果要编译生成的代码，需要先实现框架的实现文件
# 这里我们先运行一个简单的测试

cd ../examples
g++ -I../include -I/usr/include/eigen3 \
    simple_factor_test.cpp \
    ../src/timeseries_ops.cpp \
    -o simple_factor_test

# 运行测试
./simple_factor_test
```

## 详细使用步骤

### 1. 准备环境

确保您的系统已安装：
- Python 3.6+
- C++17 编译器
- Eigen3 库
- CMake 3.10+

```bash
# Ubuntu/Debian
sudo apt-get install python3 g++ libeigen3-dev cmake

# CentOS/RHEL
sudo yum install python3 gcc-c++ eigen3-devel cmake

# macOS
brew install python3 eigen cmake
```

### 2. 理解因子配置格式

您的 `feature.csv` 文件应该包含以下列：
- 第一列：因子ID（数字）
- 第二列：因子名称（字符串）
- 第三列：因子公式（使用feature_operators语法）

示例：
```csv
,fname,forms
0,p1_corrs0,"ts_Corr(Close,Volume,60)"
1,p1_corrs1,"ts_Corr(Close/ts_Delay(Close,1)-1,Volume,60)"
2,p1_corrs2,"ts_Corr(ts_Delay(Close,1),Volume,60)"
```

### 3. 生成因子代码

```bash
# 基本用法：生成所有因子
python3 factor_code_generator.py --input feature.csv --output_dir generated_factors

# 高级用法：带过滤和限制
python3 factor_code_generator.py \
    --input feature.csv \
    --output_dir generated_factors \
    --filter "p1_*" \
    --max_factors 10 \
    --verbose

# 干运行：只解析不生成（用于验证）
python3 factor_code_generator.py \
    --input feature.csv \
    --output_dir generated_factors \
    --dry_run
```

### 4. 集成到您的项目

#### 方法1：作为子项目

在您的 `CMakeLists.txt` 中添加：
```cmake
# 添加因子框架
add_subdirectory(path/to/feature_operators)
add_subdirectory(path/to/generated_factors)

# 链接到您的目标
target_link_libraries(your_target 
    feature_ops_lib 
    generated_factors
)
```

#### 方法2：直接包含源文件

```cmake
# 包含头文件目录
include_directories(
    path/to/feature_operators/include
    path/to/generated_factors/include
)

# 添加源文件
file(GLOB FACTOR_SOURCES 
    "path/to/generated_factors/src/*.cpp"
    "path/to/feature_operators/src/factor_framework/*.cpp"
)

add_executable(your_program ${FACTOR_SOURCES} your_main.cpp)
```

### 5. 编写使用代码

创建 `main.cpp`：
```cpp
#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"
#include <iostream>

int main() {
    try {
        // 初始化框架
        FACTOR_FRAMEWORK_INIT();
        generated_factors::initializeAllFactors();
        
        // 创建组件
        auto factor_manager = std::make_shared<factor_framework::FactorManager>();
        auto data_interface = std::make_shared<factor_framework::DataInterface>();
        
        // 加载因子配置
        if (!factor_manager->loadFactorsFromCSV("feature.csv")) {
            std::cerr << "Failed to load factors" << std::endl;
            return 1;
        }
        
        // 创建测试数据
        data_interface->createStandardMarketData(1000, 100);
        
        // 创建计算引擎
        factor_framework::FactorEngine engine(factor_manager, data_interface);
        
        // 选择前3个因子
        auto all_ids = factor_manager->get_all_factor_ids();
        std::vector<int> selected_ids(all_ids.begin(), all_ids.begin() + 3);
        factor_manager->selectFactors(selected_ids);
        
        // 执行计算
        auto result = engine.calculateSelectedFactors();
        
        if (result.success) {
            std::cout << "计算成功！计算了 " << result.factor_results.size() << " 个因子" << std::endl;
            for (const auto& [name, data] : result.factor_results) {
                std::cout << "因子 " << name << ": " << data.rows() << "x" << data.cols() << std::endl;
            }
        } else {
            std::cerr << "计算失败: " << result.error_message << std::endl;
        }
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
}
```

### 6. 编译和运行

```bash
# 使用CMake编译
mkdir build && cd build
cmake ..
make

# 运行程序
./your_program
```

## 常见问题

### Q1: 编译时找不到头文件
**A**: 确保正确设置了包含路径，特别是Eigen3的路径：
```bash
# 查找Eigen3路径
find /usr -name "Eigen" -type d 2>/dev/null

# 在CMakeLists.txt中添加
find_package(Eigen3 REQUIRED)
target_link_libraries(your_target Eigen3::Eigen)
```

### Q2: 运行时因子注册失败
**A**: 确保调用了初始化函数：
```cpp
FACTOR_FRAMEWORK_INIT();
generated_factors::initializeAllFactors();
```

### Q3: 数据维度不匹配错误
**A**: 确保所有输入数据具有相同的维度：
```cpp
// 验证数据
if (!data_interface->validateDataDimensions()) {
    std::cerr << "Data dimensions mismatch" << std::endl;
}
```

### Q4: 计算结果包含大量NaN
**A**: 这通常是正常的，特别是对于需要历史数据的时间序列操作。检查：
- 窗口大小是否合理
- 输入数据是否包含NaN
- 是否有足够的历史数据

### Q5: 性能较慢
**A**: 尝试以下优化：
```cpp
// 启用多线程
engine.setNumThreads(8);

// 预热引擎
engine.warmUp();

// 批量计算相关因子
std::vector<int> related_factors = {0, 1, 2};  // 使用相同数据的因子
auto result = engine.calculateFactors(related_factors);
```

## 下一步

1. **阅读完整文档**: 查看 `FACTOR_FRAMEWORK_README.md`
2. **查看示例**: 运行 `examples/` 目录中的示例程序
3. **性能测试**: 使用大数据集测试性能
4. **自定义扩展**: 添加自己的因子类型和操作符

## 获取帮助

- 查看生成的 `README.md` 文件
- 检查 `factor_info.json` 了解因子详情
- 运行 `python3 factor_code_generator.py --help` 查看所有选项
- 查看示例代码了解最佳实践

祝您使用愉快！🎉
