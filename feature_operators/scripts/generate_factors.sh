#!/bin/bash

# 因子代码生成脚本
# 使用方法: ./generate_factors.sh [选项]

set -e  # 遇到错误立即退出

# 默认配置
INPUT_CSV="../feature.csv"
OUTPUT_DIR="../generated_factors"
VERBOSE=false
DRY_RUN=false
MAX_FACTORS=""
FILTER=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
因子代码生成脚本

使用方法: $0 [选项]

选项:
    -i, --input CSV_FILE        输入的因子CSV文件路径 (默认: $INPUT_CSV)
    -o, --output OUTPUT_DIR     输出目录路径 (默认: $OUTPUT_DIR)
    -f, --filter PATTERN        因子名称过滤器 (支持通配符)
    -m, --max-factors NUM       限制生成的因子数量
    -v, --verbose               启用详细输出
    -d, --dry-run              只解析不生成代码
    -h, --help                 显示此帮助信息

示例:
    $0                                          # 使用默认配置
    $0 -i feature.csv -o my_factors            # 指定输入输出
    $0 -f "p1_*" -v                           # 只生成p1开头的因子，详细输出
    $0 -m 10 -d                               # 限制10个因子，干运行模式

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--input)
            INPUT_CSV="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -f|--filter)
            FILTER="$2"
            shift 2
            ;;
        -m|--max-factors)
            MAX_FACTORS="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查Python环境
check_python() {
    print_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未找到，请安装Python3"
        exit 1
    fi
    
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_success "Python版本: $python_version"
    
    # 检查必要的Python模块
    required_modules=("argparse" "csv" "json" "pathlib")
    for module in "${required_modules[@]}"; do
        if ! python3 -c "import $module" &> /dev/null; then
            print_error "Python模块 $module 未找到"
            exit 1
        fi
    done
    
    print_success "Python环境检查通过"
}

# 检查输入文件
check_input() {
    print_info "检查输入文件..."
    
    if [[ ! -f "$INPUT_CSV" ]]; then
        print_error "输入文件不存在: $INPUT_CSV"
        exit 1
    fi
    
    # 检查文件格式
    if ! head -1 "$INPUT_CSV" | grep -q "fname.*forms"; then
        print_warning "输入文件可能不是标准的因子CSV格式"
    fi
    
    line_count=$(wc -l < "$INPUT_CSV")
    print_success "输入文件: $INPUT_CSV (共 $((line_count-1)) 个因子)"
}

# 准备输出目录
prepare_output() {
    print_info "准备输出目录..."
    
    if [[ -d "$OUTPUT_DIR" ]]; then
        print_warning "输出目录已存在: $OUTPUT_DIR"
        read -p "是否清空现有目录? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$OUTPUT_DIR"
            print_info "已清空输出目录"
        fi
    fi
    
    mkdir -p "$OUTPUT_DIR"
    print_success "输出目录准备完成: $OUTPUT_DIR"
}

# 构建Python命令
build_command() {
    local cmd="python3 factor_code_generator.py"
    cmd="$cmd --input \"$INPUT_CSV\""
    cmd="$cmd --output_dir \"$OUTPUT_DIR\""
    
    if [[ "$VERBOSE" == true ]]; then
        cmd="$cmd --verbose"
    fi
    
    if [[ "$DRY_RUN" == true ]]; then
        cmd="$cmd --dry_run"
    fi
    
    if [[ -n "$FILTER" ]]; then
        cmd="$cmd --filter \"$FILTER\""
    fi
    
    if [[ -n "$MAX_FACTORS" ]]; then
        cmd="$cmd --max_factors $MAX_FACTORS"
    fi
    
    echo "$cmd"
}

# 运行代码生成器
run_generator() {
    print_info "运行因子代码生成器..."
    
    local cmd=$(build_command)
    print_info "执行命令: $cmd"
    
    if eval "$cmd"; then
        print_success "代码生成完成！"
        return 0
    else
        print_error "代码生成失败"
        return 1
    fi
}

# 显示结果摘要
show_summary() {
    if [[ "$DRY_RUN" == true ]]; then
        print_info "干运行模式，未生成实际文件"
        return
    fi
    
    if [[ ! -d "$OUTPUT_DIR" ]]; then
        return
    fi
    
    print_info "生成结果摘要:"
    
    # 统计生成的文件
    header_count=$(find "$OUTPUT_DIR" -name "*.hpp" | wc -l)
    source_count=$(find "$OUTPUT_DIR" -name "*.cpp" | wc -l)
    
    echo "  头文件: $header_count 个"
    echo "  源文件: $source_count 个"
    
    if [[ -f "$OUTPUT_DIR/factor_info.json" ]]; then
        factor_count=$(python3 -c "import json; data=json.load(open('$OUTPUT_DIR/factor_info.json')); print(len(data))")
        echo "  因子数量: $factor_count 个"
    fi
    
    if [[ -f "$OUTPUT_DIR/CMakeLists.txt" ]]; then
        echo "  CMake配置: ✓"
    fi
    
    if [[ -f "$OUTPUT_DIR/README.md" ]]; then
        echo "  使用说明: ✓"
    fi
    
    print_success "输出目录: $OUTPUT_DIR"
}

# 主函数
main() {
    echo "========================================"
    echo "        因子代码自动生成器"
    echo "========================================"
    
    check_python
    check_input
    
    if [[ "$DRY_RUN" != true ]]; then
        prepare_output
    fi
    
    if run_generator; then
        show_summary
        print_success "🎉 因子代码生成成功完成！"
        
        if [[ "$DRY_RUN" != true ]]; then
            echo ""
            echo "下一步:"
            echo "1. cd $OUTPUT_DIR"
            echo "2. mkdir build && cd build"
            echo "3. cmake .."
            echo "4. make"
        fi
    else
        print_error "❌ 因子代码生成失败"
        exit 1
    fi
}

# 运行主函数
main "$@"
