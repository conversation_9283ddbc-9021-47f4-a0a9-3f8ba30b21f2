# 因子框架设计文档

## 概述

本项目实现了一个完整的因子计算框架，能够从CSV配置文件自动生成C++因子代码，并提供高性能的因子计算引擎。

## 设计目标

1. **通用性**: 设计通用的因子接口，支持各种类型的因子计算
2. **自动化**: 从CSV配置自动生成C++代码，减少手工编码工作
3. **高性能**: 支持多线程并行计算，优化内存使用
4. **易用性**: 提供简洁的API和完整的文档
5. **可扩展性**: 支持新因子类型的轻松添加

## 架构设计

### 1. 核心组件

```
因子框架
├── 因子基类 (FactorBase)           # 所有因子的抽象基类
├── 因子管理器 (FactorManager)      # 管理因子生命周期
├── 数据接口 (DataInterface)       # 处理输入数据
├── 计算引擎 (FactorEngine)        # 高性能计算核心
└── 代码生成器 (Python脚本)        # 自动生成C++代码
```

### 2. 设计模式

- **工厂模式**: 用于创建因子实例
- **注册模式**: 自动注册因子类型
- **策略模式**: 支持不同的计算策略
- **观察者模式**: 性能监控和日志记录

### 3. 数据流

```
CSV配置 → Python生成器 → C++因子类 → 因子管理器 → 计算引擎 → 结果输出
```

## 主要特性

### 1. 因子基类 (FactorBase)

```cpp
class FactorBase {
public:
    // 纯虚函数，子类必须实现
    virtual DataFrame calculate(const DataMap& data_map) = 0;
    virtual std::vector<std::string> getRequiredFields() const = 0;
    
    // 通用功能
    bool validateInput(const DataMap& data_map) const;
    std::string getDescription() const;
    
protected:
    int factor_id_;
    std::string factor_name_;
    std::string formula_;
};
```

**特点**:
- 统一的接口设计
- 自动输入验证
- 错误处理机制
- 元数据管理

### 2. 因子管理器 (FactorManager)

```cpp
class FactorManager {
public:
    // 因子管理
    bool loadFactorsFromCSV(const std::string& csv_file);
    bool addFactor(int id, const std::string& name, const std::string& formula);
    
    // 因子选择
    void selectFactors(const std::vector<int>& factor_ids);
    std::vector<int> getSelectedFactorIds() const;
    
    // 批量计算
    ResultMap calculateSelectedFactors(const DataMap& data_map) const;
};
```

**特点**:
- CSV配置加载
- 动态因子选择
- 依赖分析
- 批量计算支持

### 3. 数据接口 (DataInterface)

```cpp
class DataInterface {
public:
    // 数据加载
    bool loadDataFromCSV(const std::string& field, const std::string& file);
    void addData(const std::string& field, const DataFrame& data);
    
    // 数据访问
    const DataFrame& getData(const std::string& field) const;
    bool hasField(const std::string& field) const;
    
    // 数据验证
    bool validateDataDimensions() const;
};
```

**特点**:
- 多种数据源支持
- 自动维度验证
- 内存优化
- 统计信息提供

### 4. 计算引擎 (FactorEngine)

```cpp
class FactorEngine {
public:
    // 计算接口
    CalculationResult calculateSelectedFactors();
    CalculationResult calculateFactors(const std::vector<int>& ids);
    
    // 性能配置
    void setNumThreads(int num_threads);
    void enablePerformanceStats(bool enable);
    
    // 环境管理
    std::pair<bool, std::string> validateEnvironment() const;
    void warmUp();
};
```

**特点**:
- 多线程并行计算
- 性能统计监控
- 错误处理和恢复
- 内存预分配优化

## 代码生成器

### 1. 功能特性

- **CSV解析**: 自动解析因子配置文件
- **代码生成**: 生成标准C++因子类
- **依赖分析**: 自动提取所需数据字段
- **批量处理**: 支持大量因子的批量生成

### 2. 使用方法

```bash
# 基本用法
python3 factor_code_generator.py --input feature.csv --output_dir generated_factors

# 高级用法
python3 factor_code_generator.py \
    --input feature.csv \
    --output_dir generated_factors \
    --filter "p1_*" \
    --max_factors 10 \
    --verbose

# 使用Shell脚本
./generate_factors.sh -i feature.csv -o generated_factors -v
```

### 3. 生成的文件结构

```
generated_factors/
├── include/generated_factors/
│   ├── all_factors.hpp          # 总头文件
│   └── factor*.hpp              # 各因子头文件
├── src/
│   ├── all_factors.cpp          # 总实现文件
│   └── factor*.cpp              # 各因子实现文件
├── CMakeLists.txt               # CMake构建配置
├── factor_info.json             # 因子元数据
└── README.md                    # 使用说明
```

## 使用示例

### 1. 完整使用流程

```cpp
#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"

int main() {
    // 1. 初始化框架
    FACTOR_FRAMEWORK_INIT();
    generated_factors::initializeAllFactors();
    
    // 2. 创建组件
    auto factor_manager = std::make_shared<FactorManager>();
    auto data_interface = std::make_shared<DataInterface>();
    
    // 3. 加载配置和数据
    factor_manager->loadFactorsFromCSV("feature.csv");
    data_interface->loadDataFromCSV("Close", "close.csv");
    data_interface->loadDataFromCSV("Volume", "volume.csv");
    
    // 4. 创建计算引擎
    FactorEngine engine(factor_manager, data_interface);
    engine.setNumThreads(8);
    engine.enablePerformanceStats(true);
    
    // 5. 选择和计算因子
    factor_manager->selectFactors({0, 1, 2, 3, 4});
    auto result = engine.calculateSelectedFactors();
    
    // 6. 处理结果
    if (result.success) {
        for (const auto& [name, data] : result.factor_results) {
            std::cout << "因子: " << name << std::endl;
            // 处理因子数据...
        }
    }
    
    return 0;
}
```

### 2. 性能优化示例

```cpp
// 配置多线程
engine.setNumThreads(std::thread::hardware_concurrency());

// 启用性能监控
engine.enablePerformanceStats(true);

// 预热引擎
engine.warmUp();

// 批量计算
auto result = engine.calculateAllFactors();

// 查看性能统计
const auto& stats = engine.getLastPerformanceStats();
std::cout << "总计算时间: " << stats.total_time.count() << " ms" << std::endl;
std::cout << "平均每因子时间: " << stats.getAverageTimePerFactor() << " ms" << std::endl;
```

## 扩展指南

### 1. 添加新的操作符

1. 在`feature_operators`库中实现新操作符
2. 在代码生成器的`operator_mapping`中添加映射
3. 重新生成因子代码

### 2. 自定义因子类

```cpp
class CustomFactor : public FactorBase {
public:
    CustomFactor(int id, const std::string& name, const std::string& formula)
        : FactorBase(id, name, formula) {}
    
    DataFrame calculate(const DataMap& data_map) override {
        // 自定义计算逻辑
        return result;
    }
    
    std::vector<std::string> getRequiredFields() const override {
        return {"Field1", "Field2"};
    }
};

// 注册因子
REGISTER_FACTOR(CustomFactor, "custom_factor")
```

### 3. 自定义数据源

```cpp
class CustomDataInterface : public DataInterface {
public:
    bool loadFromDatabase(const std::string& connection_string) {
        // 从数据库加载数据
        return true;
    }
    
    bool loadFromAPI(const std::string& api_endpoint) {
        // 从API加载数据
        return true;
    }
};
```

## 性能特性

### 1. 计算性能

- **多线程并行**: 支持因子级别的并行计算
- **内存优化**: 智能内存管理和复用
- **缓存友好**: 优化数据访问模式
- **SIMD优化**: 利用Eigen库的向量化

### 2. 内存管理

- **延迟分配**: 按需分配内存
- **内存池**: 减少内存分配开销
- **智能指针**: 自动内存管理
- **数据共享**: 避免不必要的数据复制

### 3. 性能监控

- **计算时间统计**: 每个因子的计算时间
- **内存使用监控**: 峰值内存使用量
- **吞吐量统计**: 数据处理速度
- **错误率统计**: 计算失败率

## 最佳实践

### 1. 数据准备

- 确保所有数据字段维度一致
- 预处理缺失值和异常值
- 使用合适的数据类型
- 考虑数据的时间对齐

### 2. 因子计算

- 批量计算相关因子以提高效率
- 合理设置线程数量
- 启用性能监控
- 处理计算异常

### 3. 内存优化

- 及时释放不需要的数据
- 使用数据引用而非复制
- 考虑数据的生命周期
- 监控内存使用情况

### 4. 错误处理

- 验证输入数据的有效性
- 处理计算过程中的异常
- 记录详细的错误信息
- 提供恢复机制

## 总结

本因子框架提供了一个完整的解决方案，从因子定义到代码生成，再到高性能计算，涵盖了因子计算的全流程。通过合理的架构设计和优化，能够满足大规模因子计算的需求，同时保持良好的可维护性和扩展性。
