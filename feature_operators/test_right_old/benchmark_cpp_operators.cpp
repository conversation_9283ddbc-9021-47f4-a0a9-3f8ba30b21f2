#include "../include/feature_operators/core_math.hpp"
#include "../include/feature_operators/logical_ops.hpp"
#include "../include/feature_operators/comparison_ops.hpp"
#include "../include/feature_operators/data_utils.hpp"
#include "../include/feature_operators/reduction_ops.hpp"
#include "../include/feature_operators/timeseries_ops.hpp"
#include "../include/feature_operators/timeseries_ops_v2.hpp"
#include "../include/feature_operators/panel_ops.hpp"
#include "../include/feature_operators/group_ops.hpp"
#include <iostream>
#include <iomanip>
#include <vector>
#include <string>
#include <cmath>
#include <fstream>
#include <sstream>
#include <chrono>
#include <map>

using namespace feature_operators;

// 从CSV文件加载数据
DataFrame loadCsv(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filepath << std::endl;
        return DataFrame();
    }

    std::vector<std::vector<double>> data;
    std::string line;

    // 跳过标题行
    std::getline(file, line);

    // 读取数据行
    while (std::getline(file, line)) {
        std::vector<double> row;
        std::stringstream ss(line);
        std::string cell;

        // 跳过第一列（日期）
        std::getline(ss, cell, ',');

        // 读取数据列
        while (std::getline(ss, cell, ',')) {
            try {
                double value = std::stod(cell);
                row.push_back(value);
            } catch (const std::exception& e) {
                row.push_back(std::numeric_limits<double>::quiet_NaN());
            }
        }

        if (!row.empty()) {
            data.push_back(row);
        }
    }

    // 创建Eigen矩阵
    int rows = data.size();
    int cols = rows > 0 ? data[0].size() : 0;

    DataFrame matrix(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            matrix(i, j) = data[i][j];
        }
    }

    return matrix;
}

// 将结果保存为JSON文件
void saveResultsToJson(const std::map<std::string, double>& timings, const std::string& filepath) {
    std::ofstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filepath << std::endl;
        return;
    }

    file << "{\n";
    bool first = true;
    for (const auto& pair : timings) {
        if (!first) {
            file << ",\n";
        }
        first = false;
        file << "  \"" << pair.first << "\": " << std::fixed << std::setprecision(6) << pair.second;
    }
    file << "\n}";
    file.close();

    std::cout << "结果已保存到: " << filepath << std::endl;
}

// 测量函数执行时间的辅助函数（微秒）
template<typename Func>
double measureExecutionTime(Func func, int iterations) {
    // 预热
    func();

    auto start = std::chrono::high_resolution_clock::now();

    // 多次执行以获得更准确的时间
    for (int i = 0; i < iterations; ++i) {
        func();
    }

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::micro> duration = end - start;

    // 返回平均执行时间（微秒）
    return duration.count() / iterations;
}

int main(int argc, char* argv[]) {
    std::string dataDir = "test_data";
    std::string outputFile = "test_results/benchmark_cpp_results.json";
    int iterations = 100; // 默认迭代次数

    // 解析命令行参数
    if (argc > 1) {
        iterations = std::stoi(argv[1]);
    }

    std::cout << "从目录加载数据: " << dataDir << std::endl;
    std::cout << "迭代次数: " << iterations << std::endl;

    // 加载数据
    DataFrame open = loadCsv(dataDir + "/open.csv");
    DataFrame high = loadCsv(dataDir + "/high.csv");
    DataFrame low = loadCsv(dataDir + "/low.csv");
    DataFrame close = loadCsv(dataDir + "/close.csv");
    DataFrame volume = loadCsv(dataDir + "/volume.csv");

    if (open.rows() == 0 || high.rows() == 0 || low.rows() == 0 ||
        close.rows() == 0 || volume.rows() == 0) {
        std::cerr << "加载数据失败!" << std::endl;
        return 1;
    }

    std::cout << "数据加载成功: " << open.rows() << " 行, " << open.cols() << " 列" << std::endl;

    // 创建分组标签
    DataFrame group = DataFrame::Zero(close.rows(), close.cols());
    for (int j = 0; j < close.cols(); ++j) {
        group.col(j).setConstant(j % 5); // 将列分为5组
    }

    // 测量各种算子的执行时间
    std::map<std::string, double> timings;

    std::cout << "\n开始性能测试..." << std::endl;
    std::cout << "算子执行时间统计 (微秒):" << std::endl;
    std::cout << "==================================================" << std::endl;
    std::cout << std::setw(20) << "算子名称" << std::setw(15) << "执行时间(微秒)" << std::endl;
    std::cout << "--------------------------------------------------" << std::endl;

    // 基本算术运算
    timings["Add"] = measureExecutionTime([&]() {
        DataFrame result = Add(close, open);
        volatile double dummy = result(0, 0);  // 防止编译器优化
    }, iterations);
    std::cout << std::setw(20) << "Add" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Add"] << std::endl;

    timings["Minus"] = measureExecutionTime([&]() {
        DataFrame result = Minus(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Minus" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Minus"] << std::endl;

    timings["Multiply"] = measureExecutionTime([&]() {
        DataFrame result = Multiply(close, volume);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Multiply" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Multiply"] << std::endl;

    timings["Divide"] = measureExecutionTime([&]() {
        DataFrame result = Divide(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Divide" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Divide"] << std::endl;

    timings["Sqrt"] = measureExecutionTime([&]() {
        DataFrame result = Sqrt(volume);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Sqrt" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Sqrt"] << std::endl;

    timings["Log"] = measureExecutionTime([&]() {
        DataFrame result = Log(volume);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Log" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Log"] << std::endl;

    timings["Abs"] = measureExecutionTime([&]() {
        DataFrame result = Abs(Minus(close, open));
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Abs" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Abs"] << std::endl;

    timings["Max"] = measureExecutionTime([&]() {
        DataFrame result = Max(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Max" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Max"] << std::endl;

    timings["Min"] = measureExecutionTime([&]() {
        DataFrame result = Min(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Min" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Min"] << std::endl;

    // 逻辑运算
    timings["And"] = measureExecutionTime([&]() {
        DataFrame result = And(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "And" << std::setw(15) << std::fixed << std::setprecision(3) << timings["And"] << std::endl;

    timings["Or"] = measureExecutionTime([&]() {
        DataFrame result = Or(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Or" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Or"] << std::endl;

    timings["Not"] = measureExecutionTime([&]() {
        DataFrame result = Not(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Not" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Not"] << std::endl;

    timings["Xor"] = measureExecutionTime([&]() {
        DataFrame result = Xor(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Xor" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Xor"] << std::endl;

    // 时间序列运算
    timings["ts_Delay"] = measureExecutionTime([&]() {
        DataFrame result = ts_Delay(close, 5);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Delay" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Delay"] << std::endl;

    timings["ts_Mean"] = measureExecutionTime([&]() {
        DataFrame result = ts_Mean(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Mean" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Mean"] << std::endl;

    timings["ts_Sum"] = measureExecutionTime([&]() {
        DataFrame result = ts_Sum(volume, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Sum" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Sum"] << std::endl;

    timings["ts_Stdev"] = measureExecutionTime([&]() {
        DataFrame result = ts_Stdev(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Stdev" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Stdev"] << std::endl;

    timings["ts_Corr"] = measureExecutionTime([&]() {
        DataFrame result = ts_Corr(close, volume, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Corr" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Corr"] << std::endl;

    timings["ts_Min"] = measureExecutionTime([&]() {
        DataFrame result = ts_Min(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Min" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Min"] << std::endl;

    timings["ts_Max"] = measureExecutionTime([&]() {
        DataFrame result = ts_Max(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Max" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Max"] << std::endl;

    // 面板运算
    timings["pn_Mean"] = measureExecutionTime([&]() {
        DataFrame result = pn_Mean(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_Mean" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_Mean"] << std::endl;

    timings["pn_Rank"] = measureExecutionTime([&]() {
        DataFrame result = pn_Rank(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_Rank" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_Rank"] << std::endl;

    timings["pn_Stand"] = measureExecutionTime([&]() {
        DataFrame result = pn_Stand(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_Stand" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_Stand"] << std::endl;

    timings["pn_GroupRank"] = measureExecutionTime([&]() {
        DataFrame result = pn_GroupRank(close, group);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_GroupRank" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_GroupRank"] << std::endl;

    // Tot系列函数
    timings["Tot_Mean"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Mean(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Mean" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Mean"] << std::endl;

    timings["Tot_Sum"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Sum(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Sum" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Sum"] << std::endl;

    // 更多基本算术运算
    timings["inv"] = measureExecutionTime([&]() {
        DataFrame result = inv(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "inv" << std::setw(15) << std::fixed << std::setprecision(3) << timings["inv"] << std::endl;

    timings["Power"] = measureExecutionTime([&]() {
        DataFrame result = Power(close, 2);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Power" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Power"] << std::endl;

    timings["Sign"] = measureExecutionTime([&]() {
        DataFrame result = Sign(Minus(close, open));
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Sign" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Sign"] << std::endl;

    timings["Exp"] = measureExecutionTime([&]() {
        DataFrame result = Exp(Log(close));
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Exp" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Exp"] << std::endl;

    timings["Reverse"] = measureExecutionTime([&]() {
        DataFrame result = Reverse(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Reverse" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Reverse"] << std::endl;

    timings["Floor"] = measureExecutionTime([&]() {
        DataFrame result = Floor(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Floor" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Floor"] << std::endl;

    timings["SignedPower"] = measureExecutionTime([&]() {
        DataFrame result = SignedPower(close, 2.0);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "SignedPower" << std::setw(15) << std::fixed << std::setprecision(3) << timings["SignedPower"] << std::endl;

    timings["Softsign"] = measureExecutionTime([&]() {
        DataFrame result = Softsign(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Softsign" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Softsign"] << std::endl;

    // 比较运算
    timings["Equal"] = measureExecutionTime([&]() {
        DataFrame result = Equal(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Equal" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Equal"] << std::endl;

    timings["Mthan"] = measureExecutionTime([&]() {
        DataFrame result = Mthan(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Mthan" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Mthan"] << std::endl;

    timings["Lthan"] = measureExecutionTime([&]() {
        DataFrame result = Lthan(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Lthan" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Lthan"] << std::endl;

    // 更多时间序列运算
    timings["ts_Delta"] = measureExecutionTime([&]() {
        DataFrame result = ts_Delta(close, 5);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Delta" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Delta"] << std::endl;

    timings["ts_Divide"] = measureExecutionTime([&]() {
        DataFrame result = ts_Divide(close, 5);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Divide" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Divide"] << std::endl;

    timings["ts_ChgRate"] = measureExecutionTime([&]() {
        DataFrame result = ts_ChgRate(close, 5);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_ChgRate" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_ChgRate"] << std::endl;

    timings["ts_Rank"] = measureExecutionTime([&]() {
        DataFrame result = ts_Rank(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Rank" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Rank"] << std::endl;

    timings["ts_Median"] = measureExecutionTime([&]() {
        DataFrame result = ts_Median(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Median" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Median"] << std::endl;

    timings["ts_Cov"] = measureExecutionTime([&]() {
        DataFrame result = ts_Cov(close, volume, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Cov" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Cov"] << std::endl;

    timings["ts_Skewness"] = measureExecutionTime([&]() {
        DataFrame result = ts_Skewness(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Skewness" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Skewness"] << std::endl;

    timings["ts_Kurtosis"] = measureExecutionTime([&]() {
        DataFrame result = ts_Kurtosis(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Kurtosis" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Kurtosis"] << std::endl;

    timings["ts_Scale"] = measureExecutionTime([&]() {
        DataFrame result = ts_Scale(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Scale" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Scale"] << std::endl;

    timings["ts_Product"] = measureExecutionTime([&]() {
        DataFrame result = ts_Product(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Product" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Product"] << std::endl;

    timings["ts_TransNorm"] = measureExecutionTime([&]() {
        DataFrame result = ts_TransNorm(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_TransNorm" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_TransNorm"] << std::endl;

    timings["ts_Decay"] = measureExecutionTime([&]() {
        DataFrame result = ts_Decay(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Decay" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Decay"] << std::endl;

    timings["ts_Decay2"] = measureExecutionTime([&]() {
        DataFrame result = ts_Decay2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Decay2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Decay2"] << std::endl;

    // 添加缺失的原版本算子测试
    timings["ts_Argmax"] = measureExecutionTime([&]() {
        DataFrame result = ts_Argmax(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Argmax" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Argmax"] << std::endl;

    timings["ts_Argmin"] = measureExecutionTime([&]() {
        DataFrame result = ts_Argmin(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Argmin" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Argmin"] << std::endl;

    timings["ts_MaxDD"] = measureExecutionTime([&]() {
        DataFrame result = ts_MaxDD(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_MaxDD" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_MaxDD"] << std::endl;

    timings["ts_MeanChg"] = measureExecutionTime([&]() {
        DataFrame result = ts_MeanChg(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_MeanChg" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_MeanChg"] << std::endl;

    timings["ts_Quantile_A"] = measureExecutionTime([&]() {
        DataFrame result = ts_Quantile(close, 10, 'A');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Quantile_A" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Quantile_A"] << std::endl;

    timings["ts_Quantile_B"] = measureExecutionTime([&]() {
        DataFrame result = ts_Quantile(close, 10, 'B');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Quantile_B" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Quantile_B"] << std::endl;

    timings["ts_Quantile_C"] = measureExecutionTime([&]() {
        DataFrame result = ts_Quantile(close, 10, 'C');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Quantile_C" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Quantile_C"] << std::endl;

    timings["ts_Quantile_D"] = measureExecutionTime([&]() {
        DataFrame result = ts_Quantile(close, 10, 'D');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Quantile_D" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Quantile_D"] << std::endl;

    // 更多面板运算
    // timings["pn_TransNorm"] = measureExecutionTime([&]() {
    //     DataFrame result = pn_TransNorm(close);
    //     volatile double dummy = result(0, 0);
    // }, iterations);
    // std::cout << std::setw(20) << "pn_TransNorm" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_TransNorm"] << std::endl;

    timings["pn_Rank2"] = measureExecutionTime([&]() {
        DataFrame result = pn_Rank2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_Rank2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_Rank2"] << std::endl;

    timings["pn_RankCentered"] = measureExecutionTime([&]() {
        DataFrame result = pn_RankCentered(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_RankCentered" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_RankCentered"] << std::endl;

    // timings["pn_FillMax"] = measureExecutionTime([&]() {
    //     DataFrame result = pn_FillMax(close);
    //     volatile double dummy = result(0, 0);
    // }, iterations);
    // std::cout << std::setw(20) << "pn_FillMax" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_FillMax"] << std::endl;

    // timings["pn_FillMin"] = measureExecutionTime([&]() {
    //     DataFrame result = pn_FillMin(close);
    //     volatile double dummy = result(0, 0);
    // }, iterations);
    // std::cout << std::setw(20) << "pn_FillMin" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_FillMin"] << std::endl;

    timings["pn_TransStd"] = measureExecutionTime([&]() {
        DataFrame result = pn_TransStd(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_TransStd" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_TransStd"] << std::endl;

    timings["pn_Winsor"] = measureExecutionTime([&]() {
        DataFrame result = pn_Winsor(close, 3.0);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_Winsor" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_Winsor"] << std::endl;

    // timings["pn_Cut"] = measureExecutionTime([&]() {
    //     DataFrame result = pn_Cut(close);
    //     volatile double dummy = result(0, 0);
    // }, iterations);
    // std::cout << std::setw(20) << "pn_Cut" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_Cut"] << std::endl;

    timings["pn_CrossFit"] = measureExecutionTime([&]() {
        DataFrame result = pn_CrossFit(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_CrossFit" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_CrossFit"] << std::endl;

    timings["pn_GroupNorm"] = measureExecutionTime([&]() {
        DataFrame result = pn_GroupNorm(close, group);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_GroupNorm" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_GroupNorm"] << std::endl;

    timings["pn_GroupNeutral"] = measureExecutionTime([&]() {
        DataFrame result = pn_GroupNeutral(close, group);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_GroupNeutral" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_GroupNeutral"] << std::endl;

    // 更多Tot系列函数
    timings["Tot_Stdev"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Stdev(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Stdev" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Stdev"] << std::endl;

    timings["Tot_Delta"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Delta(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Delta" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Delta"] << std::endl;

    timings["Tot_Divide"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Divide(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Divide" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Divide"] << std::endl;

    timings["Tot_ChgRate"] = measureExecutionTime([&]() {
        DataFrame result = Tot_ChgRate(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_ChgRate" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_ChgRate"] << std::endl;

    timings["Tot_Rank"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Rank(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Rank" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Rank"] << std::endl;

    timings["Tot_ArgMax"] = measureExecutionTime([&]() {
        DataFrame result = Tot_ArgMax(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_ArgMax" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_ArgMax"] << std::endl;

    timings["Tot_ArgMin"] = measureExecutionTime([&]() {
        DataFrame result = Tot_ArgMin(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_ArgMin" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_ArgMin"] << std::endl;

    timings["Tot_Max"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Max(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Max" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Max"] << std::endl;

    timings["Tot_Min"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Min(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Min" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Min"] << std::endl;

    // 数据工具函数
    timings["FilterInf"] = measureExecutionTime([&]() {
        DataFrame result = FilterInf(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "FilterInf" << std::setw(15) << std::fixed << std::setprecision(3) << timings["FilterInf"] << std::endl;

    timings["FillNan"] = measureExecutionTime([&]() {
        DataFrame result = FillNan(close, 0.0);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "FillNan" << std::setw(15) << std::fixed << std::setprecision(3) << timings["FillNan"] << std::endl;

    // 重构版本的时间序列运算 (v2) - 性能对比测试
    std::cout << "\n=== 重构版本 (v2) 性能测试 ===" << std::endl;

    timings["ts_Delay_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Delay_v2(close, 5);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Delay_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Delay_v2"] << std::endl;

    timings["ts_Mean_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Mean_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Mean_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Mean_v2"] << std::endl;

    timings["ts_Sum_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Sum_v2(volume, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Sum_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Sum_v2"] << std::endl;

    timings["ts_Stdev_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Stdev_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Stdev_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Stdev_v2"] << std::endl;

    timings["ts_Corr_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Corr_v2(close, volume, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Corr_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Corr_v2"] << std::endl;

    timings["ts_Min_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Min_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Min_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Min_v2"] << std::endl;

    timings["ts_Max_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Max_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Max_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Max_v2"] << std::endl;

    timings["ts_Delta_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Delta_v2(close, 5);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Delta_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Delta_v2"] << std::endl;

    timings["ts_Divide_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Divide_v2(close, 5);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Divide_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Divide_v2"] << std::endl;

    timings["ts_ChgRate_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_ChgRate_v2(close, 5);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_ChgRate_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_ChgRate_v2"] << std::endl;

    timings["ts_Rank_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Rank_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Rank_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Rank_v2"] << std::endl;

    timings["ts_Median_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Median_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Median_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Median_v2"] << std::endl;

    timings["ts_Cov_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Cov_v2(close, volume, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Cov_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Cov_v2"] << std::endl;

    timings["ts_Skewness_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Skewness_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Skewness_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Skewness_v2"] << std::endl;

    timings["ts_Kurtosis_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Kurtosis_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Kurtosis_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Kurtosis_v2"] << std::endl;

    timings["ts_Scale_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Scale_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Scale_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Scale_v2"] << std::endl;

    timings["ts_Product_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Product_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Product_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Product_v2"] << std::endl;

    timings["ts_Decay_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Decay_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Decay_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Decay_v2"] << std::endl;

    timings["ts_Decay2_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Decay2_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Decay2_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Decay2_v2"] << std::endl;

    timings["ts_Argmax_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Argmax_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Argmax_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Argmax_v2"] << std::endl;

    timings["ts_Argmin_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Argmin_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Argmin_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Argmin_v2"] << std::endl;

    timings["ts_MaxDD_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_MaxDD_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_MaxDD_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_MaxDD_v2"] << std::endl;

    timings["ts_MeanChg_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_MeanChg_v2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_MeanChg_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_MeanChg_v2"] << std::endl;

    // ts_Quantile_v2 系列
    timings["ts_Quantile_A_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Quantile_v2(close, 10, 'A');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Quantile_A_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Quantile_A_v2"] << std::endl;

    timings["ts_Quantile_B_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Quantile_v2(close, 10, 'B');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Quantile_B_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Quantile_B_v2"] << std::endl;

    timings["ts_Quantile_C_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Quantile_v2(close, 10, 'C');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Quantile_C_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Quantile_C_v2"] << std::endl;

    timings["ts_Quantile_D_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::ts_Quantile_v2(close, 10, 'D');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Quantile_D_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Quantile_D_v2"] << std::endl;

    // Tot系列函数 v2
    timings["Tot_Mean_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::Tot_Mean_v2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Mean_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Mean_v2"] << std::endl;

    timings["Tot_Sum_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::Tot_Sum_v2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Sum_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Sum_v2"] << std::endl;

    // 添加缺失的 Tot 系列 v2 函数测试
    timings["Tot_Stdev_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::Tot_Stdev_v2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Stdev_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Stdev_v2"] << std::endl;

    timings["Tot_Delta_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::Tot_Delta_v2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Delta_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Delta_v2"] << std::endl;

    timings["Tot_Divide_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::Tot_Divide_v2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Divide_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Divide_v2"] << std::endl;

    timings["Tot_ChgRate_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::Tot_ChgRate_v2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_ChgRate_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_ChgRate_v2"] << std::endl;

    timings["Tot_Rank_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::Tot_Rank_v2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Rank_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Rank_v2"] << std::endl;

    timings["Tot_ArgMax_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::Tot_ArgMax_v2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_ArgMax_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_ArgMax_v2"] << std::endl;

    timings["Tot_ArgMin_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::Tot_ArgMin_v2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_ArgMin_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_ArgMin_v2"] << std::endl;

    timings["Tot_Max_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::Tot_Max_v2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Max_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Max_v2"] << std::endl;

    timings["Tot_Min_v2"] = measureExecutionTime([&]() {
        DataFrame result = v2::Tot_Min_v2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Min_v2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Min_v2"] << std::endl;

    // 保存结果
    saveResultsToJson(timings, outputFile);

    std::cout << "\n性能测试完成!" << std::endl;
    std::cout << "总共测试了 " << timings.size() << " 个算子" << std::endl;
    return 0;
}
