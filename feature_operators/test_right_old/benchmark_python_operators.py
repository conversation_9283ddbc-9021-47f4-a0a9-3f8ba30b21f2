#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import numpy as np
import pandas as pd
import time
import json

# 添加当前目录到路径
sys.path.append('./')

# 导入Python因子计算模块
from feature_operator_funcs import *

def load_csv_data(data_dir):
    """从CSV文件加载数据"""
    try:
        open_df = pd.read_csv(os.path.join(data_dir, "open.csv"), index_col=0)
        high_df = pd.read_csv(os.path.join(data_dir, "high.csv"), index_col=0)
        low_df = pd.read_csv(os.path.join(data_dir, "low.csv"), index_col=0)
        close_df = pd.read_csv(os.path.join(data_dir, "close.csv"), index_col=0)
        volume_df = pd.read_csv(os.path.join(data_dir, "volume.csv"), index_col=0)

        print(f"数据加载成功: {close_df.shape[0]} 行, {close_df.shape[1]} 列")
        return open_df, high_df, low_df, close_df, volume_df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None, None, None, None, None

def measure_python_operator_time(open_df, high_df, low_df, close_df, volume_df, output_file, iterations=100):
    """测量Python算子的执行时间"""

    # 创建分组标签
    group_labels = pd.DataFrame(np.zeros(close_df.shape, dtype=int),
                               index=close_df.index,
                               columns=close_df.columns)
    # 按列索引模5分组
    for j in range(group_labels.shape[1]):
        group_labels.iloc[:, j] = j % 5

    # 定义要测试的算子
    operators = {
        # 基本算术运算
        'Add': lambda: Add(close_df, open_df),
        'Minus': lambda: Minus(close_df, open_df),
        'Multiply': lambda: Multiply(close_df, volume_df),
        'Divide': lambda: Divide(close_df, open_df),
        'Sqrt': lambda: Sqrt(volume_df),
        'Log': lambda: Log(volume_df),
        'Abs': lambda: Abs(Minus(close_df, open_df)),
        'Max': lambda: Max(close_df, open_df),
        'Min': lambda: Min(close_df, open_df),

        # 逻辑运算
        'And': lambda: And(close_df, open_df),
        'Or': lambda: Or(close_df, open_df),
        'Not': lambda: Not(close_df),
        'Xor': lambda: Xor(close_df, open_df),

        # 时间序列运算
        'ts_Delay': lambda: ts_Delay(close_df, 5),
        'ts_Mean': lambda: ts_Mean(close_df, 10),
        'ts_Sum': lambda: ts_Sum(volume_df, 10),
        'ts_Stdev': lambda: ts_Stdev(close_df, 10),
        'ts_Corr': lambda: ts_Corr(close_df, volume_df, 10),
        'ts_Min': lambda: ts_Min(close_df, 10),
        'ts_Max': lambda: ts_Max(close_df, 10),

        # 面板运算
        'pn_Mean': lambda: pn_Mean(close_df),
        'pn_Rank': lambda: pn_Rank(close_df),
        'pn_Stand': lambda: pn_Stand(close_df),
        'pn_GroupRank': lambda: pn_GroupRank(close_df, group_labels),

        # Tot系列函数
        'Tot_Mean': lambda: Tot_Mean(close_df),
        'Tot_Sum': lambda: Tot_Sum(close_df),
        'Tot_Stdev': lambda: Tot_Stdev(close_df),
        'Tot_Delta': lambda: Tot_Delta(close_df),
        'Tot_Divide': lambda: Tot_Divide(close_df),
        'Tot_ChgRate': lambda: Tot_ChgRate(close_df),
        'Tot_Rank': lambda: Tot_Rank(close_df),
        'Tot_ArgMax': lambda: Tot_ArgMax(close_df),
        'Tot_ArgMin': lambda: Tot_ArgMin(close_df),
        'Tot_Max': lambda: Tot_Max(close_df),
        'Tot_Min': lambda: Tot_Min(close_df),

        # 更多基本算术运算
        'inv': lambda: inv(close_df),
        'Power': lambda: Power(close_df, 2),
        'Sign': lambda: Sign(Minus(close_df, open_df)),
        'Exp': lambda: Exp(Log(close_df)),
        'Reverse': lambda: Reverse(close_df),
        'Floor': lambda: Floor(close_df),
        'SignedPower': lambda: SignedPower(close_df, 2.0),
        'Softsign': lambda: Softsign(close_df),

        # 比较运算
        'Equal': lambda: Equal(close_df, open_df),
        'Mthan': lambda: Mthan(close_df, open_df),
        'Lthan': lambda: Lthan(close_df, open_df),

        # 更多时间序列运算
        'ts_Delta': lambda: ts_Delta(close_df, 5),
        'ts_Divide': lambda: ts_Divide(close_df, 5),
        'ts_ChgRate': lambda: ts_ChgRate(close_df, 5),
        'ts_Rank': lambda: ts_Rank(close_df, 10),
        'ts_Median': lambda: ts_Median(close_df, 10),
        'ts_Cov': lambda: ts_Cov(close_df, volume_df, 10),
        'ts_Skewness': lambda: ts_Skewness(close_df, 10),
        'ts_Kurtosis': lambda: ts_Kurtosis(close_df, 10),
        'ts_Scale': lambda: ts_Scale(close_df, 10),
        'ts_Product': lambda: ts_Product(close_df, 10),
        'ts_TransNorm': lambda: ts_TransNorm(close_df, 10),
        'ts_Decay': lambda: ts_Decay(close_df, 10),
        'ts_Decay2': lambda: ts_Decay2(close_df, 10),

        # 更多面板运算
        'pn_TransNorm': lambda: pn_TransNorm(close_df),
        'pn_Rank2': lambda: pn_Rank2(close_df),
        'pn_RankCentered': lambda: pn_RankCentered(close_df),
        'pn_FillMax': lambda: pn_FillMax(close_df),
        'pn_FillMin': lambda: pn_FillMin(close_df),
        'pn_TransStd': lambda: pn_TransStd(close_df),
        'pn_Winsor': lambda: pn_Winsor(close_df, 3.0),
        'pn_Cut': lambda: pn_Cut(close_df),
        'pn_CrossFit': lambda: pn_CrossFit(close_df, open_df),
        'pn_GroupNorm': lambda: pn_GroupNorm(close_df, group_labels),
        'pn_GroupNeutral': lambda: pn_GroupNeutral(close_df, group_labels),

        # 数据工具函数
        'FilterInf': lambda: FilterInf(close_df),
        'FillNan': lambda: FillNan(close_df, 0.0),
    }

    # 测量每个算子的执行时间
    python_times = {}

    print("\n开始性能测试...")
    print("Python算子执行时间 (微秒):")
    print("=" * 50)
    print(f"{'算子名称':<20} {'执行时间(微秒)':<15}")
    print("-" * 50)

    for op_name, op_func in operators.items():
        try:
            # 预热
            result = op_func()
            # 确保结果被计算但不被优化掉
            if isinstance(result, pd.DataFrame):
                dummy = result.iloc[0, 0] if not result.empty else 0

            # 测量总时间
            start_time = time.time()
            for _ in range(iterations):
                result = op_func()
                # 确保结果被计算但不被优化掉
                if isinstance(result, pd.DataFrame):
                    dummy = result.iloc[0, 0] if not result.empty else 0
            end_time = time.time()

            # 计算平均时间
            total_time = (end_time - start_time) * 1e6  # 转换为微秒
            avg_time = total_time / iterations
            python_times[op_name] = avg_time

            # 打印结果
            print(f"{op_name:<20} {avg_time:.3f}")

        except Exception as e:
            print(f"{op_name:<20} ERROR: {e}")
            python_times[op_name] = -1  # 标记错误

    # 保存结果到JSON文件
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    with open(output_file, 'w') as f:
        json.dump(python_times, f, indent=2)

    print(f"\n结果已保存到: {output_file}")
    return python_times

def main():
    import argparse

    parser = argparse.ArgumentParser(description='Python算子性能测试')
    parser.add_argument('--iterations', type=int, default=100, help='迭代次数')
    parser.add_argument('--data-dir', type=str, default='test_data', help='数据目录')
    parser.add_argument('--output', type=str, default='test_results/benchmark_python_results.json', help='输出文件')
    args = parser.parse_args()

    print(f"从目录加载数据: {args.data_dir}")
    print(f"迭代次数: {args.iterations}")

    # 加载数据
    open_df, high_df, low_df, close_df, volume_df = load_csv_data(args.data_dir)

    if close_df is None:
        print("数据加载失败，退出程序")
        return 1

    # 测量Python算子执行时间
    measure_python_operator_time(
        open_df, high_df, low_df, close_df, volume_df,
        args.output, iterations=args.iterations
    )

    print("\n性能测试完成!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
