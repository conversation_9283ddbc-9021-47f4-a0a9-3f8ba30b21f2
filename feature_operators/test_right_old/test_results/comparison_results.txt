在所有实现中找到 131 个唯一因子
C++: 131 个因子
Python: 89 个因子

比较结果:
================================================================================
因子                   C++        Python     C++ vs Python            
--------------------------------------------------------------------------------
Abs                  ✓          ✓          ✓                        
Add                  ✓          ✓          ✓                        
And                  ✓          ✓          ✓                        
Ceil                 ✓          ✓          ✗ (文件不相等 (最大差异: 1.00e+00 > 容差: 1.00e-08))
Divide               ✓          ✓          ✓                        
Equal                ✓          ✓          ✓                        
Exp                  ✓          ✓          ✓                        
FillNan              ✓          ✗          N/A                      
FilterInf            ✓          ✓          ✓                        
Floor                ✓          ✓          ✓                        
LEthan               ✓          ✓          ✓                        
Log                  ✓          ✓          ✓                        
Lthan                ✓          ✓          ✓                        
MEthan               ✓          ✓          ✓                        
Max                  ✓          ✓          ✓                        
Min                  ✓          ✓          ✓                        
Minus                ✓          ✓          ✓                        
Mthan                ✓          ✓          ✓                        
Multiply             ✓          ✓          ✓                        
Not                  ✓          ✓          ✓                        
Or                   ✓          ✓          ✓                        
Power                ✓          ✓          ✗ (文件不相等 (最大差异: 1.00e-03 > 容差: 1.00e-08))
Reverse              ✓          ✓          ✓                        
Round                ✓          ✓          ✗ (文件不相等 (最大差异: 1.00e+00 > 容差: 1.00e-08))
Sign                 ✓          ✓          ✓                        
SignedPower          ✓          ✓          ✓                        
Softsign             ✓          ✓          ✓                        
Sqrt                 ✓          ✓          ✓                        
Tot_ArgMax           ✓          ✓          ✓                        
Tot_ArgMax_v2        ✓          ✗          N/A                      
Tot_ArgMin           ✓          ✓          ✓                        
Tot_ArgMin_v2        ✓          ✗          N/A                      
Tot_ChgRate          ✓          ✓          ✓                        
Tot_ChgRate_v2       ✓          ✗          N/A                      
Tot_Delta            ✓          ✓          ✓                        
Tot_Delta_v2         ✓          ✗          N/A                      
Tot_Divide           ✓          ✓          ✓                        
Tot_Divide_v2        ✓          ✗          N/A                      
Tot_Max              ✓          ✓          ✓                        
Tot_Max_v2           ✓          ✗          N/A                      
Tot_Mean             ✓          ✓          ✓                        
Tot_Mean_v2          ✓          ✗          N/A                      
Tot_Min              ✓          ✓          ✓                        
Tot_Min_v2           ✓          ✗          N/A                      
Tot_Rank             ✓          ✓          ✓                        
Tot_Rank_v2          ✓          ✗          N/A                      
Tot_Stdev            ✓          ✓          ✓                        
Tot_Stdev_v2         ✓          ✗          N/A                      
Tot_Sum              ✓          ✓          ✓                        
Tot_Sum_v2           ✓          ✗          N/A                      
UnEqual              ✓          ✗          N/A                      
Xor                  ✓          ✓          ✓                        
getInf               ✓          ✗          N/A                      
getNan               ✓          ✗          N/A                      
inv                  ✓          ✓          ✓                        
pn_CrossFit          ✓          ✓          ✓                        
pn_Cut               ✓          ✓          ✓                        
pn_FillMax           ✓          ✓          ✓                        
pn_FillMin           ✓          ✓          ✓                        
pn_GroupNeutral      ✓          ✓          ✗ (文件不相等 (最大差异: 1.00e-07 > 容差: 1.00e-08))
pn_GroupNorm         ✓          ✓          ✓                        
pn_GroupRank         ✓          ✓          ✓                        
pn_Mean              ✓          ✓          ✓                        
pn_Rank              ✓          ✓          ✓                        
pn_Rank2             ✓          ✓          ✓                        
pn_RankCentered      ✓          ✓          ✓                        
pn_Stand             ✓          ✓          ✓                        
pn_TransNorm         ✓          ✓          ✗ (文件不相等 (最大差异: 5.21e+00 > 容差: 1.00e-08))
pn_TransStd          ✓          ✓          ✓                        
pn_Winsor            ✓          ✓          ✓                        
ts_Argmax            ✓          ✓          ✓                        
ts_Argmax_v2         ✓          ✗          N/A                      
ts_Argmin            ✓          ✓          ✓                        
ts_Argmin_v2         ✓          ✗          N/A                      
ts_ChgRate           ✓          ✓          ✓                        
ts_ChgRate_v2        ✓          ✗          N/A                      
ts_Corr              ✓          ✓          ✗ (文件不相等 (最大差异: 9.92e-05 > 容差: 1.00e-08))
ts_Corr_v2           ✓          ✗          N/A                      
ts_Cov               ✓          ✓          ✓                        
ts_Cov_v2            ✓          ✗          N/A                      
ts_Decay             ✓          ✓          ✓                        
ts_Decay2            ✓          ✓          ✓                        
ts_Decay2_v2         ✓          ✗          N/A                      
ts_Decay_v2          ✓          ✗          N/A                      
ts_Delay             ✓          ✓          ✓                        
ts_Delay_v2          ✓          ✗          N/A                      
ts_Delta             ✓          ✓          ✓                        
ts_Delta_v2          ✓          ✗          N/A                      
ts_Divide            ✓          ✓          ✓                        
ts_Divide_v2         ✓          ✗          N/A                      
ts_Entropy           ✓          ✓          ✗ (文件不相等 (最大差异: 4.19e-02 > 容差: 1.00e-08))
ts_Kurtosis          ✓          ✓          ✗ (文件不相等 (最大差异: 3.28e+04 > 容差: 1.00e-08))
ts_Kurtosis_v2       ✓          ✗          N/A                      
ts_Max               ✓          ✓          ✓                        
ts_MaxDD             ✓          ✓          ✓                        
ts_MaxDD_v2          ✓          ✗          N/A                      
ts_Max_v2            ✓          ✗          N/A                      
ts_Mean              ✓          ✓          ✓                        
ts_MeanChg           ✓          ✓          ✓                        
ts_MeanChg_v2        ✓          ✗          N/A                      
ts_Mean_v2           ✓          ✗          N/A                      
ts_Median            ✓          ✓          ✓                        
ts_Median_v2         ✓          ✗          N/A                      
ts_Min               ✓          ✓          ✓                        
ts_Min_v2            ✓          ✗          N/A                      
ts_Partial_corr      ✓          ✓          ✗ (文件不相等 (最大差异: 4.30e+00 > 容差: 1.00e-08))
ts_Product           ✓          ✓          ✓                        
ts_Product_v2        ✓          ✗          N/A                      
ts_Quantile_A        ✓          ✓          ✓                        
ts_Quantile_A_v2     ✓          ✗          N/A                      
ts_Quantile_B        ✓          ✓          ✓                        
ts_Quantile_B_v2     ✓          ✗          N/A                      
ts_Quantile_C        ✓          ✓          ✓                        
ts_Quantile_C_v2     ✓          ✗          N/A                      
ts_Quantile_D        ✓          ✓          ✓                        
ts_Quantile_D_v2     ✓          ✗          N/A                      
ts_Rank              ✓          ✓          ✓                        
ts_Rank_v2           ✓          ✗          N/A                      
ts_Regression_A      ✓          ✓          ✗ (文件不相等 (最大差异: 7.23e-02 > 容差: 1.00e-08))
ts_Regression_B      ✓          ✓          ✗ (文件不相等 (最大差异: 2.32e+02 > 容差: 1.00e-08))
ts_Regression_C      ✓          ✓          ✗ (文件不相等 (最大差异: 5.79e-04 > 容差: 1.00e-08))
ts_Regression_D      ✓          ✓          ✗ (文件不相等 (最大差异: 5.79e-04 > 容差: 1.00e-08))
ts_Scale             ✓          ✓          ✓                        
ts_Scale_v2          ✓          ✗          N/A                      
ts_Skewness          ✓          ✓          ✗ (文件不相等 (最大差异: 2.19e-01 > 容差: 1.00e-08))
ts_Skewness_v2       ✓          ✗          N/A                      
ts_Stdev             ✓          ✓          ✓                        
ts_Stdev_v2          ✓          ✗          N/A                      
ts_Sum               ✓          ✓          ✓                        
ts_Sum_v2            ✓          ✗          N/A                      
ts_TransNorm         ✓          ✓          ✓                        


摘要:
C++ vs Python: 75/89 个因子匹配 (84.3%)

详细统计信息:

数值不一致的因子:
  Ceil (cpp_vs_py): 最大差异 1.00e+00，平均差异 9.91e-01，中位数差异 1.00e+00，形状 (500, 518)，共 259000 个元素
  Power (cpp_vs_py): 最大差异 1.00e-03，平均差异 5.41e-08，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  Round (cpp_vs_py): 最大差异 1.00e+00，平均差异 6.80e-04，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  pn_GroupNeutral (cpp_vs_py): 最大差异 1.00e-07，平均差异 7.07e-11，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  pn_TransNorm (cpp_vs_py): 最大差异 5.21e+00，平均差异 1.69e+00，中位数差异 1.03e+00，形状 (500, 518)，共 259000 个元素
  ts_Corr (cpp_vs_py): 最大差异 9.92e-05，平均差异 3.57e-09，中位数差异 1.00e-11，形状 (500, 518)，共 259000 个元素
  ts_Entropy (cpp_vs_py): 最大差异 4.19e-02，平均差异 4.74e-05，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  ts_Kurtosis (cpp_vs_py): 最大差异 3.28e+04，平均差异 1.83e-01，中位数差异 3.48e-06，形状 (500, 518)，共 259000 个元素
  ts_Partial_corr (cpp_vs_py): 最大差异 4.30e+00，平均差异 7.71e-05，中位数差异 3.40e-10，形状 (500, 518)，共 259000 个元素
  ts_Regression_A (cpp_vs_py): 最大差异 7.23e-02，平均差异 1.13e-06，中位数差异 2.30e-10，形状 (500, 518)，共 259000 个元素
  ts_Regression_B (cpp_vs_py): 最大差异 2.32e+02，平均差异 3.61e-03，中位数差异 3.90e-11，形状 (500, 518)，共 259000 个元素
  ts_Regression_C (cpp_vs_py): 最大差异 5.79e-04，平均差异 5.95e-09，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  ts_Regression_D (cpp_vs_py): 最大差异 5.79e-04，平均差异 6.27e-09，中位数差异 4.00e-14，形状 (500, 518)，共 259000 个元素
  ts_Skewness (cpp_vs_py): 最大差异 2.19e-01，平均差异 1.38e-06，中位数差异 4.41e-09，形状 (500, 518)，共 259000 个元素
