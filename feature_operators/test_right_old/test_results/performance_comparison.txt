Feature Operators 性能比较报告
============================================================

测试算子总数: 67
C++平均执行时间: 8418.379 μs
Python平均执行时间: 30041.767 μs
平均加速比: 6.82x

性能排名 (按加速比排序):
------------------------------------------------------------
排名   算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
1    <USER>              <GROUP>.065     190843.487      38.06     
2    ts_Cov               2760.686     102153.381      37.00     
3    Min                  288.675      10125.049       35.07     
4    Max                  290.734      10125.287       34.83     
5    Xor                  495.171      14040.295       28.35     
6    Not                  326.235      8891.789        27.26     
7    Or                   496.680      11169.179       22.49     
8    And                  496.272      9781.814        19.71     
9    pn_GroupNeutral      4599.516     72547.873       15.77     
10   Tot_Sum              3000.941     30927.809       10.31     
11   ts_Sum               3002.059     30101.029       10.03     
12   pn_GroupRank         12083.153    117456.460      9.72      
13   Tot_Mean             4148.738     31505.942       7.59      
14   ts_Mean              4161.889     31258.138       7.51      
15   ts_Scale             9495.169     66412.505       6.99      
16   ts_Stdev             6498.957     42571.886       6.55      
17   Tot_Stdev            6481.539     42232.227       6.52      
18   pn_GroupNorm         26576.703    154750.872      5.82      
19   pn_CrossFit          3880.014     21046.154       5.42      
20   Lthan                293.066      1556.325        5.31      
21   Mthan                294.839      1558.820        5.29      
22   ts_Skewness          8591.728     44505.922       5.18      
23   Equal                311.275      1577.854        5.07      
24   Tot_Delta            277.238      1287.468        4.64      
25   ts_Delta             279.615      1289.805        4.61      
26   Tot_ArgMax           10736.047    48994.700       4.56      
27   Tot_ArgMin           10736.066    48711.467       4.54      
28   FilterInf            164.003      712.347         4.34      
29   ts_Kurtosis          11484.224    46422.156       4.04      
30   Abs                  224.172      812.562         3.62      
31   ts_Product           8822.813     31699.594       3.59      
32   Add                  245.040      712.689         2.91      
33   ts_Delay             270.650      763.122         2.82      
34   pn_Winsor            2838.241     7955.503        2.80      
35   Minus                230.478      645.685         2.80      
36   Multiply             232.543      646.019         2.78      
37   Tot_ChgRate          738.277      2017.228        2.73      
38   ts_ChgRate           744.913      1973.295        2.65      
39   Sign                 538.868      1323.414        2.46      
40   ts_Rank              51921.061    126366.218      2.43      
41   ts_Min               13128.375    31412.999       2.39      
42   Tot_Rank             58611.622    133652.314      2.28      
43   Tot_Divide           738.477      1629.130        2.21      
44   ts_TransNorm         66690.041    146637.122      2.20      
45   ts_Divide            745.000      1603.746        2.15      
46   ts_Max               15242.228    31331.960       2.06      
47   ts_Median            63563.075    128272.637      2.02      
48   Reverse              167.073      332.737         1.99      
49   Divide               384.342      730.284         1.90      
50   Log                  4883.710     9075.419        1.86      
51   SignedPower          2632.029     4746.413        1.80      
52   Softsign             1052.895     1884.063        1.79      
53   Floor                164.823      286.436         1.74      
54   Tot_Min              18787.304    31115.055       1.66      
55   Tot_Max              21202.605    31365.816       1.48      
56   inv                  371.060      532.293         1.43      
57   pn_RankCentered      13223.978    18889.467       1.43      
58   pn_TransStd          3178.585     4539.863        1.43      
59   pn_Rank              11750.424    16431.936       1.40      
60   pn_Rank2             11183.198    14869.603       1.33      
61   pn_Mean              1238.673     1527.627        1.23      
62   Sqrt                 643.687      790.302         1.23      
63   Exp                  8838.171     9874.813        1.12      
64   ts_Decay2            8150.058     8399.510        1.03      
65   ts_Decay             8481.277     8428.963        0.99      
66   pn_Stand             9576.664     4625.686        0.48      
67   Power                15329.643    338.856         0.02      

============================================================
分类性能统计:
------------------------------
基本算子 (25个): 平均加速比 8.69x
时间序列算子 (20个): 平均加速比 7.22x
面板算子 (11个): 平均加速比 4.26x
Tot算子 (11个): 平均加速比 4.41x
