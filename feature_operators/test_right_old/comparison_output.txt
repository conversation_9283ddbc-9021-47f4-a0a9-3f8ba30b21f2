=== ts_Corr Python vs C++ 结果比较 ===
Python结果数量: 500
C++结果数量: 500

差异统计:
有效比较数量: 499
最大绝对差异: 4.57e-10
平均绝对差异: 9.71e-12
标准差: 2.79e-11

最大差异位置:
行 213: Python=-0.293317256076993, C++=-0.293317255620238
差异: -4.57e-10

差异分布:
差异 > 1e-15: 499 个 (100.0%)
差异 > 1e-12: 384 个 (77.0%)
差异 > 1e-10: 5 个 (1.0%)
差异 > 1e-08: 0 个 (0.0%)
差异 > 1e-06: 0 个 (0.0%)

前10行详细比较:
Row 0: 都是NaN ✓
Row 1: Python=-0.999999999999968, C++=-1.000000000001711, Diff=1.74e-12
Row 2: Python=-0.649740874478885, C++=-0.649740874497923, Diff=1.90e-11
Row 3: Python=-0.515132748776077, C++=-0.515132748775241, Diff=-8.36e-13
Row 4: Python=-0.445013513583016, C++=-0.445013513538191, Diff=-4.48e-11
Row 5: Python=-0.116886056364262, C++=-0.116886056364713, Diff=4.51e-13
Row 6: Python=0.010839472257844, C++=0.010839472257783, Diff=6.10e-14
Row 7: Python=-0.084586113981986, C++=-0.084586113982094, Diff=1.08e-13
Row 8: Python=0.013303025836075, C++=0.013303025836177, Diff=-1.02e-13
Row 9: Python=0.051256015185274, C++=0.051256015184684, Diff=5.90e-13

特殊值检查:
Python接近-1.0的数量: 1
C++接近-1.0的数量: 1
Python第一个接近-1.0的值 (行1): -0.999999999999968
C++第一个接近-1.0的值 (行1): -1.000000000001711

=== 总结 ===
⚠ 有明显数值差异 (差异 < 1e-6)

主要问题:
1. 最大绝对差异: 4.57e-10
2. 数值精度问题主要出现在接近完美相关性的情况
3. C++版本在Row 1的-1.0值有轻微偏差: -1.71e-12
