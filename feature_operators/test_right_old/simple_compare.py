#!/usr/bin/env python3
import os
import pandas as pd
import numpy as np
import glob

def load_csv(filepath):
    """加载 CSV 文件到 pandas DataFrame。"""
    try:
        # 首先尝试直接加载，不使用索引和标题，强制所有列为浮点数
        df = pd.read_csv(filepath, header=None, dtype=float)
        return df
    except Exception as e:
        try:
            # 如果失败，尝试使用索引加载
            df = pd.read_csv(filepath, index_col=0)
            # 尝试将所有列转换为浮点数
            df = df.astype(float)
            return df
        except Exception as e2:
            try:
                # 如果仍然失败，尝试先加载然后手动处理
                df = pd.read_csv(filepath)
                # 如果第一列看起来像索引，则将其设置为索引
                if df.columns[0] == 'Unnamed: 0' or df.columns[0] == 'index':
                    df = df.set_index(df.columns[0])
                # 尝试将所有列转换为浮点数
                df = df.astype(float)
                return df
            except Exception as e3:
                print(f"加载 {filepath} 出错: {e} -> {e2} -> {e3}")
                return None

def compare_files(file1, file2, tolerance=1e-8):
    """
    比较两个 CSV 文件，检查它们是否相等。

    参数:
        file1, file2: 要比较的CSV文件路径
        tolerance: 数值比较的容差

    返回:
        (is_equal, message, stats):
        - is_equal: 布尔值，表示文件是否相等
        - message: 描述比较结果的字符串
        - stats: 包含详细统计信息的字典
    """
    # 加载文件
    df1 = load_csv(file1)
    df2 = load_csv(file2)

    # 初始化统计信息
    stats = {
        "shape1": None,
        "shape2": None,
        "total_elements": 0,
        "max_diff": float('inf'),
        "mean_diff": float('inf'),
        "median_diff": float('inf')
    }

    # 检查文件加载是否成功
    if df1 is None:
        return False, f"无法加载文件1: {file1}", stats
    if df2 is None:
        return False, f"无法加载文件2: {file2}", stats

    # 记录形状
    stats["shape1"] = df1.shape
    stats["shape2"] = df2.shape

    # 检查形状是否匹配
    if df1.shape != df2.shape:
        return False, f"形状不匹配: {df1.shape} vs {df2.shape}", stats

    stats["total_elements"] = df1.size

    # 如果矩阵为空，直接返回相等
    if df1.size == 0:
        stats.update({"max_diff": 0.0, "mean_diff": 0.0, "median_diff": 0.0})
        return True, "两个文件都是空矩阵", stats

    try:
        # 使用pandas的compare方法检查是否完全相等
        diff_df = df1.compare(df2)

        # 如果compare返回空DataFrame，说明完全相等
        if diff_df.empty:
            stats.update({"max_diff": 0.0, "mean_diff": 0.0, "median_diff": 0.0})
            return True, "文件完全相等", stats

        # 如果不完全相等，计算数值差异
        # 使用numpy计算差异统计
        diff_values = np.abs(df1.values - df2.values)

        # 处理NaN值：如果两个位置都是NaN，差异为0；如果只有一个是NaN，差异为inf
        both_nan = np.isnan(df1.values) & np.isnan(df2.values)
        one_nan = np.isnan(df1.values) ^ np.isnan(df2.values)

        diff_values[both_nan] = 0.0  # 两个都是NaN，差异为0
        diff_values[one_nan] = np.inf  # 只有一个是NaN，差异为无穷大

        # 计算统计信息
        finite_diff = diff_values[np.isfinite(diff_values)]

        if len(finite_diff) == 0:
            # 所有差异都是无穷大，说明有NaN不匹配
            stats.update({"max_diff": np.inf, "mean_diff": np.inf, "median_diff": np.inf})
            return False, "存在NaN位置不匹配", stats

        stats["max_diff"] = float(np.max(finite_diff))
        stats["mean_diff"] = float(np.mean(finite_diff))
        stats["median_diff"] = float(np.median(finite_diff))

        # 判断是否在容差范围内
        if stats["max_diff"] <= tolerance:
            return True, f"文件在容差范围内相等 (最大差异: {stats['max_diff']:.2e})", stats
        else:
            return False, f"文件不相等 (最大差异: {stats['max_diff']:.2e} > 容差: {tolerance:.2e})", stats

    except Exception as e:
        # 如果pandas compare失败，回退到numpy比较
        try:
            diff_values = np.abs(df1.values.astype(float) - df2.values.astype(float))

            # 处理NaN和Inf
            finite_mask = np.isfinite(diff_values)
            if not np.any(finite_mask):
                stats.update({"max_diff": np.inf, "mean_diff": np.inf, "median_diff": np.inf})
                return False, "所有差异都是无穷大或NaN", stats

            finite_diff = diff_values[finite_mask]
            stats["max_diff"] = float(np.max(finite_diff))
            stats["mean_diff"] = float(np.mean(finite_diff))
            stats["median_diff"] = float(np.median(finite_diff))

            if stats["max_diff"] <= tolerance:
                return True, f"文件在容差范围内相等 (最大差异: {stats['max_diff']:.2e})", stats
            else:
                return False, f"文件不相等 (最大差异: {stats['max_diff']:.2e} > 容差: {tolerance:.2e})", stats

        except Exception as e2:
            return False, f"比较过程中出错: {e2}", stats

def main():
    # 定义路径
    base_dir = "/home/<USER>/git/feature_operators/test_right/test_results"
    cpp_dir = os.path.join(base_dir, "cpp")
    python_dir = os.path.join(base_dir, "python")

    # 获取所有目录中的所有可用因子
    cpp_files = glob.glob(os.path.join(cpp_dir, "*.csv"))
    py_files = glob.glob(os.path.join(python_dir, "*.csv"))

    # 提取因子名称
    cpp_factors = [os.path.splitext(os.path.basename(f))[0] for f in cpp_files]
    py_factors = [os.path.splitext(os.path.basename(f))[0] for f in py_files]

    # 合并所有因子
    all_factors = sorted(set(cpp_factors + py_factors))

    print(f"在所有实现中找到 {len(all_factors)} 个唯一因子")
    print(f"C++: {len(cpp_factors)} 个因子")
    print(f"Python: {len(py_factors)} 个因子")

    # 比较结果
    print("\n比较结果:")
    print("=" * 80)
    print(f"{'因子':<20} {'C++':<10} {'Python':<10} {'C++ vs Python':<25}")
    print("-" * 80)

    # 统计
    cpp_vs_py_matches = 0
    cpp_vs_py_total = 0

    # 详细统计
    nan_diff_factors = []  # NaN 不一致的因子
    inf_diff_factors = []  # Inf 不一致的因子
    all_nan_inf_factors = []  # 全是 NaN 或 Inf 的因子
    value_diff_factors = []  # 值不一致的因子

    # 保存结果到文件
    with open(os.path.join(base_dir, "comparison_results.txt"), "w") as f:
        f.write(f"在所有实现中找到 {len(all_factors)} 个唯一因子\n")
        f.write(f"C++: {len(cpp_factors)} 个因子\n")
        f.write(f"Python: {len(py_factors)} 个因子\n\n")

        f.write("比较结果:\n")
        f.write("=" * 80 + "\n")
        f.write(f"{'因子':<20} {'C++':<10} {'Python':<10} {'C++ vs Python':<25}\n")
        f.write("-" * 80 + "\n")

    # 收集所有比较结果
    comparison_results = []

    for factor in all_factors:
        cpp_file = os.path.join(cpp_dir, f"{factor}.csv")
        py_file = os.path.join(python_dir, f"{factor}.csv")

        has_cpp = os.path.exists(cpp_file) and os.path.getsize(cpp_file) > 0
        has_py = os.path.exists(py_file) and os.path.getsize(py_file) > 0

        cpp_status = "✓" if has_cpp else "✗"
        py_status = "✓" if has_py else "✗"

        factor_result = {
            "factor": factor,
            "has_cpp": has_cpp,
            "has_py": has_py,
            "cpp_vs_py": None
        }

        # 比较 C++ vs Python
        cpp_vs_py = "N/A"
        if has_cpp and has_py:
            try:
                is_equal, message, stats = compare_files(cpp_file, py_file)
                cpp_vs_py = "✓" if is_equal else f"✗ ({message})"
                cpp_vs_py_total += 1
                if is_equal:
                    cpp_vs_py_matches += 1

                factor_result["cpp_vs_py"] = {
                    "is_equal": is_equal,
                    "message": message,
                    "stats": stats
                }

                # 收集统计信息
                if not is_equal:
                    if "NaN位置不匹配" in message or "存在NaN位置不匹配" in message:
                        nan_diff_factors.append((factor, "cpp_vs_py", stats))
                    elif "所有差异都是无穷大" in message:
                        inf_diff_factors.append((factor, "cpp_vs_py", stats))
                    elif "所有值都是 NaN 或 Inf" in message:
                        all_nan_inf_factors.append((factor, "cpp_vs_py", stats))
                    else:
                        value_diff_factors.append((factor, "cpp_vs_py", stats))
            except Exception as e:
                cpp_vs_py = f"错误: {e}"
                factor_result["cpp_vs_py"] = {
                    "is_equal": False,
                    "message": str(e),
                    "stats": None
                }

        comparison_results.append(factor_result)

        result_line = f"{factor:<20} {cpp_status:<10} {py_status:<10} {cpp_vs_py:<25}"
        print(result_line)

        # 保存到文件
        with open(os.path.join(base_dir, "comparison_results.txt"), "a") as f:
            f.write(result_line + "\n")

    print("-" * 80)

    # 打印摘要
    print("\n摘要:")
    summary = []
    if cpp_vs_py_total > 0:
        summary.append(f"C++ vs Python: {cpp_vs_py_matches}/{cpp_vs_py_total} 个因子匹配 ({cpp_vs_py_matches/cpp_vs_py_total*100:.1f}%)")

    for line in summary:
        print(line)

    # 打印详细统计信息
    print("\n详细统计信息:")

    # NaN 不一致的因子
    if nan_diff_factors:
        print("\nNaN 位置不匹配的因子:")
        for factor, comparison, stats in nan_diff_factors:
            print(f"  {factor} ({comparison}): 形状 {stats['shape1']} vs {stats['shape2']}，共 {stats['total_elements']} 个元素")

    # Inf 不一致的因子
    if inf_diff_factors:
        print("\n所有差异都是无穷大的因子:")
        for factor, comparison, stats in inf_diff_factors:
            print(f"  {factor} ({comparison}): 形状 {stats['shape1']} vs {stats['shape2']}，共 {stats['total_elements']} 个元素")

    # 全是 NaN 或 Inf 的因子
    if all_nan_inf_factors:
        print("\n全是 NaN 或 Inf 的因子:")
        for factor, comparison, stats in all_nan_inf_factors:
            print(f"  {factor} ({comparison}): 形状 {stats['shape1']} vs {stats['shape2']}，共 {stats['total_elements']} 个元素")

    # 值不一致的因子
    if value_diff_factors:
        print("\n数值不一致的因子:")
        for factor, comparison, stats in value_diff_factors:
            print(f"  {factor} ({comparison}): 最大差异 {stats['max_diff']:.2e}，"
                  f"平均差异 {stats['mean_diff']:.2e}，中位数差异 {stats['median_diff']:.2e}，"
                  f"形状 {stats['shape1']}，共 {stats['total_elements']} 个元素")

    # 保存摘要到文件
    with open(os.path.join(base_dir, "comparison_results.txt"), "a") as f:
        f.write("\n\n摘要:\n")
        for line in summary:
            f.write(line + "\n")

        # 保存详细统计信息
        f.write("\n详细统计信息:\n")

        # NaN 不一致的因子
        if nan_diff_factors:
            f.write("\nNaN 位置不匹配的因子:\n")
            for factor, comparison, stats in nan_diff_factors:
                f.write(f"  {factor} ({comparison}): 形状 {stats['shape1']} vs {stats['shape2']}，共 {stats['total_elements']} 个元素\n")

        # Inf 不一致的因子
        if inf_diff_factors:
            f.write("\n所有差异都是无穷大的因子:\n")
            for factor, comparison, stats in inf_diff_factors:
                f.write(f"  {factor} ({comparison}): 形状 {stats['shape1']} vs {stats['shape2']}，共 {stats['total_elements']} 个元素\n")

        # 全是 NaN 或 Inf 的因子
        if all_nan_inf_factors:
            f.write("\n全是 NaN 或 Inf 的因子:\n")
            for factor, comparison, stats in all_nan_inf_factors:
                f.write(f"  {factor} ({comparison}): 形状 {stats['shape1']} vs {stats['shape2']}，共 {stats['total_elements']} 个元素\n")

        # 值不一致的因子
        if value_diff_factors:
            f.write("\n数值不一致的因子:\n")
            for factor, comparison, stats in value_diff_factors:
                f.write(f"  {factor} ({comparison}): 最大差异 {stats['max_diff']:.2e}，"
                       f"平均差异 {stats['mean_diff']:.2e}，中位数差异 {stats['median_diff']:.2e}，"
                       f"形状 {stats['shape1']}，共 {stats['total_elements']} 个元素\n")

if __name__ == "__main__":
    main()
