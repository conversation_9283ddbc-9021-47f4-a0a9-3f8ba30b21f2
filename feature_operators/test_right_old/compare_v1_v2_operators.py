#!/usr/bin/env python3
"""
比较重构版本(v2)算子和Python版本的正确性
"""

import pandas as pd
import numpy as np
import os
import json
import time
from pathlib import Path

def load_csv_results(file_path):
    """加载CSV结果文件"""
    try:
        return pd.read_csv(file_path, header=None)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def compare_dataframes(df1, df2, tolerance=1e-9, name=""):
    """比较两个DataFrame的差异"""
    if df1 is None or df2 is None:
        return False, "One of the dataframes is None"

    if df1.shape != df2.shape:
        return False, f"Shape mismatch: {df1.shape} vs {df2.shape}"

    # 处理NaN值
    df1_nan = pd.isna(df1)
    df2_nan = pd.isna(df2)

    # 检查NaN位置是否一致
    nan_mismatch = ~(df1_nan == df2_nan)
    if nan_mismatch.any().any():
        nan_diff_count = nan_mismatch.sum().sum()
        return False, f"NaN position mismatch: {nan_diff_count} differences"

    # 比较非NaN值
    mask = ~(df1_nan | df2_nan)
    if mask.any().any():
        diff = np.abs(df1[mask] - df2[mask])
        max_diff = diff.max().max()

        if max_diff > tolerance:
            # 计算相对误差
            rel_diff = diff / np.abs(df1[mask])
            max_rel_diff = rel_diff.max().max()
            return False, f"Max absolute diff: {max_diff:.2e}, Max relative diff: {max_rel_diff:.2e}"

    return True, "Match"

def main():
    # 设置路径
    cpp_results_dir = Path("/home/<USER>/git/feature_operators/test_right/test_results/cpp")
    python_results_dir = Path("/home/<USER>/git/feature_operators/test_right/test_results/python")

    if not cpp_results_dir.exists():
        print(f"C++ test results directory not found: {cpp_results_dir}")
        return

    if not python_results_dir.exists():
        print(f"Python test results directory not found: {python_results_dir}")
        return

    # 需要比较的算子列表
    operators_to_compare = [
        "ts_Delay", "ts_Mean", "ts_Sum", "ts_Stdev", "ts_Min", "ts_Max",
        "ts_Delta", "ts_Divide", "ts_ChgRate", "ts_Argmax", "ts_Argmin",
        "ts_Rank", "ts_Median", "ts_Corr", "ts_Cov", "ts_Skewness",
        "ts_Kurtosis", "ts_Scale", "ts_Product", "ts_Decay", "ts_Decay2",
        "ts_MaxDD", "ts_MeanChg", "ts_Quantile_A", "ts_Quantile_B",
        "ts_Quantile_C", "ts_Quantile_D"
    ]

    tot_operators = [
        "Tot_Mean", "Tot_Sum", "Tot_Stdev", "Tot_Delta", "Tot_Divide",
        "Tot_ChgRate", "Tot_Rank", "Tot_ArgMax", "Tot_ArgMin", "Tot_Max", "Tot_Min"
    ]

    # 合并所有算子
    all_operators = operators_to_compare + tot_operators

    print("=" * 80)
    print("算子正确性比较报告 (C++ v2版本 vs Python版本)")
    print("=" * 80)
    print(f"{'算子名称':<20} {'状态':<10} {'详细信息':<50}")
    print("-" * 80)

    results = {}
    match_count = 0
    total_count = 0

    for op in all_operators:
        # C++ v2版本结果
        cpp_v2_file = cpp_results_dir / f"{op}_v2.csv"
        # Python版本结果
        python_file = python_results_dir / f"{op}.csv"

        if cpp_v2_file.exists() and python_file.exists():
            total_count += 1

            # 加载数据
            df_cpp_v2 = load_csv_results(cpp_v2_file)
            df_python = load_csv_results(python_file)

            # 比较结果
            is_match, details = compare_dataframes(df_cpp_v2, df_python, tolerance=1e-9, name=op)

            if is_match:
                match_count += 1
                status = "✓ MATCH"
                print(f"{op:<20} {status:<10} {details:<50}")
            else:
                status = "✗ DIFF"
                print(f"{op:<20} {status:<10} {details:<50}")

            results[op] = {
                "match": is_match,
                "details": details,
                "cpp_v2_shape": df_cpp_v2.shape if df_cpp_v2 is not None else None,
                "python_shape": df_python.shape if df_python is not None else None
            }
        else:
            missing_files = []
            if not cpp_v2_file.exists():
                missing_files.append("C++ v2")
            if not python_file.exists():
                missing_files.append("Python")

            status = "✗ MISSING"
            details = f"Missing files: {', '.join(missing_files)}"
            print(f"{op:<20} {status:<10} {details:<50}")

            results[op] = {
                "match": False,
                "details": details,
                "cpp_v2_shape": None,
                "python_shape": None
            }

    print("-" * 80)
    if total_count > 0:
        print(f"总结: {match_count}/{total_count} 个算子完全匹配 ({match_count/total_count*100:.1f}%)")
    else:
        print("总结: 没有找到可比较的算子结果文件")

    # 保存详细结果
    results_file = cpp_results_dir / "v2_vs_python_comparison_results.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    print(f"详细结果已保存到: {results_file}")
    print("\n算子正确性比较完成!")

if __name__ == "__main__":
    main()
