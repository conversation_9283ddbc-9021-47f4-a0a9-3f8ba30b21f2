#!/usr/bin/env python3
"""
Python版本的ts_Corr计算，用于与C++版本比较
"""

import pandas as pd
import numpy as np
import sys
import os

def manual_ts_corr(s1, s2, n):
    """手动实现ts_Corr，完全按照Python逻辑"""
    n = int(n)
    if n <= 1:
        n = 1

    # 复制数据
    tem1 = s1.copy()
    tem2 = s2.copy()

    # NaN传播
    tem1[tem2.isna()] = np.nan
    tem2[tem1.isna()] = np.nan

    # 计算滚动统计量
    tem1_m = tem1.rolling(n, min_periods=1).mean()
    tem2_m = tem2.rolling(n, min_periods=1).mean()
    tem_prod_m = (tem1 * tem2).rolling(n, min_periods=1).mean()
    tem1_std = tem1.rolling(n, min_periods=1).std(ddof=0)
    tem2_std = tem2.rolling(n, min_periods=1).std(ddof=0)

    # 计算相关系数
    res = (tem_prod_m - tem1_m * tem2_m) / (tem1_std * tem2_std)
    return res.replace([-np.inf, np.inf], np.nan)

def main():
    """主函数"""
    print("=== Python ts_Corr 计算 ===")

    # 加载测试数据
    try:
        close_df = pd.read_csv('sample_close.csv', index_col=0)
        volume_df = pd.read_csv('sample_volume.csv', index_col=0)
        print(f"数据加载成功: close {close_df.shape}, volume {volume_df.shape}")
    except Exception as e:
        print(f"数据加载失败: {e}")
        return

    # 测试窗口大小
    window_size = 20
    print(f"使用窗口大小: {window_size}")

    # 计算ts_Corr
    result = manual_ts_corr(close_df, volume_df, window_size)

    print(f"计算完成，结果形状: {result.shape}")
    print(f"NaN数量: {result.isna().sum().sum()}")
    print(f"有效值数量: {(~result.isna()).sum().sum()}")

    # 统计特殊值
    valid_result = result.dropna()
    if len(valid_result) > 0:
        print(f"最小值: {valid_result.min().min():.10f}")
        print(f"最大值: {valid_result.max().max():.10f}")
        print(f"均值: {valid_result.mean().mean():.10f}")
        print(f"1.0的数量: {(result == 1.0).sum().sum()}")
        print(f"-1.0的数量: {(result == -1.0).sum().sum()}")
        print(f"接近1.0的数量: {(np.abs(result - 1.0) < 1e-10).sum().sum()}")
        print(f"接近-1.0的数量: {(np.abs(result + 1.0) < 1e-10).sum().sum()}")

    # 输出所有行的详细结果
    print(f"\n所有行结果:")
    for i in range(len(result)):
        val = result.iloc[i, 0]
        if pd.isna(val):
            print(f"Row {i}: NaN")
        else:
            print(f"Row {i}: {val:.15f}")

if __name__ == "__main__":
    main()
