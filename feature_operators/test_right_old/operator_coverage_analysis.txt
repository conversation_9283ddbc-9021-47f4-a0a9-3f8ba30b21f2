C++算子总数: 90
Python算子总数: 98

算子覆盖分析:
================================================================================

在C++中但不在Python中的算子 (0):

在Python中但不在C++中的算子 (8):
  - Repmat
  - calc
  - calc2
  - delayed
  - get_dfs
  - is_number
  - njit
  - parse_expression

可能命名不一致的算子:
================================================================================

ts_系列函数:
  Python: 28 个
  C++: 28 个

Tot_系列函数:
  Python: 11 个
  C++: 11 个
