#include <iostream>
#include <vector>
#include <cmath>
#include <iomanip>
#include <fstream>
#include <sstream>
#include <string>
#include <Eigen/Dense>

// 定义DataFrame类型
using DataFrame = Eigen::ArrayXXd;

// 定义NaN常量
namespace rolling {
    const double NaN = std::numeric_limits<double>::quiet_NaN();
}

// 读取CSV文件
std::vector<std::vector<double>> read_csv(const std::string& filename) {
    std::vector<std::vector<double>> data;
    std::ifstream file(filename);
    std::string line;

    // 跳过第一行（标题行）
    if (std::getline(file, line)) {
        // 处理标题行
    }

    while (std::getline(file, line)) {
        std::vector<double> row;
        std::stringstream ss(line);
        std::string cell;

        // 跳过第一列（索引列）
        if (std::getline(ss, cell, ',')) {
            // 处理索引列
        }

        while (std::getline(ss, cell, ',')) {
            if (!cell.empty()) {
                try {
                    row.push_back(std::stod(cell));
                } catch (...) {
                    row.push_back(std::nan(""));
                }
            }
        }

        if (!row.empty()) {
            data.push_back(row);
        }
    }

    return data;
}

// 直接复制ts_Corr_v2函数的实现
// DataFrame ts_Corr_v2(const DataFrame &data1, const DataFrame &data2, int n_param) {
//   if (data1.rows() != data2.rows() || data1.cols() != data2.cols()) {
//     throw std::invalid_argument(
//         "ts_Corr: data1 and data2 must have the same dimensions.");
//   }

//   // Python: if n <= 1: n = 1 (not n = 2!)
//   int n_effective = (n_param <= 1) ? 1 : n_param;
//   int min_periods = 1;  // Python uses min_periods=1

//   DataFrame result = DataFrame::Constant(data1.rows(), data1.cols(), rolling::NaN);

//   for (Eigen::Index j = 0; j < data1.cols(); ++j) {
//     double current_sum_x = 0.0, c_sum_x = 0.0;
//     double current_sum_y = 0.0, c_sum_y = 0.0;
//     double current_sum_x_sq = 0.0, c_sum_x_sq = 0.0;
//     double current_sum_y_sq = 0.0, c_sum_y_sq = 0.0;
//     double current_sum_xy = 0.0, c_sum_xy = 0.0;
//     int current_valid_pairs_count = 0;

//     for (Eigen::Index i = 0; i < data1.rows(); ++i) {
//       // Add incoming pair with NaN propagation
//       double val1_in = data1(i, j);
//       double val2_in = data2(i, j);

//       // Python NaN propagation: tem1[tem2.isna()] = np.nan; tem2[tem1.isna()] = np.nan
//       if (std::isnan(val1_in) || std::isnan(val2_in)) {
//         val1_in = rolling::NaN;
//         val2_in = rolling::NaN;
//       }

//       if (!std::isnan(val1_in) && !std::isnan(val2_in)) {
//         current_valid_pairs_count++;
//         double val1_in_sq = val1_in * val1_in;
//         double val2_in_sq = val2_in * val2_in;
//         double val_xy_in = val1_in * val2_in;

//         // Kahan sum for sum_x
//         double y = val1_in - c_sum_x;
//         double t = current_sum_x + y;
//         c_sum_x = (t - current_sum_x) - y;
//         current_sum_x = t;
//         // Kahan sum for sum_y
//         y = val2_in - c_sum_y;
//         t = current_sum_y + y;
//         c_sum_y = (t - current_sum_y) - y;
//         current_sum_y = t;
//         // Kahan sum for sum_x_sq
//         y = val1_in_sq - c_sum_x_sq;
//         t = current_sum_x_sq + y;
//         c_sum_x_sq = (t - current_sum_x_sq) - y;
//         current_sum_x_sq = t;
//         // Kahan sum for sum_y_sq
//         y = val2_in_sq - c_sum_y_sq;
//         t = current_sum_y_sq + y;
//         c_sum_y_sq = (t - current_sum_y_sq) - y;
//         current_sum_y_sq = t;
//         // Kahan sum for sum_xy
//         y = val_xy_in - c_sum_xy;
//         t = current_sum_xy + y;
//         c_sum_xy = (t - current_sum_xy) - y;
//         current_sum_xy = t;
//       }

//       // Subtract outgoing pair with NaN propagation
//       if (i >= n_effective) {
//         double val1_out = data1(i - n_effective, j);
//         double val2_out = data2(i - n_effective, j);

//         // Apply same NaN propagation for outgoing pair
//         if (std::isnan(val1_out) || std::isnan(val2_out)) {
//           val1_out = rolling::NaN;
//           val2_out = rolling::NaN;
//         }

//         if (!std::isnan(val1_out) && !std::isnan(val2_out)) {
//           current_valid_pairs_count--;
//           double val1_out_sq = val1_out * val1_out;
//           double val2_out_sq = val2_out * val2_out;
//           double val_xy_out = val1_out * val2_out;

//           // Kahan sum for sum_x (subtracting)
//           double y = -val1_out - c_sum_x;
//           double t = current_sum_x + y;
//           c_sum_x = (t - current_sum_x) - y;
//           current_sum_x = t;
//           // Kahan sum for sum_y (subtracting)
//           y = -val2_out - c_sum_y;
//           t = current_sum_y + y;
//           c_sum_y = (t - current_sum_y) - y;
//           current_sum_y = t;
//           // Kahan sum for sum_x_sq (subtracting)
//           y = -val1_out_sq - c_sum_x_sq;
//           t = current_sum_x_sq + y;
//           c_sum_x_sq = (t - current_sum_x_sq) - y;
//           current_sum_x_sq = t;
//           // Kahan sum for sum_y_sq (subtracting)
//           y = -val2_out_sq - c_sum_y_sq;
//           t = current_sum_y_sq + y;
//           c_sum_y_sq = (t - current_sum_y_sq) - y;
//           current_sum_y_sq = t;
//           // Kahan sum for sum_xy (subtracting)
//           y = -val_xy_out - c_sum_xy;
//           t = current_sum_xy + y;
//           c_sum_xy = (t - current_sum_xy) - y;
//           current_sum_xy = t;
//         }
//       }

//       if (current_valid_pairs_count >= min_periods) {
//         double N_double = static_cast<double>(current_valid_pairs_count);
//         double mean_x = current_sum_x / N_double;
//         double mean_y = current_sum_y / N_double;
//         double mean_xy = current_sum_xy / N_double;
//         double mean_x_sq = current_sum_x_sq / N_double;
//         double mean_y_sq = current_sum_y_sq / N_double;

//         double var_x_pop = mean_x_sq - mean_x * mean_x;
//         double var_y_pop = mean_y_sq - mean_y * mean_y;

//         if (var_x_pop < 0 && var_x_pop > -1e-12)
//           var_x_pop = 0;
//         if (var_y_pop < 0 && var_y_pop > -1e-12)
//           var_y_pop = 0;

//         if (var_x_pop <= 1e-14 || var_y_pop <= 1e-14) {
//           result(i, j) = rolling::NaN;
//         } else {
//           double std_x_pop = std::sqrt(var_x_pop);
//           double std_y_pop = std::sqrt(var_y_pop);
//           double cov_pop =
//               mean_xy - mean_x * mean_y; // This is also used by ts_Cov
//           double corr = cov_pop / (std_x_pop * std_y_pop);

//           // Python: return res.replace([-np.inf, np.inf], np.nan)
//           if (std::isinf(corr)) {
//             result(i, j) = rolling::NaN;
//           } else {
//             result(i, j) = corr;
//           }
//         }
//       } else {
//         result(i, j) = rolling::NaN;
//       }
//     }
//   }
//   return result;
// }
DataFrame ts_Corr_v2(const DataFrame &data1, const DataFrame &data2, int n_param) {
  if (data1.rows() != data2.rows() || data1.cols() != data2.cols()) {
    throw std::invalid_argument(
        "ts_Corr: data1 and data2 must have the same dimensions.");
  }

  // Python: if n <= 1: n = 1 (not n = 2!)
  int n_effective = (n_param <= 1) ? 1 : n_param;
  int min_periods = 1;  // Python uses min_periods=1

  DataFrame result = DataFrame::Constant(data1.rows(), data1.cols(), rolling::NaN);

  for (Eigen::Index j = 0; j < data1.cols(); ++j) {
    double current_sum_x = 0.0, c_sum_x = 0.0;
    double current_sum_y = 0.0, c_sum_y = 0.0;
    double current_sum_x_sq = 0.0, c_sum_x_sq = 0.0;
    double current_sum_y_sq = 0.0, c_sum_y_sq = 0.0;
    double current_sum_xy = 0.0, c_sum_xy = 0.0;
    int current_valid_pairs_count = 0;

    for (Eigen::Index i = 0; i < data1.rows(); ++i) {
      // Add incoming pair with NaN propagation
      double val1_in = data1(i, j);
      double val2_in = data2(i, j);

      // Python NaN propagation: tem1[tem2.isna()] = np.nan; tem2[tem1.isna()] = np.nan
      if (std::isnan(val1_in) || std::isnan(val2_in)) {
        val1_in = rolling::NaN;
        val2_in = rolling::NaN;
      }

      if (!std::isnan(val1_in) && !std::isnan(val2_in)) {
        current_valid_pairs_count++;
        double val1_in_sq = val1_in * val1_in;
        double val2_in_sq = val2_in * val2_in;
        double val_xy_in = val1_in * val2_in;

        // Kahan sum for sum_x
        double y = val1_in - c_sum_x;
        double t = current_sum_x + y;
        c_sum_x = (t - current_sum_x) - y;
        current_sum_x = t;
        // Kahan sum for sum_y
        y = val2_in - c_sum_y;
        t = current_sum_y + y;
        c_sum_y = (t - current_sum_y) - y;
        current_sum_y = t;
        // Kahan sum for sum_x_sq
        y = val1_in_sq - c_sum_x_sq;
        t = current_sum_x_sq + y;
        c_sum_x_sq = (t - current_sum_x_sq) - y;
        current_sum_x_sq = t;
        // Kahan sum for sum_y_sq
        y = val2_in_sq - c_sum_y_sq;
        t = current_sum_y_sq + y;
        c_sum_y_sq = (t - current_sum_y_sq) - y;
        current_sum_y_sq = t;
        // Kahan sum for sum_xy
        y = val_xy_in - c_sum_xy;
        t = current_sum_xy + y;
        c_sum_xy = (t - current_sum_xy) - y;
        current_sum_xy = t;
      }

      // Subtract outgoing pair with NaN propagation
      if (i >= n_effective) {
        double val1_out = data1(i - n_effective, j);
        double val2_out = data2(i - n_effective, j);

        // Apply same NaN propagation for outgoing pair
        if (std::isnan(val1_out) || std::isnan(val2_out)) {
          val1_out = rolling::NaN;
          val2_out = rolling::NaN;
        }

        if (!std::isnan(val1_out) && !std::isnan(val2_out)) {
          current_valid_pairs_count--;
          double val1_out_sq = val1_out * val1_out;
          double val2_out_sq = val2_out * val2_out;
          double val_xy_out = val1_out * val2_out;

          // Kahan sum for sum_x (subtracting)
          double y = -val1_out - c_sum_x;
          double t = current_sum_x + y;
          c_sum_x = (t - current_sum_x) - y;
          current_sum_x = t;
          // Kahan sum for sum_y (subtracting)
          y = -val2_out - c_sum_y;
          t = current_sum_y + y;
          c_sum_y = (t - current_sum_y) - y;
          current_sum_y = t;
          // Kahan sum for sum_x_sq (subtracting)
          y = -val1_out_sq - c_sum_x_sq;
          t = current_sum_x_sq + y;
          c_sum_x_sq = (t - current_sum_x_sq) - y;
          current_sum_x_sq = t;
          // Kahan sum for sum_y_sq (subtracting)
          y = -val2_out_sq - c_sum_y_sq;
          t = current_sum_y_sq + y;
          c_sum_y_sq = (t - current_sum_y_sq) - y;
          current_sum_y_sq = t;
          // Kahan sum for sum_xy (subtracting)
          y = -val_xy_out - c_sum_xy;
          t = current_sum_xy + y;
          c_sum_xy = (t - current_sum_xy) - y;
          current_sum_xy = t;
        }
      }

      if (current_valid_pairs_count >= min_periods) {
        double N_double = static_cast<double>(current_valid_pairs_count);
        double mean_x = current_sum_x / N_double;
        double mean_y = current_sum_y / N_double;
        double mean_xy = current_sum_xy / N_double;
        double mean_x_sq = current_sum_x_sq / N_double;
        double mean_y_sq = current_sum_y_sq / N_double;

        double var_x_pop = mean_x_sq - mean_x * mean_x;
        double var_y_pop = mean_y_sq - mean_y * mean_y;

        // if (var_x_pop < 0 && var_x_pop > -1e-12)
        //   var_x_pop = 0;
        // if (var_y_pop < 0 && var_y_pop > -1e-12)
        //   var_y_pop = 0;
                  // std::cout << "index: " << i << " var_x_pop: " << var_x_pop
                  //   << " var_y_pop:" << var_y_pop << std::endl;

        // if (var_x_pop <= 1e-14 || var_y_pop <= 1e-14) {
        //   result(i, j) = rolling::NaN;
        // } else {
          double std_x_pop = std::sqrt(var_x_pop);
          double std_y_pop = std::sqrt(var_y_pop);
          double cov_pop =
              mean_xy - mean_x * mean_y; // This is also used by ts_Cov
          double corr = cov_pop / (std_x_pop * std_y_pop);

          // Python: return res.replace([-np.inf, np.inf], np.nan)
          if (std::isinf(corr)) {
            result(i, j) = rolling::NaN;
          } else {
            result(i, j) = corr;
             std::cout << "index: " << i << " corr: " << corr <<" in:"<<val1_in << " " << val2_in << std::endl;
          }
        // }
      } else {
        result(i, j) = rolling::NaN;
      }
    }
  }
  return result;
}
int main() {
    std::cout << std::fixed << std::setprecision(15);
    std::cout << "=== C++ ts_Corr 计算 ===" << std::endl;

    // 读取数据
    std::vector<std::vector<double>> close_data = read_csv("sample_close.csv");
    std::vector<std::vector<double>> volume_data = read_csv("sample_volume.csv");

    if (close_data.empty() || volume_data.empty()) {
        std::cout << "数据加载失败" << std::endl;
        return 1;
    }

    std::cout << "数据加载成功: close " << close_data.size() << "x" << close_data[0].size()
              << ", volume " << volume_data.size() << "x" << volume_data[0].size() << std::endl;

    // 转换为DataFrame格式
    int rows = close_data.size();
    int cols = close_data[0].size();

    DataFrame close_df(rows, cols);
    DataFrame volume_df(rows, cols);

    for (int i = 0; i < rows; i++) {
        for (int j = 0; j < cols; j++) {
            close_df(i, j) = close_data[i][j];
            volume_df(i, j) = volume_data[i][j];
        }
    }

    // 计算ts_Corr_v2
    int window_size = 10;
    std::cout << "使用窗口大小: " << window_size << std::endl;

    DataFrame result = ts_Corr_v2(close_df, volume_df, window_size);

    std::cout << "计算完成，结果形状: " << result.rows() << "x" << result.cols() << std::endl;

    // 统计
    int nan_count = 0;
    int perfect_pos = 0;
    int perfect_neg = 0;
    int close_to_pos = 0;
    int close_to_neg = 0;
    double min_val = 1e10, max_val = -1e10, sum_val = 0.0;
    int valid_count = 0;

    for (int i = 0; i < result.rows(); i++) {
        double val = result(i, 0);  // 使用第一列
        if (std::isnan(val)) {
            nan_count++;
        } else {
            valid_count++;
            sum_val += val;
            if (val < min_val) min_val = val;
            if (val > max_val) max_val = val;

            if (val == 1.0) perfect_pos++;
            if (val == -1.0) perfect_neg++;
            if (std::abs(val - 1.0) < 1e-10) close_to_pos++;
            if (std::abs(val + 1.0) < 1e-10) close_to_neg++;
        }
    }

    std::cout << "NaN数量: " << nan_count << std::endl;
    std::cout << "有效值数量: " << valid_count << std::endl;
    if (valid_count > 0) {
        std::cout << "最小值: " << min_val << std::endl;
        std::cout << "最大值: " << max_val << std::endl;
        std::cout << "均值: " << (sum_val / valid_count) << std::endl;
        std::cout << "1.0的数量: " << perfect_pos << std::endl;
        std::cout << "-1.0的数量: " << perfect_neg << std::endl;
        std::cout << "接近1.0的数量: " << close_to_pos << std::endl;
        std::cout << "接近-1.0的数量: " << close_to_neg << std::endl;
    }

    // 输出所有行的详细结果
    std::cout << "\n所有行结果:" << std::endl;
    for (int i = 0; i < result.rows(); i++) {
        double val = result(i, 0);
        if (std::isnan(val)) {
            std::cout << "Row " << i << ": NaN" << std::endl;
        } else {
            std::cout << "Row " << i << ": " << std::setprecision(15) << val << std::endl;
        }
    }

    return 0;
}
