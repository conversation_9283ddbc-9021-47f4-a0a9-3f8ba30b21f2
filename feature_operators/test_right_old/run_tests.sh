#!/bin/bash
set -e

# Activate Python environment
source /home/<USER>/freqtrade/.venv/bin/activate

# Define paths
BASE_DIR="/home/<USER>/git/feature_operators"
TEST_DIR="$BASE_DIR/test_right"
RESULTS_DIR="$TEST_DIR/test_results"
PYTHON_RESULTS_DIR="$RESULTS_DIR/python"
CPP_RESULTS_DIR="$RESULTS_DIR/cpp"
COMPARE_SCRIPT="$TEST_DIR/simple_compare.py"

# Create build directory if it doesn't exist
mkdir -p "$TEST_DIR/build"
cd "$TEST_DIR/build"

# Create results directories if they don't exist
mkdir -p "$PYTHON_RESULTS_DIR"
mkdir -p "$CPP_RESULTS_DIR"

# Configure and build the C++ tests
echo "Configuring and building C++ tests..."
cmake ..
make -j$(nproc)

# Run the C++ tests
echo "Running C++ operators test..."
./test_optimized_operators

# Run the Python test
echo "Running Python operators test..."
cd "$TEST_DIR"
python3 test_python_operators.py

# Run the comparison script
echo "Comparing results..."
python3 "$COMPARE_SCRIPT"

# Run the operator coverage analysis
echo "Analyzing operator coverage..."
python3 "$TEST_DIR/analyze_operator_coverage.py"

echo "All tests completed."
echo "Results are in $RESULTS_DIR/comparison_results.txt"
echo "Operator coverage analysis is in $TEST_DIR/operator_coverage_analysis.txt"
