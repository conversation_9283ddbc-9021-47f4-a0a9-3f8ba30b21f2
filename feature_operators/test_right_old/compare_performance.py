#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import pandas as pd
import numpy as np

def load_benchmark_results(cpp_file, python_file):
    """加载C++和Python的性能测试结果"""
    cpp_results = {}
    python_results = {}

    # 加载C++结果
    if os.path.exists(cpp_file):
        with open(cpp_file, 'r') as f:
            cpp_results = json.load(f)
        print(f"C++结果加载成功: {len(cpp_results)} 个算子")
    else:
        print(f"C++结果文件不存在: {cpp_file}")

    # 加载Python结果
    if os.path.exists(python_file):
        with open(python_file, 'r') as f:
            python_results = json.load(f)
        print(f"Python结果加载成功: {len(python_results)} 个算子")
    else:
        print(f"Python结果文件不存在: {python_file}")

    return cpp_results, python_results

def compare_performance(cpp_results, python_results, output_file):
    """比较C++和Python的性能"""

    # 找到共同的算子
    common_operators = set(cpp_results.keys()) & set(python_results.keys())

    if not common_operators:
        print("没有找到共同的算子进行比较")
        return

    print(f"找到 {len(common_operators)} 个共同算子进行比较")

    # 创建比较数据
    comparison_data = []

    for op in sorted(common_operators):
        cpp_time = cpp_results[op]
        python_time = python_results[op]

        # 跳过错误的结果
        if cpp_time < 0 or python_time < 0:
            continue

        speedup = python_time / cpp_time if cpp_time > 0 else float('inf')

        comparison_data.append({
            'Operator': op,
            'C++_Time_μs': cpp_time,
            'Python_Time_μs': python_time,
            'Speedup': speedup,
            'Performance_Ratio': f"{speedup:.2f}x"
        })

    # 创建DataFrame
    df = pd.DataFrame(comparison_data)
    df = df.sort_values('Speedup', ascending=False)

    # 保存详细结果
    df.to_csv(output_file.replace('.txt', '_detailed.csv'), index=False)

    # 生成报告
    with open(output_file, 'w') as f:
        f.write("Feature Operators 性能比较报告\n")
        f.write("=" * 60 + "\n\n")

        f.write(f"测试算子总数: {len(comparison_data)}\n")
        f.write(f"C++平均执行时间: {df['C++_Time_μs'].mean():.3f} μs\n")
        f.write(f"Python平均执行时间: {df['Python_Time_μs'].mean():.3f} μs\n")
        f.write(f"平均加速比: {df['Speedup'].mean():.2f}x\n\n")

        f.write("性能排名 (按加速比排序):\n")
        f.write("-" * 60 + "\n")
        f.write(f"{'排名':<4} {'算子':<20} {'C++时间(μs)':<12} {'Python时间(μs)':<15} {'加速比':<10}\n")
        f.write("-" * 60 + "\n")

        for i, row in df.iterrows():
            rank = df.index.get_loc(i) + 1
            f.write(f"{rank:<4} {row['Operator']:<20} {row['C++_Time_μs']:<12.3f} "
                   f"{row['Python_Time_μs']:<15.3f} {row['Speedup']:<10.2f}\n")

        f.write("\n" + "=" * 60 + "\n")

        # 分类统计
        basic_ops = [op for op in comparison_data if not op['Operator'].startswith(('ts_', 'pn_', 'Tot_'))]
        ts_ops = [op for op in comparison_data if op['Operator'].startswith('ts_')]
        pn_ops = [op for op in comparison_data if op['Operator'].startswith('pn_')]
        tot_ops = [op for op in comparison_data if op['Operator'].startswith('Tot_')]

        f.write("分类性能统计:\n")
        f.write("-" * 30 + "\n")

        if basic_ops:
            basic_speedup = np.mean([op['Speedup'] for op in basic_ops])
            f.write(f"基本算子 ({len(basic_ops)}个): 平均加速比 {basic_speedup:.2f}x\n")

        if ts_ops:
            ts_speedup = np.mean([op['Speedup'] for op in ts_ops])
            f.write(f"时间序列算子 ({len(ts_ops)}个): 平均加速比 {ts_speedup:.2f}x\n")

        if pn_ops:
            pn_speedup = np.mean([op['Speedup'] for op in pn_ops])
            f.write(f"面板算子 ({len(pn_ops)}个): 平均加速比 {pn_speedup:.2f}x\n")

        if tot_ops:
            tot_speedup = np.mean([op['Speedup'] for op in tot_ops])
            f.write(f"Tot算子 ({len(tot_ops)}个): 平均加速比 {tot_speedup:.2f}x\n")

    # 打印摘要到控制台
    print("\n性能比较摘要:")
    print("=" * 50)
    print(f"测试算子总数: {len(comparison_data)}")
    print(f"C++平均执行时间: {df['C++_Time_μs'].mean():.3f} μs")
    print(f"Python平均执行时间: {df['Python_Time_μs'].mean():.3f} μs")
    print(f"平均加速比: {df['Speedup'].mean():.2f}x")

    print(f"\n前5名最快的算子:")
    for i, row in df.head().iterrows():
        print(f"  {row['Operator']}: {row['Speedup']:.2f}x")

    print(f"\n后5名最慢的算子:")
    for i, row in df.tail().iterrows():
        print(f"  {row['Operator']}: {row['Speedup']:.2f}x")

    return df

def main():
    cpp_file = "test_results/benchmark_cpp_results.json"
    python_file = "test_results/benchmark_python_results.json"
    output_file = "test_results/performance_comparison.txt"

    print("开始性能比较分析...")

    # 加载结果
    cpp_results, python_results = load_benchmark_results(cpp_file, python_file)

    if not cpp_results or not python_results:
        print("缺少性能测试结果文件，请先运行性能测试")
        return 1

    # 比较性能
    df = compare_performance(cpp_results, python_results, output_file)

    if df is not None and not df.empty:
        print(f"\n详细报告已保存到: {output_file}")
        print(f"详细数据已保存到: {output_file.replace('.txt', '_detailed.csv')}")

    print("\n性能比较分析完成!")
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
