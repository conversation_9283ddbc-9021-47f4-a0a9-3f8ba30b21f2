#!/usr/bin/env python3
"""
比较C++原始版本和重构版本(v2)的性能
"""

import pandas as pd
import numpy as np
import json
from pathlib import Path

def main():
    # 设置路径
    cpp_results_dir = Path("/home/<USER>/git/feature_operators/test_right/test_results/")
    
    if not cpp_results_dir.exists():
        print(f"C++ test results directory not found: {cpp_results_dir}")
        return
    
    # 需要比较的算子列表
    operators_to_compare = [
        "ts_Delay", "ts_Mean", "ts_Sum", "ts_Stdev", "ts_Min", "ts_<PERSON>",
        "ts_Delta", "ts_Divide", "ts_ChgRate", "ts_Argmax", "ts_Argmin",
        "ts_Rank", "ts_Median", "ts_Corr", "ts_Cov", "ts_Skewness",
        "ts_Kurtosis", "ts_Scale", "ts_Product", "ts_Decay", "ts_Decay2",
        "ts_MaxDD", "ts_MeanChg", "ts_Quantile_A", "ts_Quantile_B",
        "ts_Quantile_C", "ts_Quantile_D"
    ]
    
    tot_operators = [
        "Tot_Mean", "Tot_Sum", "Tot_Stdev", "Tot_Delta", "Tot_Divide",
        "Tot_ChgRate", "Tot_Rank", "Tot_ArgMax", "Tot_ArgMin", "Tot_Max", "Tot_Min"
    ]
    
    # 合并所有算子
    all_operators = operators_to_compare + tot_operators
    
    # 性能比较（如果有基准测试结果）
    benchmark_file = cpp_results_dir / "benchmark_cpp_results.json"
    if not benchmark_file.exists():
        print(f"Benchmark results file not found: {benchmark_file}")
        print("请先运行基准测试生成性能数据")
        return
    
    print("=" * 80)
    print("C++ 性能比较报告 (原始版本 vs 重构版本v2)")
    print("=" * 80)
    
    with open(benchmark_file, 'r') as f:
        benchmark_data = json.load(f)
    
    print(f"{'算子名称':<20} {'原版本(μs)':<15} {'v2版本(μs)':<15} {'性能提升':<15} {'提升率':<10}")
    print("-" * 80)
    
    performance_improvements = []
    comparison_results = {}
    
    for op in all_operators:
        v1_key = op
        v2_key = f"{op}_v2"
        
        if v1_key in benchmark_data and v2_key in benchmark_data:
            v1_time = benchmark_data[v1_key]
            v2_time = benchmark_data[v2_key]
            
            if v1_time > 0:
                improvement = (v1_time - v2_time) / v1_time * 100
                performance_improvements.append(improvement)
                
                if improvement > 0:
                    improvement_str = f"+{improvement:.1f}%"
                    status = "✓ 提升"
                elif improvement < -5:  # 性能下降超过5%
                    improvement_str = f"{improvement:.1f}%"
                    status = "✗ 下降"
                else:
                    improvement_str = f"{improvement:.1f}%"
                    status = "≈ 相近"
                
                print(f"{op:<20} {v1_time:<15.3f} {v2_time:<15.3f} {improvement_str:<15} {status:<10}")
                
                comparison_results[op] = {
                    "v1_time": v1_time,
                    "v2_time": v2_time,
                    "improvement_percent": improvement,
                    "status": status
                }
            else:
                print(f"{op:<20} {'N/A':<15} {'N/A':<15} {'N/A':<15} {'错误':<10}")
                comparison_results[op] = {
                    "v1_time": v1_time,
                    "v2_time": v2_time,
                    "improvement_percent": None,
                    "status": "错误"
                }
        else:
            missing = []
            if v1_key not in benchmark_data:
                missing.append("v1")
            if v2_key not in benchmark_data:
                missing.append("v2")
            
            print(f"{op:<20} {'缺失':<15} {'缺失':<15} {'N/A':<15} {f'缺失{missing}':<10}")
            comparison_results[op] = {
                "v1_time": None,
                "v2_time": None,
                "improvement_percent": None,
                "status": f"缺失{missing}"
            }
    
    print("-" * 80)
    
    if performance_improvements:
        avg_improvement = np.mean(performance_improvements)
        median_improvement = np.median(performance_improvements)
        max_improvement = np.max(performance_improvements)
        min_improvement = np.min(performance_improvements)
        
        print(f"性能统计:")
        print(f"  平均性能提升: {avg_improvement:.1f}%")
        print(f"  中位数性能提升: {median_improvement:.1f}%")
        print(f"  最大性能提升: {max_improvement:.1f}%")
        print(f"  最小性能提升: {min_improvement:.1f}%")
        
        # 统计提升、下降、相近的算子数量
        improved_count = sum(1 for x in performance_improvements if x > 5)
        degraded_count = sum(1 for x in performance_improvements if x < -5)
        similar_count = sum(1 for x in performance_improvements if -5 <= x <= 5)
        
        print(f"\n性能分布:")
        print(f"  性能提升 (>5%): {improved_count} 个算子")
        print(f"  性能下降 (<-5%): {degraded_count} 个算子")
        print(f"  性能相近 (-5%~5%): {similar_count} 个算子")
        
        # 保存详细结果
        results_file = cpp_results_dir / "v1_vs_v2_performance_comparison.json"
        with open(results_file, 'w') as f:
            json.dump({
                "summary": {
                    "avg_improvement": avg_improvement,
                    "median_improvement": median_improvement,
                    "max_improvement": max_improvement,
                    "min_improvement": min_improvement,
                    "improved_count": improved_count,
                    "degraded_count": degraded_count,
                    "similar_count": similar_count,
                    "total_compared": len(performance_improvements)
                },
                "detailed_results": comparison_results
            }, f, indent=2, default=str)
        
        print(f"\n详细结果已保存到: {results_file}")
    else:
        print("没有找到可比较的性能数据")
    
    print("\nC++ 性能比较完成!")

if __name__ == "__main__":
    main()
