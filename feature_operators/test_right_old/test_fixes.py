#!/usr/bin/env python3
"""
测试修复后的算子
"""

import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path

# 添加 feature_operator_funcs.py 的路径
sys.path.append('../')
from feature_operator_funcs import *

def test_python_implementations():
    """测试 Python 版本的实现"""
    print("=" * 60)
    print("测试 Python 版本的实现")
    print("=" * 60)
    
    # 创建测试数据
    np.random.seed(42)
    data = pd.DataFrame(np.random.randn(100, 5))
    data.iloc[10:15, 1] = np.nan  # 添加一些 NaN 值
    data.iloc[50:55, 3] = np.nan
    
    # 测试关键函数
    test_functions = [
        ('ts_Divide', lambda: ts_Divide(data, 10)),
        ('ts_ChgRate', lambda: ts_ChgRate(data, 10)),
        ('ts_Product', lambda: ts_Product(data, 10)),
        ('ts_Decay', lambda: ts_Decay(data, 10)),
        ('ts_Decay2', lambda: ts_Decay2(data, 10)),
        ('ts_Corr', lambda: ts_Corr(data.iloc[:, 0:1], data.iloc[:, 1:2], 10)),
        ('ts_Cov', lambda: ts_Cov(data.iloc[:, 0:1], data.iloc[:, 1:2], 10)),
        ('ts_Argmax', lambda: ts_Argmax(data, 10)),
        ('ts_Argmin', lambda: ts_Argmin(data, 10)),
        ('ts_Rank', lambda: ts_Rank(data, 10)),
        ('ts_Scale', lambda: ts_Scale(data, 10)),
    ]
    
    results = {}
    for name, func in test_functions:
        try:
            result = func()
            results[name] = result
            print(f"✓ {name}: 成功 - Shape: {result.shape}")
            
            # 检查 NaN 分布
            nan_count = result.isna().sum().sum()
            total_count = result.size
            nan_ratio = nan_count / total_count
            print(f"  NaN 比例: {nan_ratio:.2%} ({nan_count}/{total_count})")
            
        except Exception as e:
            print(f"✗ {name}: 失败 - {e}")
            results[name] = None
    
    return results

def save_python_results(results, output_dir):
    """保存 Python 测试结果"""
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    for name, result in results.items():
        if result is not None:
            output_file = output_dir / f"{name}.csv"
            result.to_csv(output_file, header=False, index=False)
            print(f"保存 {name} 结果到 {output_file}")

def analyze_key_differences():
    """分析关键差异"""
    print("\n" + "=" * 60)
    print("分析关键实现差异")
    print("=" * 60)
    
    # 创建简单测试数据
    data = pd.DataFrame([[1, 2, 3, 4, 5]]).T
    data.columns = ['col1']
    
    print("测试数据:")
    print(data)
    print()
    
    # 测试 ts_Divide 的参数处理
    print("ts_Divide 参数处理:")
    for n in [1, 3, 5, 7]:
        try:
            result = ts_Divide(data, n)
            print(f"  n={n}: 有效值数量 = {result.notna().sum().sum()}")
        except Exception as e:
            print(f"  n={n}: 错误 - {e}")
    
    print()
    
    # 测试 ts_ChgRate 的参数处理
    print("ts_ChgRate 参数处理:")
    for n in [0, 1, 2, 5]:
        try:
            result = ts_ChgRate(data, n)
            print(f"  n={n}: 有效值数量 = {result.notna().sum().sum()}")
        except Exception as e:
            print(f"  n={n}: 错误 - {e}")
    
    print()
    
    # 测试 ts_Product 的参数处理
    print("ts_Product 参数处理:")
    for n in [1, 2, 3, 5]:
        try:
            result = ts_Product(data, n)
            print(f"  n={n}: 有效值数量 = {result.notna().sum().sum()}")
        except Exception as e:
            print(f"  n={n}: 错误 - {e}")

def main():
    print("开始测试修复后的算子实现")
    
    # 测试 Python 实现
    results = test_python_implementations()
    
    # 保存结果
    save_python_results(results, "test_results/python_fixed")
    
    # 分析差异
    analyze_key_differences()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
