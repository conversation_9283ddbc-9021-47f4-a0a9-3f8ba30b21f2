# CMake generated Testfile for 
# Source directory: /home/<USER>/git/feature_operators
# Build directory: /home/<USER>/git/feature_operators/test_right/build/feature_operators_build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(CoreMathTest "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build/core_math_tests")
set_tests_properties(CoreMathTest PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/git/feature_operators/CMakeLists.txt;42;add_test;/home/<USER>/git/feature_operators/CMakeLists.txt;0;")
add_test(DataUtilsTest "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build/data_utils_tests")
set_tests_properties(DataUtilsTest PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/git/feature_operators/CMakeLists.txt;46;add_test;/home/<USER>/git/feature_operators/CMakeLists.txt;0;")
add_test(LogicalOpsTest "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build/logical_ops_tests")
set_tests_properties(LogicalOpsTest PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/git/feature_operators/CMakeLists.txt;50;add_test;/home/<USER>/git/feature_operators/CMakeLists.txt;0;")
add_test(ComparisonOpsTest "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build/comparison_ops_tests")
set_tests_properties(ComparisonOpsTest PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/git/feature_operators/CMakeLists.txt;54;add_test;/home/<USER>/git/feature_operators/CMakeLists.txt;0;")
add_test(ReductionOpsTest "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build/reduction_ops_tests")
set_tests_properties(ReductionOpsTest PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/git/feature_operators/CMakeLists.txt;58;add_test;/home/<USER>/git/feature_operators/CMakeLists.txt;0;")
add_test(PanelOpsTest "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build/panel_ops_tests")
set_tests_properties(PanelOpsTest PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/git/feature_operators/CMakeLists.txt;62;add_test;/home/<USER>/git/feature_operators/CMakeLists.txt;0;")
add_test(TimeSeriesOpsTest "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build/timeseries_ops_tests")
set_tests_properties(TimeSeriesOpsTest PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/git/feature_operators/CMakeLists.txt;66;add_test;/home/<USER>/git/feature_operators/CMakeLists.txt;0;")
add_test(GroupOpsTest "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build/group_ops_tests")
set_tests_properties(GroupOpsTest PROPERTIES  _BACKTRACE_TRIPLES "/home/<USER>/git/feature_operators/CMakeLists.txt;70;add_test;/home/<USER>/git/feature_operators/CMakeLists.txt;0;")
