# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/git/feature_operators/test_right

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/git/feature_operators/test_right/build

# Include any dependencies generated for this target.
include feature_operators_build/CMakeFiles/reduction_ops_tests.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include feature_operators_build/CMakeFiles/reduction_ops_tests.dir/compiler_depend.make

# Include the progress variables for this target.
include feature_operators_build/CMakeFiles/reduction_ops_tests.dir/progress.make

# Include the compile flags for this target's objects.
include feature_operators_build/CMakeFiles/reduction_ops_tests.dir/flags.make

feature_operators_build/CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.o: feature_operators_build/CMakeFiles/reduction_ops_tests.dir/flags.make
feature_operators_build/CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.o: /home/<USER>/git/feature_operators/tests/test_reduction_ops.cpp
feature_operators_build/CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.o: feature_operators_build/CMakeFiles/reduction_ops_tests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object feature_operators_build/CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.o"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT feature_operators_build/CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.o -MF CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.o.d -o CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.o -c /home/<USER>/git/feature_operators/tests/test_reduction_ops.cpp

feature_operators_build/CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.i"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/feature_operators/tests/test_reduction_ops.cpp > CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.i

feature_operators_build/CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.s"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/feature_operators/tests/test_reduction_ops.cpp -o CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.s

# Object files for target reduction_ops_tests
reduction_ops_tests_OBJECTS = \
"CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.o"

# External object files for target reduction_ops_tests
reduction_ops_tests_EXTERNAL_OBJECTS =

feature_operators_build/reduction_ops_tests: feature_operators_build/CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.o
feature_operators_build/reduction_ops_tests: feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build.make
feature_operators_build/reduction_ops_tests: feature_operators_build/libfeature_ops_lib.a
feature_operators_build/reduction_ops_tests: /usr/local/lib64/libCatch2Main.a
feature_operators_build/reduction_ops_tests: /usr/local/lib64/libCatch2.a
feature_operators_build/reduction_ops_tests: feature_operators_build/CMakeFiles/reduction_ops_tests.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable reduction_ops_tests"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/reduction_ops_tests.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build: feature_operators_build/reduction_ops_tests
.PHONY : feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build

feature_operators_build/CMakeFiles/reduction_ops_tests.dir/clean:
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && $(CMAKE_COMMAND) -P CMakeFiles/reduction_ops_tests.dir/cmake_clean.cmake
.PHONY : feature_operators_build/CMakeFiles/reduction_ops_tests.dir/clean

feature_operators_build/CMakeFiles/reduction_ops_tests.dir/depend:
	cd /home/<USER>/git/feature_operators/test_right/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/git/feature_operators/test_right /home/<USER>/git/feature_operators /home/<USER>/git/feature_operators/test_right/build /home/<USER>/git/feature_operators/test_right/build/feature_operators_build /home/<USER>/git/feature_operators/test_right/build/feature_operators_build/CMakeFiles/reduction_ops_tests.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : feature_operators_build/CMakeFiles/reduction_ops_tests.dir/depend

