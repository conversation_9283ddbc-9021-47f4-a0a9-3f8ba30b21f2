# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/git/feature_operators/test_right

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/git/feature_operators/test_right/build

# Include any dependencies generated for this target.
include feature_operators_build/CMakeFiles/feature_ops_lib.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include feature_operators_build/CMakeFiles/feature_ops_lib.dir/compiler_depend.make

# Include the progress variables for this target.
include feature_operators_build/CMakeFiles/feature_ops_lib.dir/progress.make

# Include the compile flags for this target's objects.
include feature_operators_build/CMakeFiles/feature_ops_lib.dir/flags.make

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/flags.make
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.o: /home/<USER>/git/feature_operators/src/core_math.cpp
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.o"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.o -MF CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.o.d -o CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.o -c /home/<USER>/git/feature_operators/src/core_math.cpp

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.i"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/feature_operators/src/core_math.cpp > CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.i

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.s"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/feature_operators/src/core_math.cpp -o CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.s

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/flags.make
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.o: /home/<USER>/git/feature_operators/src/data_utils.cpp
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.o"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.o -MF CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.o.d -o CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.o -c /home/<USER>/git/feature_operators/src/data_utils.cpp

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.i"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/feature_operators/src/data_utils.cpp > CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.i

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.s"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/feature_operators/src/data_utils.cpp -o CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.s

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/flags.make
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.o: /home/<USER>/git/feature_operators/src/logical_ops.cpp
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.o"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.o -MF CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.o.d -o CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.o -c /home/<USER>/git/feature_operators/src/logical_ops.cpp

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.i"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/feature_operators/src/logical_ops.cpp > CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.i

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.s"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/feature_operators/src/logical_ops.cpp -o CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.s

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/flags.make
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.o: /home/<USER>/git/feature_operators/src/comparison_ops.cpp
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.o"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.o -MF CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.o.d -o CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.o -c /home/<USER>/git/feature_operators/src/comparison_ops.cpp

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.i"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/feature_operators/src/comparison_ops.cpp > CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.i

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.s"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/feature_operators/src/comparison_ops.cpp -o CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.s

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/flags.make
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.o: /home/<USER>/git/feature_operators/src/reduction_ops.cpp
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.o"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.o -MF CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.o.d -o CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.o -c /home/<USER>/git/feature_operators/src/reduction_ops.cpp

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.i"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/feature_operators/src/reduction_ops.cpp > CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.i

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.s"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/feature_operators/src/reduction_ops.cpp -o CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.s

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/flags.make
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.o: /home/<USER>/git/feature_operators/src/panel_ops.cpp
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.o"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.o -MF CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.o.d -o CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.o -c /home/<USER>/git/feature_operators/src/panel_ops.cpp

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.i"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/feature_operators/src/panel_ops.cpp > CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.i

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.s"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/feature_operators/src/panel_ops.cpp -o CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.s

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/flags.make
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.o: /home/<USER>/git/feature_operators/src/timeseries_ops.cpp
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.o"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.o -MF CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.o.d -o CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.o -c /home/<USER>/git/feature_operators/src/timeseries_ops.cpp

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.i"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/feature_operators/src/timeseries_ops.cpp > CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.i

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.s"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/feature_operators/src/timeseries_ops.cpp -o CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.s

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/flags.make
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o: /home/<USER>/git/feature_operators/src/rolling_aggregations.cpp
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o -MF CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o.d -o CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o -c /home/<USER>/git/feature_operators/src/rolling_aggregations.cpp

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.i"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/feature_operators/src/rolling_aggregations.cpp > CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.i

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.s"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/feature_operators/src/rolling_aggregations.cpp -o CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.s

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/flags.make
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.o: /home/<USER>/git/feature_operators/src/timeseries_ops_v2.cpp
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.o"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.o -MF CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.o.d -o CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.o -c /home/<USER>/git/feature_operators/src/timeseries_ops_v2.cpp

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.i"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/feature_operators/src/timeseries_ops_v2.cpp > CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.i

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.s"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/feature_operators/src/timeseries_ops_v2.cpp -o CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.s

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/flags.make
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.o: /home/<USER>/git/feature_operators/src/group_ops.cpp
feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.o: feature_operators_build/CMakeFiles/feature_ops_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.o"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.o -MF CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.o.d -o CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.o -c /home/<USER>/git/feature_operators/src/group_ops.cpp

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.i"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/feature_operators/src/group_ops.cpp > CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.i

feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.s"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && /opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/feature_operators/src/group_ops.cpp -o CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.s

# Object files for target feature_ops_lib
feature_ops_lib_OBJECTS = \
"CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.o" \
"CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.o" \
"CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.o" \
"CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.o" \
"CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.o" \
"CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.o" \
"CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.o" \
"CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o" \
"CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.o" \
"CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.o"

# External object files for target feature_ops_lib
feature_ops_lib_EXTERNAL_OBJECTS =

feature_operators_build/libfeature_ops_lib.a: feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.o
feature_operators_build/libfeature_ops_lib.a: feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.o
feature_operators_build/libfeature_ops_lib.a: feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.o
feature_operators_build/libfeature_ops_lib.a: feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.o
feature_operators_build/libfeature_ops_lib.a: feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.o
feature_operators_build/libfeature_ops_lib.a: feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.o
feature_operators_build/libfeature_ops_lib.a: feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.o
feature_operators_build/libfeature_ops_lib.a: feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o
feature_operators_build/libfeature_ops_lib.a: feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.o
feature_operators_build/libfeature_ops_lib.a: feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.o
feature_operators_build/libfeature_ops_lib.a: feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make
feature_operators_build/libfeature_ops_lib.a: feature_operators_build/CMakeFiles/feature_ops_lib.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Linking CXX static library libfeature_ops_lib.a"
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && $(CMAKE_COMMAND) -P CMakeFiles/feature_ops_lib.dir/cmake_clean_target.cmake
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/feature_ops_lib.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
feature_operators_build/CMakeFiles/feature_ops_lib.dir/build: feature_operators_build/libfeature_ops_lib.a
.PHONY : feature_operators_build/CMakeFiles/feature_ops_lib.dir/build

feature_operators_build/CMakeFiles/feature_ops_lib.dir/clean:
	cd /home/<USER>/git/feature_operators/test_right/build/feature_operators_build && $(CMAKE_COMMAND) -P CMakeFiles/feature_ops_lib.dir/cmake_clean.cmake
.PHONY : feature_operators_build/CMakeFiles/feature_ops_lib.dir/clean

feature_operators_build/CMakeFiles/feature_ops_lib.dir/depend:
	cd /home/<USER>/git/feature_operators/test_right/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/git/feature_operators/test_right /home/<USER>/git/feature_operators /home/<USER>/git/feature_operators/test_right/build /home/<USER>/git/feature_operators/test_right/build/feature_operators_build /home/<USER>/git/feature_operators/test_right/build/feature_operators_build/CMakeFiles/feature_ops_lib.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : feature_operators_build/CMakeFiles/feature_ops_lib.dir/depend

