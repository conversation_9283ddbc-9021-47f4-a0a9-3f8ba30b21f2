# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/git/feature_operators/test_right

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/git/feature_operators/test_right/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/git/feature_operators/test_right/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles /home/<USER>/git/feature_operators/test_right/build/feature_operators_build//CMakeFiles/progress.marks
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/git/feature_operators/test_right/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
feature_operators_build/CMakeFiles/feature_ops_lib.dir/rule:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/feature_ops_lib.dir/rule
.PHONY : feature_operators_build/CMakeFiles/feature_ops_lib.dir/rule

# Convenience name for target.
feature_ops_lib: feature_operators_build/CMakeFiles/feature_ops_lib.dir/rule
.PHONY : feature_ops_lib

# fast build rule for target.
feature_ops_lib/fast:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/build
.PHONY : feature_ops_lib/fast

# Convenience name for target.
feature_operators_build/CMakeFiles/core_math_tests.dir/rule:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/core_math_tests.dir/rule
.PHONY : feature_operators_build/CMakeFiles/core_math_tests.dir/rule

# Convenience name for target.
core_math_tests: feature_operators_build/CMakeFiles/core_math_tests.dir/rule
.PHONY : core_math_tests

# fast build rule for target.
core_math_tests/fast:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/core_math_tests.dir/build.make feature_operators_build/CMakeFiles/core_math_tests.dir/build
.PHONY : core_math_tests/fast

# Convenience name for target.
feature_operators_build/CMakeFiles/data_utils_tests.dir/rule:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/data_utils_tests.dir/rule
.PHONY : feature_operators_build/CMakeFiles/data_utils_tests.dir/rule

# Convenience name for target.
data_utils_tests: feature_operators_build/CMakeFiles/data_utils_tests.dir/rule
.PHONY : data_utils_tests

# fast build rule for target.
data_utils_tests/fast:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/data_utils_tests.dir/build.make feature_operators_build/CMakeFiles/data_utils_tests.dir/build
.PHONY : data_utils_tests/fast

# Convenience name for target.
feature_operators_build/CMakeFiles/logical_ops_tests.dir/rule:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/logical_ops_tests.dir/rule
.PHONY : feature_operators_build/CMakeFiles/logical_ops_tests.dir/rule

# Convenience name for target.
logical_ops_tests: feature_operators_build/CMakeFiles/logical_ops_tests.dir/rule
.PHONY : logical_ops_tests

# fast build rule for target.
logical_ops_tests/fast:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/logical_ops_tests.dir/build.make feature_operators_build/CMakeFiles/logical_ops_tests.dir/build
.PHONY : logical_ops_tests/fast

# Convenience name for target.
feature_operators_build/CMakeFiles/comparison_ops_tests.dir/rule:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/comparison_ops_tests.dir/rule
.PHONY : feature_operators_build/CMakeFiles/comparison_ops_tests.dir/rule

# Convenience name for target.
comparison_ops_tests: feature_operators_build/CMakeFiles/comparison_ops_tests.dir/rule
.PHONY : comparison_ops_tests

# fast build rule for target.
comparison_ops_tests/fast:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build.make feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build
.PHONY : comparison_ops_tests/fast

# Convenience name for target.
feature_operators_build/CMakeFiles/reduction_ops_tests.dir/rule:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/reduction_ops_tests.dir/rule
.PHONY : feature_operators_build/CMakeFiles/reduction_ops_tests.dir/rule

# Convenience name for target.
reduction_ops_tests: feature_operators_build/CMakeFiles/reduction_ops_tests.dir/rule
.PHONY : reduction_ops_tests

# fast build rule for target.
reduction_ops_tests/fast:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build.make feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build
.PHONY : reduction_ops_tests/fast

# Convenience name for target.
feature_operators_build/CMakeFiles/panel_ops_tests.dir/rule:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/panel_ops_tests.dir/rule
.PHONY : feature_operators_build/CMakeFiles/panel_ops_tests.dir/rule

# Convenience name for target.
panel_ops_tests: feature_operators_build/CMakeFiles/panel_ops_tests.dir/rule
.PHONY : panel_ops_tests

# fast build rule for target.
panel_ops_tests/fast:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/panel_ops_tests.dir/build.make feature_operators_build/CMakeFiles/panel_ops_tests.dir/build
.PHONY : panel_ops_tests/fast

# Convenience name for target.
feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/rule:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/rule
.PHONY : feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/rule

# Convenience name for target.
timeseries_ops_tests: feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/rule
.PHONY : timeseries_ops_tests

# fast build rule for target.
timeseries_ops_tests/fast:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build.make feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build
.PHONY : timeseries_ops_tests/fast

# Convenience name for target.
feature_operators_build/CMakeFiles/group_ops_tests.dir/rule:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/group_ops_tests.dir/rule
.PHONY : feature_operators_build/CMakeFiles/group_ops_tests.dir/rule

# Convenience name for target.
group_ops_tests: feature_operators_build/CMakeFiles/group_ops_tests.dir/rule
.PHONY : group_ops_tests

# fast build rule for target.
group_ops_tests/fast:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/group_ops_tests.dir/build.make feature_operators_build/CMakeFiles/group_ops_tests.dir/build
.PHONY : group_ops_tests/fast

src/comparison_ops.o: src/comparison_ops.cpp.o
.PHONY : src/comparison_ops.o

# target to build an object file
src/comparison_ops.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.o
.PHONY : src/comparison_ops.cpp.o

src/comparison_ops.i: src/comparison_ops.cpp.i
.PHONY : src/comparison_ops.i

# target to preprocess a source file
src/comparison_ops.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.i
.PHONY : src/comparison_ops.cpp.i

src/comparison_ops.s: src/comparison_ops.cpp.s
.PHONY : src/comparison_ops.s

# target to generate assembly for a file
src/comparison_ops.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.s
.PHONY : src/comparison_ops.cpp.s

src/core_math.o: src/core_math.cpp.o
.PHONY : src/core_math.o

# target to build an object file
src/core_math.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.o
.PHONY : src/core_math.cpp.o

src/core_math.i: src/core_math.cpp.i
.PHONY : src/core_math.i

# target to preprocess a source file
src/core_math.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.i
.PHONY : src/core_math.cpp.i

src/core_math.s: src/core_math.cpp.s
.PHONY : src/core_math.s

# target to generate assembly for a file
src/core_math.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.s
.PHONY : src/core_math.cpp.s

src/data_utils.o: src/data_utils.cpp.o
.PHONY : src/data_utils.o

# target to build an object file
src/data_utils.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.o
.PHONY : src/data_utils.cpp.o

src/data_utils.i: src/data_utils.cpp.i
.PHONY : src/data_utils.i

# target to preprocess a source file
src/data_utils.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.i
.PHONY : src/data_utils.cpp.i

src/data_utils.s: src/data_utils.cpp.s
.PHONY : src/data_utils.s

# target to generate assembly for a file
src/data_utils.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.s
.PHONY : src/data_utils.cpp.s

src/group_ops.o: src/group_ops.cpp.o
.PHONY : src/group_ops.o

# target to build an object file
src/group_ops.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.o
.PHONY : src/group_ops.cpp.o

src/group_ops.i: src/group_ops.cpp.i
.PHONY : src/group_ops.i

# target to preprocess a source file
src/group_ops.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.i
.PHONY : src/group_ops.cpp.i

src/group_ops.s: src/group_ops.cpp.s
.PHONY : src/group_ops.s

# target to generate assembly for a file
src/group_ops.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.s
.PHONY : src/group_ops.cpp.s

src/logical_ops.o: src/logical_ops.cpp.o
.PHONY : src/logical_ops.o

# target to build an object file
src/logical_ops.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.o
.PHONY : src/logical_ops.cpp.o

src/logical_ops.i: src/logical_ops.cpp.i
.PHONY : src/logical_ops.i

# target to preprocess a source file
src/logical_ops.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.i
.PHONY : src/logical_ops.cpp.i

src/logical_ops.s: src/logical_ops.cpp.s
.PHONY : src/logical_ops.s

# target to generate assembly for a file
src/logical_ops.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.s
.PHONY : src/logical_ops.cpp.s

src/panel_ops.o: src/panel_ops.cpp.o
.PHONY : src/panel_ops.o

# target to build an object file
src/panel_ops.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.o
.PHONY : src/panel_ops.cpp.o

src/panel_ops.i: src/panel_ops.cpp.i
.PHONY : src/panel_ops.i

# target to preprocess a source file
src/panel_ops.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.i
.PHONY : src/panel_ops.cpp.i

src/panel_ops.s: src/panel_ops.cpp.s
.PHONY : src/panel_ops.s

# target to generate assembly for a file
src/panel_ops.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.s
.PHONY : src/panel_ops.cpp.s

src/reduction_ops.o: src/reduction_ops.cpp.o
.PHONY : src/reduction_ops.o

# target to build an object file
src/reduction_ops.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.o
.PHONY : src/reduction_ops.cpp.o

src/reduction_ops.i: src/reduction_ops.cpp.i
.PHONY : src/reduction_ops.i

# target to preprocess a source file
src/reduction_ops.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.i
.PHONY : src/reduction_ops.cpp.i

src/reduction_ops.s: src/reduction_ops.cpp.s
.PHONY : src/reduction_ops.s

# target to generate assembly for a file
src/reduction_ops.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.s
.PHONY : src/reduction_ops.cpp.s

src/rolling_aggregations.o: src/rolling_aggregations.cpp.o
.PHONY : src/rolling_aggregations.o

# target to build an object file
src/rolling_aggregations.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o
.PHONY : src/rolling_aggregations.cpp.o

src/rolling_aggregations.i: src/rolling_aggregations.cpp.i
.PHONY : src/rolling_aggregations.i

# target to preprocess a source file
src/rolling_aggregations.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.i
.PHONY : src/rolling_aggregations.cpp.i

src/rolling_aggregations.s: src/rolling_aggregations.cpp.s
.PHONY : src/rolling_aggregations.s

# target to generate assembly for a file
src/rolling_aggregations.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.s
.PHONY : src/rolling_aggregations.cpp.s

src/timeseries_ops.o: src/timeseries_ops.cpp.o
.PHONY : src/timeseries_ops.o

# target to build an object file
src/timeseries_ops.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.o
.PHONY : src/timeseries_ops.cpp.o

src/timeseries_ops.i: src/timeseries_ops.cpp.i
.PHONY : src/timeseries_ops.i

# target to preprocess a source file
src/timeseries_ops.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.i
.PHONY : src/timeseries_ops.cpp.i

src/timeseries_ops.s: src/timeseries_ops.cpp.s
.PHONY : src/timeseries_ops.s

# target to generate assembly for a file
src/timeseries_ops.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.s
.PHONY : src/timeseries_ops.cpp.s

src/timeseries_ops_v2.o: src/timeseries_ops_v2.cpp.o
.PHONY : src/timeseries_ops_v2.o

# target to build an object file
src/timeseries_ops_v2.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.o
.PHONY : src/timeseries_ops_v2.cpp.o

src/timeseries_ops_v2.i: src/timeseries_ops_v2.cpp.i
.PHONY : src/timeseries_ops_v2.i

# target to preprocess a source file
src/timeseries_ops_v2.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.i
.PHONY : src/timeseries_ops_v2.cpp.i

src/timeseries_ops_v2.s: src/timeseries_ops_v2.cpp.s
.PHONY : src/timeseries_ops_v2.s

# target to generate assembly for a file
src/timeseries_ops_v2.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.s
.PHONY : src/timeseries_ops_v2.cpp.s

tests/test_comparison_ops.o: tests/test_comparison_ops.cpp.o
.PHONY : tests/test_comparison_ops.o

# target to build an object file
tests/test_comparison_ops.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build.make feature_operators_build/CMakeFiles/comparison_ops_tests.dir/tests/test_comparison_ops.cpp.o
.PHONY : tests/test_comparison_ops.cpp.o

tests/test_comparison_ops.i: tests/test_comparison_ops.cpp.i
.PHONY : tests/test_comparison_ops.i

# target to preprocess a source file
tests/test_comparison_ops.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build.make feature_operators_build/CMakeFiles/comparison_ops_tests.dir/tests/test_comparison_ops.cpp.i
.PHONY : tests/test_comparison_ops.cpp.i

tests/test_comparison_ops.s: tests/test_comparison_ops.cpp.s
.PHONY : tests/test_comparison_ops.s

# target to generate assembly for a file
tests/test_comparison_ops.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build.make feature_operators_build/CMakeFiles/comparison_ops_tests.dir/tests/test_comparison_ops.cpp.s
.PHONY : tests/test_comparison_ops.cpp.s

tests/test_core_math.o: tests/test_core_math.cpp.o
.PHONY : tests/test_core_math.o

# target to build an object file
tests/test_core_math.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/core_math_tests.dir/build.make feature_operators_build/CMakeFiles/core_math_tests.dir/tests/test_core_math.cpp.o
.PHONY : tests/test_core_math.cpp.o

tests/test_core_math.i: tests/test_core_math.cpp.i
.PHONY : tests/test_core_math.i

# target to preprocess a source file
tests/test_core_math.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/core_math_tests.dir/build.make feature_operators_build/CMakeFiles/core_math_tests.dir/tests/test_core_math.cpp.i
.PHONY : tests/test_core_math.cpp.i

tests/test_core_math.s: tests/test_core_math.cpp.s
.PHONY : tests/test_core_math.s

# target to generate assembly for a file
tests/test_core_math.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/core_math_tests.dir/build.make feature_operators_build/CMakeFiles/core_math_tests.dir/tests/test_core_math.cpp.s
.PHONY : tests/test_core_math.cpp.s

tests/test_data_utils.o: tests/test_data_utils.cpp.o
.PHONY : tests/test_data_utils.o

# target to build an object file
tests/test_data_utils.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/data_utils_tests.dir/build.make feature_operators_build/CMakeFiles/data_utils_tests.dir/tests/test_data_utils.cpp.o
.PHONY : tests/test_data_utils.cpp.o

tests/test_data_utils.i: tests/test_data_utils.cpp.i
.PHONY : tests/test_data_utils.i

# target to preprocess a source file
tests/test_data_utils.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/data_utils_tests.dir/build.make feature_operators_build/CMakeFiles/data_utils_tests.dir/tests/test_data_utils.cpp.i
.PHONY : tests/test_data_utils.cpp.i

tests/test_data_utils.s: tests/test_data_utils.cpp.s
.PHONY : tests/test_data_utils.s

# target to generate assembly for a file
tests/test_data_utils.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/data_utils_tests.dir/build.make feature_operators_build/CMakeFiles/data_utils_tests.dir/tests/test_data_utils.cpp.s
.PHONY : tests/test_data_utils.cpp.s

tests/test_group_ops.o: tests/test_group_ops.cpp.o
.PHONY : tests/test_group_ops.o

# target to build an object file
tests/test_group_ops.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/group_ops_tests.dir/build.make feature_operators_build/CMakeFiles/group_ops_tests.dir/tests/test_group_ops.cpp.o
.PHONY : tests/test_group_ops.cpp.o

tests/test_group_ops.i: tests/test_group_ops.cpp.i
.PHONY : tests/test_group_ops.i

# target to preprocess a source file
tests/test_group_ops.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/group_ops_tests.dir/build.make feature_operators_build/CMakeFiles/group_ops_tests.dir/tests/test_group_ops.cpp.i
.PHONY : tests/test_group_ops.cpp.i

tests/test_group_ops.s: tests/test_group_ops.cpp.s
.PHONY : tests/test_group_ops.s

# target to generate assembly for a file
tests/test_group_ops.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/group_ops_tests.dir/build.make feature_operators_build/CMakeFiles/group_ops_tests.dir/tests/test_group_ops.cpp.s
.PHONY : tests/test_group_ops.cpp.s

tests/test_logical_ops.o: tests/test_logical_ops.cpp.o
.PHONY : tests/test_logical_ops.o

# target to build an object file
tests/test_logical_ops.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/logical_ops_tests.dir/build.make feature_operators_build/CMakeFiles/logical_ops_tests.dir/tests/test_logical_ops.cpp.o
.PHONY : tests/test_logical_ops.cpp.o

tests/test_logical_ops.i: tests/test_logical_ops.cpp.i
.PHONY : tests/test_logical_ops.i

# target to preprocess a source file
tests/test_logical_ops.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/logical_ops_tests.dir/build.make feature_operators_build/CMakeFiles/logical_ops_tests.dir/tests/test_logical_ops.cpp.i
.PHONY : tests/test_logical_ops.cpp.i

tests/test_logical_ops.s: tests/test_logical_ops.cpp.s
.PHONY : tests/test_logical_ops.s

# target to generate assembly for a file
tests/test_logical_ops.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/logical_ops_tests.dir/build.make feature_operators_build/CMakeFiles/logical_ops_tests.dir/tests/test_logical_ops.cpp.s
.PHONY : tests/test_logical_ops.cpp.s

tests/test_panel_ops.o: tests/test_panel_ops.cpp.o
.PHONY : tests/test_panel_ops.o

# target to build an object file
tests/test_panel_ops.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/panel_ops_tests.dir/build.make feature_operators_build/CMakeFiles/panel_ops_tests.dir/tests/test_panel_ops.cpp.o
.PHONY : tests/test_panel_ops.cpp.o

tests/test_panel_ops.i: tests/test_panel_ops.cpp.i
.PHONY : tests/test_panel_ops.i

# target to preprocess a source file
tests/test_panel_ops.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/panel_ops_tests.dir/build.make feature_operators_build/CMakeFiles/panel_ops_tests.dir/tests/test_panel_ops.cpp.i
.PHONY : tests/test_panel_ops.cpp.i

tests/test_panel_ops.s: tests/test_panel_ops.cpp.s
.PHONY : tests/test_panel_ops.s

# target to generate assembly for a file
tests/test_panel_ops.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/panel_ops_tests.dir/build.make feature_operators_build/CMakeFiles/panel_ops_tests.dir/tests/test_panel_ops.cpp.s
.PHONY : tests/test_panel_ops.cpp.s

tests/test_reduction_ops.o: tests/test_reduction_ops.cpp.o
.PHONY : tests/test_reduction_ops.o

# target to build an object file
tests/test_reduction_ops.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build.make feature_operators_build/CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.o
.PHONY : tests/test_reduction_ops.cpp.o

tests/test_reduction_ops.i: tests/test_reduction_ops.cpp.i
.PHONY : tests/test_reduction_ops.i

# target to preprocess a source file
tests/test_reduction_ops.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build.make feature_operators_build/CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.i
.PHONY : tests/test_reduction_ops.cpp.i

tests/test_reduction_ops.s: tests/test_reduction_ops.cpp.s
.PHONY : tests/test_reduction_ops.s

# target to generate assembly for a file
tests/test_reduction_ops.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build.make feature_operators_build/CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.s
.PHONY : tests/test_reduction_ops.cpp.s

tests/test_timeseries_ops.o: tests/test_timeseries_ops.cpp.o
.PHONY : tests/test_timeseries_ops.o

# target to build an object file
tests/test_timeseries_ops.cpp.o:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build.make feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/tests/test_timeseries_ops.cpp.o
.PHONY : tests/test_timeseries_ops.cpp.o

tests/test_timeseries_ops.i: tests/test_timeseries_ops.cpp.i
.PHONY : tests/test_timeseries_ops.i

# target to preprocess a source file
tests/test_timeseries_ops.cpp.i:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build.make feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/tests/test_timeseries_ops.cpp.i
.PHONY : tests/test_timeseries_ops.cpp.i

tests/test_timeseries_ops.s: tests/test_timeseries_ops.cpp.s
.PHONY : tests/test_timeseries_ops.s

# target to generate assembly for a file
tests/test_timeseries_ops.cpp.s:
	cd /home/<USER>/git/feature_operators/test_right/build && $(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build.make feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/tests/test_timeseries_ops.cpp.s
.PHONY : tests/test_timeseries_ops.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... comparison_ops_tests"
	@echo "... core_math_tests"
	@echo "... data_utils_tests"
	@echo "... feature_ops_lib"
	@echo "... group_ops_tests"
	@echo "... logical_ops_tests"
	@echo "... panel_ops_tests"
	@echo "... reduction_ops_tests"
	@echo "... timeseries_ops_tests"
	@echo "... src/comparison_ops.o"
	@echo "... src/comparison_ops.i"
	@echo "... src/comparison_ops.s"
	@echo "... src/core_math.o"
	@echo "... src/core_math.i"
	@echo "... src/core_math.s"
	@echo "... src/data_utils.o"
	@echo "... src/data_utils.i"
	@echo "... src/data_utils.s"
	@echo "... src/group_ops.o"
	@echo "... src/group_ops.i"
	@echo "... src/group_ops.s"
	@echo "... src/logical_ops.o"
	@echo "... src/logical_ops.i"
	@echo "... src/logical_ops.s"
	@echo "... src/panel_ops.o"
	@echo "... src/panel_ops.i"
	@echo "... src/panel_ops.s"
	@echo "... src/reduction_ops.o"
	@echo "... src/reduction_ops.i"
	@echo "... src/reduction_ops.s"
	@echo "... src/rolling_aggregations.o"
	@echo "... src/rolling_aggregations.i"
	@echo "... src/rolling_aggregations.s"
	@echo "... src/timeseries_ops.o"
	@echo "... src/timeseries_ops.i"
	@echo "... src/timeseries_ops.s"
	@echo "... src/timeseries_ops_v2.o"
	@echo "... src/timeseries_ops_v2.i"
	@echo "... src/timeseries_ops_v2.s"
	@echo "... tests/test_comparison_ops.o"
	@echo "... tests/test_comparison_ops.i"
	@echo "... tests/test_comparison_ops.s"
	@echo "... tests/test_core_math.o"
	@echo "... tests/test_core_math.i"
	@echo "... tests/test_core_math.s"
	@echo "... tests/test_data_utils.o"
	@echo "... tests/test_data_utils.i"
	@echo "... tests/test_data_utils.s"
	@echo "... tests/test_group_ops.o"
	@echo "... tests/test_group_ops.i"
	@echo "... tests/test_group_ops.s"
	@echo "... tests/test_logical_ops.o"
	@echo "... tests/test_logical_ops.i"
	@echo "... tests/test_logical_ops.s"
	@echo "... tests/test_panel_ops.o"
	@echo "... tests/test_panel_ops.i"
	@echo "... tests/test_panel_ops.s"
	@echo "... tests/test_reduction_ops.o"
	@echo "... tests/test_reduction_ops.i"
	@echo "... tests/test_reduction_ops.s"
	@echo "... tests/test_timeseries_ops.o"
	@echo "... tests/test_timeseries_ops.i"
	@echo "... tests/test_timeseries_ops.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/git/feature_operators/test_right/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

