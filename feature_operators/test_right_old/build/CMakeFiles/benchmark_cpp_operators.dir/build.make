# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/git/feature_operators/test_right

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/git/feature_operators/test_right/build

# Include any dependencies generated for this target.
include CMakeFiles/benchmark_cpp_operators.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/benchmark_cpp_operators.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/benchmark_cpp_operators.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/benchmark_cpp_operators.dir/flags.make

CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.o: CMakeFiles/benchmark_cpp_operators.dir/flags.make
CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.o: /home/<USER>/git/feature_operators/test_right/benchmark_cpp_operators.cpp
CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.o: CMakeFiles/benchmark_cpp_operators.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.o"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.o -MF CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.o.d -o CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.o -c /home/<USER>/git/feature_operators/test_right/benchmark_cpp_operators.cpp

CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.i"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/git/feature_operators/test_right/benchmark_cpp_operators.cpp > CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.i

CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.s"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/git/feature_operators/test_right/benchmark_cpp_operators.cpp -o CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.s

# Object files for target benchmark_cpp_operators
benchmark_cpp_operators_OBJECTS = \
"CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.o"

# External object files for target benchmark_cpp_operators
benchmark_cpp_operators_EXTERNAL_OBJECTS =

benchmark_cpp_operators: CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.o
benchmark_cpp_operators: CMakeFiles/benchmark_cpp_operators.dir/build.make
benchmark_cpp_operators: feature_operators_build/libfeature_ops_lib.a
benchmark_cpp_operators: /usr/lib64/libopenblas.so
benchmark_cpp_operators: CMakeFiles/benchmark_cpp_operators.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable benchmark_cpp_operators"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/benchmark_cpp_operators.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/benchmark_cpp_operators.dir/build: benchmark_cpp_operators
.PHONY : CMakeFiles/benchmark_cpp_operators.dir/build

CMakeFiles/benchmark_cpp_operators.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/benchmark_cpp_operators.dir/cmake_clean.cmake
.PHONY : CMakeFiles/benchmark_cpp_operators.dir/clean

CMakeFiles/benchmark_cpp_operators.dir/depend:
	cd /home/<USER>/git/feature_operators/test_right/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/git/feature_operators/test_right /home/<USER>/git/feature_operators/test_right /home/<USER>/git/feature_operators/test_right/build /home/<USER>/git/feature_operators/test_right/build /home/<USER>/git/feature_operators/test_right/build/CMakeFiles/benchmark_cpp_operators.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/benchmark_cpp_operators.dir/depend

