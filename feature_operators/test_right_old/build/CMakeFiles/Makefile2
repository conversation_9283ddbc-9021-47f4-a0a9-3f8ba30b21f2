# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/git/feature_operators/test_right

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/git/feature_operators/test_right/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/test_optimized_operators.dir/all
all: CMakeFiles/benchmark_cpp_operators.dir/all
all: feature_operators_build/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: feature_operators_build/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/test_optimized_operators.dir/clean
clean: CMakeFiles/benchmark_cpp_operators.dir/clean
clean: feature_operators_build/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory feature_operators_build

# Recursive "all" directory target.
feature_operators_build/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/core_math_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/data_utils_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/logical_ops_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/comparison_ops_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/reduction_ops_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/panel_ops_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/all
feature_operators_build/all: feature_operators_build/CMakeFiles/group_ops_tests.dir/all
.PHONY : feature_operators_build/all

# Recursive "preinstall" directory target.
feature_operators_build/preinstall:
.PHONY : feature_operators_build/preinstall

# Recursive "clean" directory target.
feature_operators_build/clean: feature_operators_build/CMakeFiles/feature_ops_lib.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/core_math_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/data_utils_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/logical_ops_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/comparison_ops_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/reduction_ops_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/panel_ops_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/clean
feature_operators_build/clean: feature_operators_build/CMakeFiles/group_ops_tests.dir/clean
.PHONY : feature_operators_build/clean

#=============================================================================
# Target rules for target CMakeFiles/test_optimized_operators.dir

# All Build rule for target.
CMakeFiles/test_optimized_operators.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=28,29 "Built target test_optimized_operators"
.PHONY : CMakeFiles/test_optimized_operators.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_optimized_operators.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_optimized_operators.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : CMakeFiles/test_optimized_operators.dir/rule

# Convenience name for target.
test_optimized_operators: CMakeFiles/test_optimized_operators.dir/rule
.PHONY : test_optimized_operators

# clean rule for target.
CMakeFiles/test_optimized_operators.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/clean
.PHONY : CMakeFiles/test_optimized_operators.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/benchmark_cpp_operators.dir

# All Build rule for target.
CMakeFiles/benchmark_cpp_operators.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_cpp_operators.dir/build.make CMakeFiles/benchmark_cpp_operators.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_cpp_operators.dir/build.make CMakeFiles/benchmark_cpp_operators.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=1,2 "Built target benchmark_cpp_operators"
.PHONY : CMakeFiles/benchmark_cpp_operators.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/benchmark_cpp_operators.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/benchmark_cpp_operators.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : CMakeFiles/benchmark_cpp_operators.dir/rule

# Convenience name for target.
benchmark_cpp_operators: CMakeFiles/benchmark_cpp_operators.dir/rule
.PHONY : benchmark_cpp_operators

# clean rule for target.
CMakeFiles/benchmark_cpp_operators.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_cpp_operators.dir/build.make CMakeFiles/benchmark_cpp_operators.dir/clean
.PHONY : CMakeFiles/benchmark_cpp_operators.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/feature_ops_lib.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/feature_ops_lib.dir/all:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=9,10,11,12,13,14,15,16,17,18,19 "Built target feature_ops_lib"
.PHONY : feature_operators_build/CMakeFiles/feature_ops_lib.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/feature_ops_lib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 11
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/feature_ops_lib.dir/rule

# Convenience name for target.
feature_ops_lib: feature_operators_build/CMakeFiles/feature_ops_lib.dir/rule
.PHONY : feature_ops_lib

# clean rule for target.
feature_operators_build/CMakeFiles/feature_ops_lib.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/clean
.PHONY : feature_operators_build/CMakeFiles/feature_ops_lib.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/core_math_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/core_math_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/core_math_tests.dir/build.make feature_operators_build/CMakeFiles/core_math_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/core_math_tests.dir/build.make feature_operators_build/CMakeFiles/core_math_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=5,6 "Built target core_math_tests"
.PHONY : feature_operators_build/CMakeFiles/core_math_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/core_math_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/core_math_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/core_math_tests.dir/rule

# Convenience name for target.
core_math_tests: feature_operators_build/CMakeFiles/core_math_tests.dir/rule
.PHONY : core_math_tests

# clean rule for target.
feature_operators_build/CMakeFiles/core_math_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/core_math_tests.dir/build.make feature_operators_build/CMakeFiles/core_math_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/core_math_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/data_utils_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/data_utils_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/data_utils_tests.dir/build.make feature_operators_build/CMakeFiles/data_utils_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/data_utils_tests.dir/build.make feature_operators_build/CMakeFiles/data_utils_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=7,8 "Built target data_utils_tests"
.PHONY : feature_operators_build/CMakeFiles/data_utils_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/data_utils_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/data_utils_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/data_utils_tests.dir/rule

# Convenience name for target.
data_utils_tests: feature_operators_build/CMakeFiles/data_utils_tests.dir/rule
.PHONY : data_utils_tests

# clean rule for target.
feature_operators_build/CMakeFiles/data_utils_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/data_utils_tests.dir/build.make feature_operators_build/CMakeFiles/data_utils_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/data_utils_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/logical_ops_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/logical_ops_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/logical_ops_tests.dir/build.make feature_operators_build/CMakeFiles/logical_ops_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/logical_ops_tests.dir/build.make feature_operators_build/CMakeFiles/logical_ops_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=22,23 "Built target logical_ops_tests"
.PHONY : feature_operators_build/CMakeFiles/logical_ops_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/logical_ops_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/logical_ops_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/logical_ops_tests.dir/rule

# Convenience name for target.
logical_ops_tests: feature_operators_build/CMakeFiles/logical_ops_tests.dir/rule
.PHONY : logical_ops_tests

# clean rule for target.
feature_operators_build/CMakeFiles/logical_ops_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/logical_ops_tests.dir/build.make feature_operators_build/CMakeFiles/logical_ops_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/logical_ops_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/comparison_ops_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/comparison_ops_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build.make feature_operators_build/CMakeFiles/comparison_ops_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build.make feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=3,4 "Built target comparison_ops_tests"
.PHONY : feature_operators_build/CMakeFiles/comparison_ops_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/comparison_ops_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/comparison_ops_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/comparison_ops_tests.dir/rule

# Convenience name for target.
comparison_ops_tests: feature_operators_build/CMakeFiles/comparison_ops_tests.dir/rule
.PHONY : comparison_ops_tests

# clean rule for target.
feature_operators_build/CMakeFiles/comparison_ops_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build.make feature_operators_build/CMakeFiles/comparison_ops_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/comparison_ops_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/reduction_ops_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/reduction_ops_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build.make feature_operators_build/CMakeFiles/reduction_ops_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build.make feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=26,27 "Built target reduction_ops_tests"
.PHONY : feature_operators_build/CMakeFiles/reduction_ops_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/reduction_ops_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/reduction_ops_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/reduction_ops_tests.dir/rule

# Convenience name for target.
reduction_ops_tests: feature_operators_build/CMakeFiles/reduction_ops_tests.dir/rule
.PHONY : reduction_ops_tests

# clean rule for target.
feature_operators_build/CMakeFiles/reduction_ops_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build.make feature_operators_build/CMakeFiles/reduction_ops_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/reduction_ops_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/panel_ops_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/panel_ops_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/panel_ops_tests.dir/build.make feature_operators_build/CMakeFiles/panel_ops_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/panel_ops_tests.dir/build.make feature_operators_build/CMakeFiles/panel_ops_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=24,25 "Built target panel_ops_tests"
.PHONY : feature_operators_build/CMakeFiles/panel_ops_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/panel_ops_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/panel_ops_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/panel_ops_tests.dir/rule

# Convenience name for target.
panel_ops_tests: feature_operators_build/CMakeFiles/panel_ops_tests.dir/rule
.PHONY : panel_ops_tests

# clean rule for target.
feature_operators_build/CMakeFiles/panel_ops_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/panel_ops_tests.dir/build.make feature_operators_build/CMakeFiles/panel_ops_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/panel_ops_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/timeseries_ops_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build.make feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build.make feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=30,31 "Built target timeseries_ops_tests"
.PHONY : feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/rule

# Convenience name for target.
timeseries_ops_tests: feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/rule
.PHONY : timeseries_ops_tests

# clean rule for target.
feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build.make feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/clean

#=============================================================================
# Target rules for target feature_operators_build/CMakeFiles/group_ops_tests.dir

# All Build rule for target.
feature_operators_build/CMakeFiles/group_ops_tests.dir/all: feature_operators_build/CMakeFiles/feature_ops_lib.dir/all
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/group_ops_tests.dir/build.make feature_operators_build/CMakeFiles/group_ops_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/group_ops_tests.dir/build.make feature_operators_build/CMakeFiles/group_ops_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/git/feature_operators/test_right/build/CMakeFiles --progress-num=20,21 "Built target group_ops_tests"
.PHONY : feature_operators_build/CMakeFiles/group_ops_tests.dir/all

# Build rule for subdir invocation for target.
feature_operators_build/CMakeFiles/group_ops_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_operators_build/CMakeFiles/group_ops_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : feature_operators_build/CMakeFiles/group_ops_tests.dir/rule

# Convenience name for target.
group_ops_tests: feature_operators_build/CMakeFiles/group_ops_tests.dir/rule
.PHONY : group_ops_tests

# clean rule for target.
feature_operators_build/CMakeFiles/group_ops_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/group_ops_tests.dir/build.make feature_operators_build/CMakeFiles/group_ops_tests.dir/clean
.PHONY : feature_operators_build/CMakeFiles/group_ops_tests.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

