[{"directory": "/home/<USER>/git/feature_operators/test_right/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++ -DEIGEN_USE_BLAS -DEIGEN_USE_LAPACKE -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -o CMakeFiles/test_optimized_operators.dir/test_optimized_operators.cpp.o -c /home/<USER>/git/feature_operators/test_right/test_optimized_operators.cpp", "file": "/home/<USER>/git/feature_operators/test_right/test_optimized_operators.cpp", "output": "CMakeFiles/test_optimized_operators.dir/test_optimized_operators.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++ -DEIGEN_USE_BLAS -DEIGEN_USE_LAPACKE -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -o CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.o -c /home/<USER>/git/feature_operators/test_right/benchmark_cpp_operators.cpp", "file": "/home/<USER>/git/feature_operators/test_right/benchmark_cpp_operators.cpp", "output": "CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.o -c /home/<USER>/git/feature_operators/src/core_math.cpp", "file": "/home/<USER>/git/feature_operators/src/core_math.cpp", "output": "feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/core_math.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.o -c /home/<USER>/git/feature_operators/src/data_utils.cpp", "file": "/home/<USER>/git/feature_operators/src/data_utils.cpp", "output": "feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/data_utils.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.o -c /home/<USER>/git/feature_operators/src/logical_ops.cpp", "file": "/home/<USER>/git/feature_operators/src/logical_ops.cpp", "output": "feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/logical_ops.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.o -c /home/<USER>/git/feature_operators/src/comparison_ops.cpp", "file": "/home/<USER>/git/feature_operators/src/comparison_ops.cpp", "output": "feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/comparison_ops.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.o -c /home/<USER>/git/feature_operators/src/reduction_ops.cpp", "file": "/home/<USER>/git/feature_operators/src/reduction_ops.cpp", "output": "feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/reduction_ops.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.o -c /home/<USER>/git/feature_operators/src/panel_ops.cpp", "file": "/home/<USER>/git/feature_operators/src/panel_ops.cpp", "output": "feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/panel_ops.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.o -c /home/<USER>/git/feature_operators/src/timeseries_ops.cpp", "file": "/home/<USER>/git/feature_operators/src/timeseries_ops.cpp", "output": "feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o -c /home/<USER>/git/feature_operators/src/rolling_aggregations.cpp", "file": "/home/<USER>/git/feature_operators/src/rolling_aggregations.cpp", "output": "feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.o -c /home/<USER>/git/feature_operators/src/timeseries_ops_v2.cpp", "file": "/home/<USER>/git/feature_operators/src/timeseries_ops_v2.cpp", "output": "feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/timeseries_ops_v2.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.o -c /home/<USER>/git/feature_operators/src/group_ops.cpp", "file": "/home/<USER>/git/feature_operators/src/group_ops.cpp", "output": "feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/group_ops.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/core_math_tests.dir/tests/test_core_math.cpp.o -c /home/<USER>/git/feature_operators/tests/test_core_math.cpp", "file": "/home/<USER>/git/feature_operators/tests/test_core_math.cpp", "output": "feature_operators_build/CMakeFiles/core_math_tests.dir/tests/test_core_math.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/data_utils_tests.dir/tests/test_data_utils.cpp.o -c /home/<USER>/git/feature_operators/tests/test_data_utils.cpp", "file": "/home/<USER>/git/feature_operators/tests/test_data_utils.cpp", "output": "feature_operators_build/CMakeFiles/data_utils_tests.dir/tests/test_data_utils.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/logical_ops_tests.dir/tests/test_logical_ops.cpp.o -c /home/<USER>/git/feature_operators/tests/test_logical_ops.cpp", "file": "/home/<USER>/git/feature_operators/tests/test_logical_ops.cpp", "output": "feature_operators_build/CMakeFiles/logical_ops_tests.dir/tests/test_logical_ops.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/comparison_ops_tests.dir/tests/test_comparison_ops.cpp.o -c /home/<USER>/git/feature_operators/tests/test_comparison_ops.cpp", "file": "/home/<USER>/git/feature_operators/tests/test_comparison_ops.cpp", "output": "feature_operators_build/CMakeFiles/comparison_ops_tests.dir/tests/test_comparison_ops.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.o -c /home/<USER>/git/feature_operators/tests/test_reduction_ops.cpp", "file": "/home/<USER>/git/feature_operators/tests/test_reduction_ops.cpp", "output": "feature_operators_build/CMakeFiles/reduction_ops_tests.dir/tests/test_reduction_ops.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/panel_ops_tests.dir/tests/test_panel_ops.cpp.o -c /home/<USER>/git/feature_operators/tests/test_panel_ops.cpp", "file": "/home/<USER>/git/feature_operators/tests/test_panel_ops.cpp", "output": "feature_operators_build/CMakeFiles/panel_ops_tests.dir/tests/test_panel_ops.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/timeseries_ops_tests.dir/tests/test_timeseries_ops.cpp.o -c /home/<USER>/git/feature_operators/tests/test_timeseries_ops.cpp", "file": "/home/<USER>/git/feature_operators/tests/test_timeseries_ops.cpp", "output": "feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/tests/test_timeseries_ops.cpp.o"}, {"directory": "/home/<USER>/git/feature_operators/test_right/build/feature_operators_build", "command": "/opt/rh/gcc-toolset-11/root/usr/bin/c++  -I/home/<USER>/git/feature_operators/test_right/../include -I/home/<USER>/git/feature_operators/include -isystem /usr/local/include/eigen3 -std=gnu++17 -Wall -O3 -march=native -g -msse4 -o CMakeFiles/group_ops_tests.dir/tests/test_group_ops.cpp.o -c /home/<USER>/git/feature_operators/tests/test_group_ops.cpp", "file": "/home/<USER>/git/feature_operators/tests/test_group_ops.cpp", "output": "feature_operators_build/CMakeFiles/group_ops_tests.dir/tests/test_group_ops.cpp.o"}]