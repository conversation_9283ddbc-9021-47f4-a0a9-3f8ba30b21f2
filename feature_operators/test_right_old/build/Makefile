# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/git/feature_operators/test_right

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/git/feature_operators/test_right/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles /home/<USER>/git/feature_operators/test_right/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/git/feature_operators/test_right/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named test_optimized_operators

# Build rule for target.
test_optimized_operators: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_optimized_operators
.PHONY : test_optimized_operators

# fast build rule for target.
test_optimized_operators/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/build
.PHONY : test_optimized_operators/fast

#=============================================================================
# Target rules for targets named benchmark_cpp_operators

# Build rule for target.
benchmark_cpp_operators: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 benchmark_cpp_operators
.PHONY : benchmark_cpp_operators

# fast build rule for target.
benchmark_cpp_operators/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_cpp_operators.dir/build.make CMakeFiles/benchmark_cpp_operators.dir/build
.PHONY : benchmark_cpp_operators/fast

#=============================================================================
# Target rules for targets named feature_ops_lib

# Build rule for target.
feature_ops_lib: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 feature_ops_lib
.PHONY : feature_ops_lib

# fast build rule for target.
feature_ops_lib/fast:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/feature_ops_lib.dir/build.make feature_operators_build/CMakeFiles/feature_ops_lib.dir/build
.PHONY : feature_ops_lib/fast

#=============================================================================
# Target rules for targets named core_math_tests

# Build rule for target.
core_math_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_math_tests
.PHONY : core_math_tests

# fast build rule for target.
core_math_tests/fast:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/core_math_tests.dir/build.make feature_operators_build/CMakeFiles/core_math_tests.dir/build
.PHONY : core_math_tests/fast

#=============================================================================
# Target rules for targets named data_utils_tests

# Build rule for target.
data_utils_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 data_utils_tests
.PHONY : data_utils_tests

# fast build rule for target.
data_utils_tests/fast:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/data_utils_tests.dir/build.make feature_operators_build/CMakeFiles/data_utils_tests.dir/build
.PHONY : data_utils_tests/fast

#=============================================================================
# Target rules for targets named logical_ops_tests

# Build rule for target.
logical_ops_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 logical_ops_tests
.PHONY : logical_ops_tests

# fast build rule for target.
logical_ops_tests/fast:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/logical_ops_tests.dir/build.make feature_operators_build/CMakeFiles/logical_ops_tests.dir/build
.PHONY : logical_ops_tests/fast

#=============================================================================
# Target rules for targets named comparison_ops_tests

# Build rule for target.
comparison_ops_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 comparison_ops_tests
.PHONY : comparison_ops_tests

# fast build rule for target.
comparison_ops_tests/fast:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build.make feature_operators_build/CMakeFiles/comparison_ops_tests.dir/build
.PHONY : comparison_ops_tests/fast

#=============================================================================
# Target rules for targets named reduction_ops_tests

# Build rule for target.
reduction_ops_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 reduction_ops_tests
.PHONY : reduction_ops_tests

# fast build rule for target.
reduction_ops_tests/fast:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build.make feature_operators_build/CMakeFiles/reduction_ops_tests.dir/build
.PHONY : reduction_ops_tests/fast

#=============================================================================
# Target rules for targets named panel_ops_tests

# Build rule for target.
panel_ops_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 panel_ops_tests
.PHONY : panel_ops_tests

# fast build rule for target.
panel_ops_tests/fast:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/panel_ops_tests.dir/build.make feature_operators_build/CMakeFiles/panel_ops_tests.dir/build
.PHONY : panel_ops_tests/fast

#=============================================================================
# Target rules for targets named timeseries_ops_tests

# Build rule for target.
timeseries_ops_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 timeseries_ops_tests
.PHONY : timeseries_ops_tests

# fast build rule for target.
timeseries_ops_tests/fast:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build.make feature_operators_build/CMakeFiles/timeseries_ops_tests.dir/build
.PHONY : timeseries_ops_tests/fast

#=============================================================================
# Target rules for targets named group_ops_tests

# Build rule for target.
group_ops_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 group_ops_tests
.PHONY : group_ops_tests

# fast build rule for target.
group_ops_tests/fast:
	$(MAKE) $(MAKESILENT) -f feature_operators_build/CMakeFiles/group_ops_tests.dir/build.make feature_operators_build/CMakeFiles/group_ops_tests.dir/build
.PHONY : group_ops_tests/fast

benchmark_cpp_operators.o: benchmark_cpp_operators.cpp.o
.PHONY : benchmark_cpp_operators.o

# target to build an object file
benchmark_cpp_operators.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_cpp_operators.dir/build.make CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.o
.PHONY : benchmark_cpp_operators.cpp.o

benchmark_cpp_operators.i: benchmark_cpp_operators.cpp.i
.PHONY : benchmark_cpp_operators.i

# target to preprocess a source file
benchmark_cpp_operators.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_cpp_operators.dir/build.make CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.i
.PHONY : benchmark_cpp_operators.cpp.i

benchmark_cpp_operators.s: benchmark_cpp_operators.cpp.s
.PHONY : benchmark_cpp_operators.s

# target to generate assembly for a file
benchmark_cpp_operators.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/benchmark_cpp_operators.dir/build.make CMakeFiles/benchmark_cpp_operators.dir/benchmark_cpp_operators.cpp.s
.PHONY : benchmark_cpp_operators.cpp.s

test_optimized_operators.o: test_optimized_operators.cpp.o
.PHONY : test_optimized_operators.o

# target to build an object file
test_optimized_operators.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/test_optimized_operators.cpp.o
.PHONY : test_optimized_operators.cpp.o

test_optimized_operators.i: test_optimized_operators.cpp.i
.PHONY : test_optimized_operators.i

# target to preprocess a source file
test_optimized_operators.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/test_optimized_operators.cpp.i
.PHONY : test_optimized_operators.cpp.i

test_optimized_operators.s: test_optimized_operators.cpp.s
.PHONY : test_optimized_operators.s

# target to generate assembly for a file
test_optimized_operators.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_optimized_operators.dir/build.make CMakeFiles/test_optimized_operators.dir/test_optimized_operators.cpp.s
.PHONY : test_optimized_operators.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... benchmark_cpp_operators"
	@echo "... comparison_ops_tests"
	@echo "... core_math_tests"
	@echo "... data_utils_tests"
	@echo "... feature_ops_lib"
	@echo "... group_ops_tests"
	@echo "... logical_ops_tests"
	@echo "... panel_ops_tests"
	@echo "... reduction_ops_tests"
	@echo "... test_optimized_operators"
	@echo "... timeseries_ops_tests"
	@echo "... benchmark_cpp_operators.o"
	@echo "... benchmark_cpp_operators.i"
	@echo "... benchmark_cpp_operators.s"
	@echo "... test_optimized_operators.o"
	@echo "... test_optimized_operators.i"
	@echo "... test_optimized_operators.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

