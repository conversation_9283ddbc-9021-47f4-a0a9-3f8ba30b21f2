Configuring and building C++ tests...
-- Found generic BLAS: /usr/lib64/libopenblas.so
-- Eigen3 include directory: /usr/local/include/eigen3
-- Boost include directories: 
-- Boost libraries: 
-- Catch2 include directory: 
-- Configuring done (0.1s)
-- Generating done (0.1s)
-- Build files have been written to: /home/<USER>/git/feature_operators/test_right/build
[ 35%] Built target feature_ops_lib
[ 41%] Built target benchmark_cpp_operators
[ 48%] Built target core_math_tests
[ 54%] Built target test_optimized_operators
[ 61%] Built target logical_ops_tests
[ 67%] Built target data_utils_tests
[ 80%] Built target reduction_ops_tests
[ 80%] Built target comparison_ops_tests
[ 87%] Built target panel_ops_tests
[ 93%] Built target timeseries_ops_tests
[100%] Built target group_ops_tests
Running C++ operators test...
Loading test data...
Data loaded. Rows: 500, Cols: 518
Testing basic arithmetic operators...
Testing logical operators...
Testing comparison operators...
Testing data utility functions...
Testing reduction operators...
Testing time series operators...
Testing panel operators...
Testing group operators...
Testing Tot series functions...
Testing refactored time series operators (v2)...
index: 0 var_x_pop: 1.40105e-23 var_y_pop:0
index: 1 var_x_pop: 1.8225e-12 var_y_pop:3.77549e+11
index: 1 corr: -1 in:0.0008133 1.2289e+06
index: 2 var_x_pop: 1.37556e-12 var_y_pop:3.32274e+11
index: 2 corr: -0.649741 in:0.0008138 12300
index: 3 var_x_pop: 1.09188e-12 var_y_pop:2.81301e+11
index: 3 corr: -0.515133 in:0.0008138 0
index: 4 var_x_pop: 9.024e-13 var_y_pop:2.40446e+11
index: 4 corr: -0.445014 in:0.0008138 0
index: 5 var_x_pop: 2.03556e-12 var_y_pop:2.07664e+11
index: 5 corr: -0.116886 in:0.0008111 19100
index: 6 var_x_pop: 2.72776e-12 var_y_pop:1.8339e+11
index: 6 corr: 0.0108395 in:0.0008108 200
index: 7 var_x_pop: 4.53188e-12 var_y_pop:1.63413e+11
index: 7 corr: -0.0845861 in:0.0008088 344200
index: 8 var_x_pop: 5.51136e-12 var_y_pop:1.4923e+11
index: 8 corr: 0.013303 in:0.0008088 0
index: 9 var_x_pop: 5.5429e-12 var_y_pop:1.36254e+11
index: 9 corr: 0.051256 in:0.0008097 31200
index: 10 var_x_pop: 4.0624e-12 var_y_pop:1.36254e+11
index: 10 corr: 0.198398 in:0.0008097 0
index: 11 var_x_pop: 3.832e-12 var_y_pop:1.85544e+11
index: 11 corr: -0.312502 in:0.0008097 1.4412e+06
index: 12 var_x_pop: 2.9696e-12 var_y_pop:1.85043e+11
index: 12 corr: -0.289735 in:0.000811 27400
index: 13 var_x_pop: 1.92e-12 var_y_pop:1.84197e+11
index: 13 corr: -0.260082 in:0.0008106 24100
index: 14 var_x_pop: 7.049e-13 var_y_pop:1.83655e+11
index: 14 corr: -0.274476 in:0.0008109 14900
index: 15 var_x_pop: 6.104e-13 var_y_pop:1.82387e+11
index: 15 corr: -0.223537 in:0.0008096 60700
index: 16 var_x_pop: 7.82e-13 var_y_pop:2.43671e+11
index: 16 corr: -0.422433 in:0.0008082 1.0689e+06
index: 17 var_x_pop: 8.009e-13 var_y_pop:2.43468e+11
index: 17 corr: -0.408746 in:0.0008087 300400
index: 18 var_x_pop: 8.196e-13 var_y_pop:2.43468e+11
index: 18 corr: -0.39741 in:0.0008087 0
index: 19 var_x_pop: 9.056e-13 var_y_pop:2.45213e+11
index: 19 corr: -0.314516 in:0.0008087 0
index: 20 var_x_pop: 9.44e-13 var_y_pop:2.44853e+11
index: 20 corr: -0.260172 in:0.0008089 6200
index: 21 var_x_pop: 1.0969e-12 var_y_pop:1.07277e+11
index: 21 corr: -0.331965 in:0.0008108 475300
index: 22 var_x_pop: 1.4649e-12 var_y_pop:2.11939e+11
index: 22 corr: 0.292837 in:0.000812 1.3116e+06
index: 23 var_x_pop: 1.9344e-12 var_y_pop:2.23816e+11
index: 23 corr: 0.461768 in:0.0008121 854400
index: 24 var_x_pop: 2.0916e-12 var_y_pop:2.14968e+11
index: 24 corr: 0.448965 in:0.0008115 147000
index: 25 var_x_pop: 2.2196e-12 var_y_pop:2.19179e+11
index: 25 corr: 0.339886 in:0.0008112 6200
index: 26 var_x_pop: 1.9016e-12 var_y_pop:1.82645e+11
index: 26 corr: 0.585381 in:0.0008112 0
index: 27 var_x_pop: 1.6136e-12 var_y_pop:1.90642e+11
index: 27 corr: 0.592923 in:0.0008111 12900
index: 28 var_x_pop: 1.2104e-12 var_y_pop:2.58217e+11
index: 28 corr: 0.520412 in:0.0008111 1.2338e+06
index: 29 var_x_pop: 1.052e-12 var_y_pop:2.48547e+11
index: 29 corr: 0.270626 in:0.0008091 757600
index: 30 var_x_pop: 9.756e-13 var_y_pop:2.49139e+11
index: 30 corr: 0.263737 in:0.0008091 0
index: 31 var_x_pop: 1.2765e-12 var_y_pop:2.69906e+11
index: 31 corr: 0.356454 in:0.0008091 0
index: 32 var_x_pop: 1.3084e-12 var_y_pop:1.94088e+11
index: 32 corr: 0.263179 in:0.0008091 0
index: 33 var_x_pop: 1.1344e-12 var_y_pop:1.65255e+11
index: 33 corr: 0.132172 in:0.0008091 0
index: 34 var_x_pop: 1.0096e-12 var_y_pop:1.69221e+11
index: 34 corr: 0.207534 in:0.0008091 0
index: 35 var_x_pop: 8.624e-13 var_y_pop:1.68942e+11
index: 35 corr: 0.266643 in:0.0008104 13500
index: 36 var_x_pop: 1.4729e-12 var_y_pop:6.1891e+11
index: 36 corr: 0.776373 in:0.0008127 2.4714e+06
index: 37 var_x_pop: 2.1641e-12 var_y_pop:6.13016e+11
index: 37 corr: 0.592756 in:0.0008129 86000
index: 38 var_x_pop: 2.7905e-12 var_y_pop:5.58146e+11
index: 38 corr: 0.407112 in:0.0008129 0
index: 39 var_x_pop: 3.1401e-12 var_y_pop:5.45444e+11
index: 39 corr: 0.385936 in:0.0008129 0
index: 40 var_x_pop: 4.1369e-12 var_y_pop:5.40526e+11
index: 40 corr: 0.269195 in:0.0008147 121500
index: 41 var_x_pop: 4.5065e-12 var_y_pop:5.40526e+11
index: 41 corr: 0.161315 in:0.0008147 0
index: 42 var_x_pop: 3.6664e-12 var_y_pop:5.35095e+11
index: 42 corr: 0.120527 in:0.0008122 128400
index: 43 var_x_pop: 2.6584e-12 var_y_pop:5.34749e+11
index: 43 corr: 0.0750659 in:0.0008119 6200
index: 44 var_x_pop: 1.4936e-12 var_y_pop:5.34749e+11
index: 44 corr: 0.0115755 in:0.0008119 0
index: 45 var_x_pop: 1.0001e-12 var_y_pop:5.35492e+11
index: 45 corr: -0.0392527 in:0.0008119 0
index: 46 var_x_pop: 2.358e-12 var_y_pop:2.5937e+09
index: 46 corr: 0.237339 in:0.000809 21100
index: 47 var_x_pop: 3.5429e-12 var_y_pop:2.35128e+09
index: 47 corr: 0.246938 in:0.0008088 12000
index: 48 var_x_pop: 4.3916e-12 var_y_pop:2.35128e+09
index: 48 corr: 0.338483 in:0.0008088 0
index: 49 var_x_pop: 4.9041e-12 var_y_pop:2.35128e+09
index: 49 corr: 0.430729 in:0.0008088 0
index: 50 var_x_pop: 3.6849e-12 var_y_pop:1.5777e+09
index: 50 corr: 0.0918445 in:0.0008099 63200
index: 51 var_x_pop: 1.9921e-12 var_y_pop:1.57451e+09
index: 51 corr: 0.314403 in:0.0008101 700
index: 52 var_x_pop: 1.6036e-12 var_y_pop:3.95024e+08
index: 52 corr: -0.171086 in:0.0008101 35300
index: 53 var_x_pop: 1.8496e-12 var_y_pop:3.89444e+08
index: 53 corr: -0.0764046 in:0.0008125 17900
index: 54 var_x_pop: 1.9609e-12 var_y_pop:3.74702e+08
index: 54 corr: 0.108882 in:0.0008122 27400
index: 55 var_x_pop: 2.0704e-12 var_y_pop:3.74702e+08
index: 55 corr: 0.0868348 in:0.0008122 0
index: 56 var_x_pop: 1.9009e-12 var_y_pop:3.88934e+08
index: 56 corr: 0.111178 in:0.0008105 30500
index: 57 var_x_pop: 1.6204e-12 var_y_pop:4.17974e+08
index: 57 corr: 0.0751594 in:0.0008105 0
index: 58 var_x_pop: 1.4416e-12 var_y_pop:3.84193e+08
index: 58 corr: -0.0708845 in:0.0008094 21100
index: 59 var_x_pop: 1.1205e-12 var_y_pop:3.83412e+08
index: 59 corr: -0.204105 in:0.0008101 200
index: 60 var_x_pop: 1.2549e-12 var_y_pop:1.15602e+10
index: 60 corr: -0.41147 in:0.0008093 370500
index: 61 var_x_pop: 1.4069e-12 var_y_pop:1.15672e+10
index: 61 corr: -0.356629 in:0.0008093 0
index: 62 var_x_pop: 1.5461e-12 var_y_pop:1.17852e+10
index: 62 corr: -0.295986 in:0.0008093 0
index: 63 var_x_pop: 1.2069e-12 var_y_pop:1.19174e+10
index: 63 corr: -0.242557 in:0.0008093 0
index: 64 var_x_pop: 8.096e-13 var_y_pop:1.20812e+10
index: 64 corr: -0.225439 in:0.0008093 0
index: 65 var_x_pop: 2.345e-13 var_y_pop:1.19493e+10
index: 65 corr: -0.211401 in:0.0008095 19800
index: 66 var_x_pop: 2.184e-13 var_y_pop:2.14327e+10
index: 66 corr: 0.216347 in:0.0008104 370700
index: 67 var_x_pop: 3.016e-13 var_y_pop:2.13745e+10
index: 67 corr: 0.151154 in:0.0008109 3800
index: 68 var_x_pop: 6.14e-13 var_y_pop:2.1139e+10
index: 68 corr: 0.147972 in:0.0008116 123300
index: 69 var_x_pop: 9.776e-13 var_y_pop:2.03014e+10
index: 69 corr: 0.108093 in:0.0008119 78900
index: 70 var_x_pop: 1.1804e-12 var_y_pop:1.23672e+10
index: 70 corr: 0.236859 in:0.0008119 0
index: 71 var_x_pop: 1.8405e-12 var_y_pop:1.20131e+10
index: 71 corr: 0.184283 in:0.0008134 87700
index: 72 var_x_pop: 2.1644e-12 var_y_pop:1.20131e+10
index: 72 corr: -0.00403227 in:0.0008134 0
index: 73 var_x_pop: 2.1521e-12 var_y_pop:1.20131e+10
index: 73 corr: -0.178508 in:0.0008134 0
index: 74 var_x_pop: 1.8036e-12 var_y_pop:1.20131e+10
index: 74 corr: -0.38557 in:0.0008134 0
index: 75 var_x_pop: 1.2381e-12 var_y_pop:1.2241e+10
index: 75 corr: -0.631607 in:0.0008134 0
index: 76 var_x_pop: 1.558e-12 var_y_pop:3.00794e+09
index: 76 corr: -0.57379 in:0.0008097 140800
index: 77 var_x_pop: 2.0236e-12 var_y_pop:3.03938e+09
index: 77 corr: -0.428175 in:0.0008097 0
index: 78 var_x_pop: 2.5689e-12 var_y_pop:2.42917e+09
index: 78 corr: -0.260617 in:0.0008097 0
index: 79 var_x_pop: 3.0441e-12 var_y_pop:2.22947e+09
index: 79 corr: -0.180265 in:0.0008097 0
index: 80 var_x_pop: 3.3161e-12 var_y_pop:2.18174e+09
index: 80 corr: -0.190442 in:0.0008135 14700
index: 81 var_x_pop: 3.2609e-12 var_y_pop:1.73652e+09
index: 81 corr: -0.346793 in:0.0008132 20800
index: 82 var_x_pop: 3.2049e-12 var_y_pop:1.73652e+09
index: 82 corr: -0.345083 in:0.0008132 0
index: 83 var_x_pop: 3.1481e-12 var_y_pop:1.73652e+09
index: 83 corr: -0.343413 in:0.0008132 0
index: 84 var_x_pop: 3.0905e-12 var_y_pop:1.73652e+09
index: 84 corr: -0.341786 in:0.0008132 0
index: 85 var_x_pop: 2.8264e-12 var_y_pop:6.34005e+09
index: 85 corr: -0.191496 in:0.0008115 246600
index: 86 var_x_pop: 2.4241e-12 var_y_pop:5.34522e+09
index: 86 corr: -0.0493014 in:0.0008124 900
index: 87 var_x_pop: 1.8876e-12 var_y_pop:5.31359e+09
index: 87 corr: -0.135631 in:0.0008126 6200
index: 88 var_x_pop: 1.6176e-12 var_y_pop:5.44536e+09
index: 88 corr: -0.125596 in:0.0008147 82100
index: 89 var_x_pop: 8.476e-13 var_y_pop:5.44536e+09
index: 89 corr: -0.446773 in:0.0008147 0
index: 90 var_x_pop: 1.15536e-11 var_y_pop:1.18132e+12
index: 90 corr: 0.952785 in:0.0008241 3.6543e+06
index: 91 var_x_pop: 1.74756e-11 var_y_pop:1.17934e+12
index: 91 corr: 0.700989 in:0.0008226 47700
index: 92 var_x_pop: 2.10089e-11 var_y_pop:1.68959e+12
index: 92 corr: 0.762529 in:0.0008221 2.8716e+06
index: 93 var_x_pop: 2.2958e-11 var_y_pop:1.68959e+12
index: 93 corr: 0.630707 in:0.0008221 0
index: 94 var_x_pop: 2.30005e-11 var_y_pop:1.68901e+12
index: 94 corr: 0.536265 in:0.0008217 4200
index: 95 var_x_pop: 1.91496e-11 var_y_pop:1.66829e+12
index: 95 corr: 0.530861 in:0.0008212 622600
index: 96 var_x_pop: 1.53621e-11 var_y_pop:1.61262e+12
index: 96 corr: 0.508662 in:0.0008225 999300
index: 97 var_x_pop: 9.594e-12 var_y_pop:1.58148e+12
index: 97 corr: 0.469536 in:0.0008213 220600
index: 98 var_x_pop: 6.4404e-12 var_y_pop:1.52942e+12
index: 98 corr: 0.453634 in:0.0008183 548400
index: 99 var_x_pop: 3.0276e-12 var_y_pop:1.52942e+12
index: 99 corr: 0.511581 in:0.0008183 0
index: 100 var_x_pop: 3.4104e-12 var_y_pop:6.95223e+11
index: 100 corr: 0.25444 in:0.0008175 246800
index: 101 var_x_pop: 3.622e-12 var_y_pop:7.00069e+11
index: 101 corr: 0.399526 in:0.000818 1900
index: 102 var_x_pop: 3.3349e-12 var_y_pop:1.03383e+11
index: 102 corr: 0.310381 in:0.0008192 169500
index: 103 var_x_pop: 3.0204e-12 var_y_pop:9.98292e+10
index: 103 corr: 0.505359 in:0.0008184 71300
index: 104 var_x_pop: 3.0296e-12 var_y_pop:9.96651e+10
index: 104 corr: 0.741871 in:0.0008171 7100
index: 105 var_x_pop: 2.8861e-12 var_y_pop:9.29808e+10
index: 105 corr: 0.723419 in:0.0008171 0
index: 106 var_x_pop: 2.5429e-12 var_y_pop:2.67762e+10
index: 106 corr: 0.156426 in:0.0008147 182100
index: 107 var_x_pop: 1.6936e-12 var_y_pop:2.7597e+10
index: 107 corr: -0.04741 in:0.0008156 263700
index: 108 var_x_pop: 1.8745e-12 var_y_pop:1.08665e+10
index: 108 corr: -0.231669 in:0.0008156 0
index: 109 var_x_pop: 1.8409e-12 var_y_pop:1.07531e+10
index: 109 corr: -0.0786989 in:0.0008159 6200
index: 110 var_x_pop: 1.8825e-12 var_y_pop:8.73522e+09
index: 110 corr: -0.112333 in:0.0008159 0
index: 111 var_x_pop: 1.82e-12 var_y_pop:8.23736e+09
index: 111 corr: -0.0279725 in:0.0008155 62800
index: 112 var_x_pop: 1.0541e-12 var_y_pop:7.66259e+09
index: 112 corr: -0.303113 in:0.0008155 0
index: 113 var_x_pop: 4.976e-13 var_y_pop:7.77789e+09
index: 113 corr: -0.509783 in:0.0008163 19800
index: 114 var_x_pop: 3.664e-13 var_y_pop:7.84927e+09
index: 114 corr: -0.52725 in:0.0008163 0
index: 115 var_x_pop: 2.224e-13 var_y_pop:7.84927e+09
index: 115 corr: -0.574386 in:0.0008163 0
index: 116 var_x_pop: 1.136e-13 var_y_pop:6.14864e+09
index: 116 corr: -0.391087 in:0.0008163 0
index: 117 var_x_pop: 1.129e-13 var_y_pop:3.58578e+08
index: 117 corr: -0.395935 in:0.0008163 0
index: 118 var_x_pop: 1.2196e-12 var_y_pop:3.70428e+08
index: 118 corr: -0.36304 in:0.0008125 25000
index: 119 var_x_pop: 1.3796e-12 var_y_pop:3.69397e+08
index: 119 corr: -0.311046 in:0.0008143 7300
index: 120 var_x_pop: 1.4884e-12 var_y_pop:3.69397e+08
index: 120 corr: -0.221059 in:0.0008143 0
index: 121 var_x_pop: 1.5844e-12 var_y_pop:7.98889e+07
index: 121 corr: -0.483299 in:0.0008143 0
index: 122 var_x_pop: 1.5856e-12 var_y_pop:8.0234e+07
index: 122 corr: -0.483902 in:0.0008149 11900
index: 123 var_x_pop: 1.6369e-12 var_y_pop:3.46456e+08
index: 123 corr: -0.609406 in:0.0008136 61300
index: 124 var_x_pop: 1.5424e-12 var_y_pop:3.46456e+08
index: 124 corr: -0.504574 in:0.0008136 0
index: 125 var_x_pop: 1.3344e-12 var_y_pop:6.15099e+08
index: 125 corr: -0.155871 in:0.0008155 67600
index: 126 var_x_pop: 1.0605e-12 var_y_pop:7.4107e+08
index: 126 corr: 0.0726261 in:0.0008152 61300
index: 127 var_x_pop: 7.624e-13 var_y_pop:7.4107e+08
index: 127 corr: 0.194131 in:0.0008152 0
index: 128 var_x_pop: 4.249e-13 var_y_pop:7.8952e+08
index: 128 corr: 0.194401 in:0.0008152 0
index: 129 var_x_pop: 4.42e-13 var_y_pop:8.14231e+08
index: 129 corr: 0.103739 in:0.0008152 0
index: 130 var_x_pop: 4.429e-13 var_y_pop:8.14231e+08
index: 130 corr: 0.00785147 in:0.0008152 0
index: 131 var_x_pop: 4.276e-13 var_y_pop:8.14231e+08
index: 131 corr: -0.0894894 in:0.0008152 0
index: 132 var_x_pop: 4.369e-13 var_y_pop:8.46754e+08
index: 132 corr: -0.117719 in:0.0008152 0
index: 133 var_x_pop: 7.84e-13 var_y_pop:6.60405e+08
index: 133 corr: 0.283859 in:0.0008175 26000
index: 134 var_x_pop: 8.269e-13 var_y_pop:6.60405e+08
index: 134 corr: 0.017883 in:0.0008175 0
index: 135 var_x_pop: 8.541e-13 var_y_pop:3.59344e+08
index: 135 corr: 0.094817 in:0.0008163 12400
index: 136 var_x_pop: 8.376e-13 var_y_pop:6.82304e+07
index: 136 corr: 0.626053 in:0.0008163 0
index: 137 var_x_pop: 7.969e-13 var_y_pop:6.82304e+07
index: 137 corr: 0.584557 in:0.0008163 0
index: 138 var_x_pop: 7.32e-13 var_y_pop:6.82304e+07
index: 138 corr: 0.550151 in:0.0008163 0
index: 139 var_x_pop: 6.429e-13 var_y_pop:6.82304e+07
index: 139 corr: 0.52326 in:0.0008163 0
index: 140 var_x_pop: 5.301e-13 var_y_pop:3.04677e+08
index: 140 corr: 0.267118 in:0.0008164 55700
index: 141 var_x_pop: 3.885e-13 var_y_pop:3.04677e+08
index: 141 corr: 0.208233 in:0.0008164 0
index: 142 var_x_pop: 6.461e-13 var_y_pop:5.25676e+08
index: 142 corr: -0.489351 in:0.0008144 61100
index: 143 var_x_pop: 8.104e-13 var_y_pop:5.3202e+08
index: 143 corr: -0.38293 in:0.0008144 0
index: 144 var_x_pop: 7.825e-13 var_y_pop:5.3202e+08
index: 144 corr: -0.193398 in:0.0008144 0
index: 145 var_x_pop: 7.625e-13 var_y_pop:2.32184e+09
index: 145 corr: -0.210333 in:0.0008153 154000
index: 146 var_x_pop: 7.7e-13 var_y_pop:1.40738e+11
index: 146 corr: -0.284726 in:0.0008148 1.2706e+06
index: 147 var_x_pop: 7.141e-13 var_y_pop:1.38697e+11
index: 147 corr: -0.263878 in:0.000815 252800
index: 148 var_x_pop: 6.244e-13 var_y_pop:1.38697e+11
index: 148 corr: -0.202937 in:0.000815 0
index: 149 var_x_pop: 5.009e-13 var_y_pop:1.38064e+11
index: 149 corr: -0.139175 in:0.000815 18500
index: 150 var_x_pop: 3.176e-13 var_y_pop:2.33128e+11
index: 150 corr: -0.0226206 in:0.0008151 1.2324e+06
index: 151 var_x_pop: 2.069e-13 var_y_pop:2.29806e+11
index: 151 corr: 0.313582 in:0.0008137 61200
index: 152 var_x_pop: 2.021e-13 var_y_pop:2.37338e+11
index: 152 corr: 0.315474 in:0.000815 728600
index: 153 var_x_pop: 1.901e-13 var_y_pop:2.37338e+11
index: 153 corr: 0.220252 in:0.000815 0
index: 154 var_x_pop: 1.789e-13 var_y_pop:2.3688e+11
index: 154 corr: 0.0836433 in:0.0008152 6200
index: 155 var_x_pop: 1.72e-13 var_y_pop:2.45742e+11
index: 155 corr: 0.0719049 in:0.0008152 0
index: 156 var_x_pop: 1.0296e-12 var_y_pop:1.54572e+11
index: 156 corr: -0.0293604 in:0.000818 123700
index: 157 var_x_pop: 2.0376e-12 var_y_pop:1.98348e+11
index: 157 corr: 0.313332 in:0.0008186 938700
index: 158 var_x_pop: 2.7864e-12 var_y_pop:1.98348e+11
index: 158 corr: 0.117376 in:0.0008186 0
index: 159 var_x_pop: 3.1461e-12 var_y_pop:1.91833e+11
index: 159 corr: 0.120611 in:0.0008183 525600
index: 160 var_x_pop: 3.1461e-12 var_y_pop:1.89997e+11
index: 160 corr: 0.412416 in:0.0008177 1.2218e+06
index: 161 var_x_pop: 2.3764e-12 var_y_pop:5.84899e+11
index: 161 corr: 0.377703 in:0.000818 2.5148e+06
index: 162 var_x_pop: 2.3016e-12 var_y_pop:5.83273e+11
index: 162 corr: 0.410824 in:0.0008152 569900
index: 163 var_x_pop: 2.226e-12 var_y_pop:5.83273e+11
index: 163 corr: 0.407385 in:0.0008152 0
index: 164 var_x_pop: 2.226e-12 var_y_pop:5.84e+11
index: 164 corr: 0.40811 in:0.0008152 0
index: 165 var_x_pop: 4.3941e-12 var_y_pop:6.44933e+11
index: 165 corr: -0.0916086 in:0.0008119 1.7066e+06
index: 166 var_x_pop: 6.4676e-12 var_y_pop:6.5562e+11
index: 166 corr: 0.143447 in:0.0008115 44200
index: 167 var_x_pop: 7.5776e-12 var_y_pop:6.95607e+11
index: 167 corr: 0.229415 in:0.0008112 33400
index: 168 var_x_pop: 8.5236e-12 var_y_pop:6.51652e+11
index: 168 corr: 0.284257 in:0.00081 963300
index: 169 var_x_pop: 8.2829e-12 var_y_pop:7.00941e+11
index: 169 corr: 0.436384 in:0.00081 0
index: 170 var_x_pop: 7.2896e-12 var_y_pop:7.09105e+11
index: 170 corr: 0.439134 in:0.00081 0
index: 171 var_x_pop: 4.5229e-12 var_y_pop:2.94852e+11
index: 171 corr: -0.0911579 in:0.0008107 310600
index: 172 var_x_pop: 3.9016e-12 var_y_pop:3.00471e+11
index: 172 corr: -0.099948 in:0.0008095 100
index: 173 var_x_pop: 3.8449e-12 var_y_pop:2.9706e+11
index: 173 corr: 0.10982 in:0.0008071 61300
index: 174 var_x_pop: 2.476e-12 var_y_pop:2.93543e+11
index: 174 corr: 0.413726 in:0.0008071 61900
index: 175 var_x_pop: 2.8841e-12 var_y_pop:1.17358e+11
index: 175 corr: -0.233529 in:0.0008066 813800
index: 176 var_x_pop: 2.7521e-12 var_y_pop:1.21113e+11
index: 176 corr: -0.251402 in:0.0008071 538900
index: 177 var_x_pop: 2.4036e-12 var_y_pop:1.2285e+11
index: 177 corr: -0.0735718 in:0.0008071 0
index: 178 var_x_pop: 2.8436e-12 var_y_pop:7.26829e+10
index: 178 corr: -0.246461 in:0.0008056 32700
index: 179 var_x_pop: 2.6549e-12 var_y_pop:7.18295e+10
index: 179 corr: -0.110506 in:0.0008063 25000
index: 180 var_x_pop: 2.1381e-12 var_y_pop:7.10715e+10
index: 180 corr: 0.0327794 in:0.0008066 21700
index: 181 var_x_pop: 9.501e-13 var_y_pop:7.60611e+11
index: 181 corr: -0.244325 in:0.0008063 2.9442e+06
index: 182 var_x_pop: 3.144e-13 var_y_pop:7.60333e+11
index: 182 corr: -0.0665638 in:0.0008056 3200
index: 183 var_x_pop: 5.64e-13 var_y_pop:7.5662e+11
index: 183 corr: 0.0806676 in:0.0008047 112000
index: 184 var_x_pop: 6.984e-13 var_y_pop:7.61836e+11
index: 184 corr: 0.213235 in:0.0008047 0
index: 185 var_x_pop: 8.181e-13 var_y_pop:7.6209e+11
index: 185 corr: 0.229827 in:0.0008047 0
index: 186 var_x_pop: 7.461e-13 var_y_pop:7.69783e+11
index: 186 corr: 0.251393 in:0.0008047 0
index: 187 var_x_pop: 5.349e-13 var_y_pop:7.69372e+11
index: 187 corr: 0.4041 in:0.0008049 6600
index: 188 var_x_pop: 6.376e-13 var_y_pop:7.71274e+11
index: 188 corr: 0.426404 in:0.0008043 600
index: 189 var_x_pop: 6.376e-13 var_y_pop:8.34912e+11
index: 189 corr: 0.579995 in:0.0008063 1.2422e+06
index: 190 var_x_pop: 5.665e-13 var_y_pop:8.3674e+11
index: 190 corr: 0.629259 in:0.0008063 0
index: 191 var_x_pop: 5.665e-13 var_y_pop:1.36945e+11
index: 191 corr: 0.445535 in:0.0008063 0
index: 192 var_x_pop: 6.596e-13 var_y_pop:1.37031e+11
index: 192 corr: 0.380696 in:0.0008063 0
index: 193 var_x_pop: 7.089e-13 var_y_pop:5.62753e+11
index: 193 corr: 0.49195 in:0.0008064 2.3139e+06
index: 194 var_x_pop: 1.3564e-12 var_y_pop:6.18988e+11
index: 194 corr: 0.550042 in:0.0008084 1.28e+06
index: 195 var_x_pop: 1.7301e-12 var_y_pop:6.18988e+11
index: 195 corr: 0.313861 in:0.0008084 0
index: 196 var_x_pop: 1.83e-12 var_y_pop:6.18988e+11
index: 196 corr: 0.136799 in:0.0008084 0
index: 197 var_x_pop: 1.7425e-12 var_y_pop:6.19623e+11
index: 197 corr: -0.0217177 in:0.0008084 0
index: 198 var_x_pop: 1.0824e-12 var_y_pop:6.19681e+11
index: 198 corr: -0.269464 in:0.0008084 0
index: 199 var_x_pop: 1.0341e-12 var_y_pop:5.70092e+11
index: 199 corr: -0.214228 in:0.0008084 0
index: 200 var_x_pop: 8.976e-13 var_y_pop:5.70092e+11
index: 200 corr: -0.335445 in:0.0008084 0
index: 201 var_x_pop: 7.201e-13 var_y_pop:5.67245e+11
index: 201 corr: -0.510996 in:0.0008088 41800
index: 202 var_x_pop: 4.176e-13 var_y_pop:5.67245e+11
index: 202 corr: -0.857768 in:0.0008088 0
index: 203 var_x_pop: 2.89e-14 var_y_pop:1.46383e+11
index: 203 corr: -0.196027 in:0.0008087 6200
index: 204 var_x_pop: 3.04e-14 var_y_pop:1.55528e+08
index: 204 corr: 0.545436 in:0.0008087 0
index: 205 var_x_pop: 3.01e-14 var_y_pop:1.55528e+08
index: 205 corr: 0.481593 in:0.0008087 0
index: 206 var_x_pop: 3.09e-14 var_y_pop:1.55433e+08
index: 206 corr: 0.388721 in:0.0008088 100
index: 207 var_x_pop: 3.24e-14 var_y_pop:1.52928e+08
index: 207 corr: 0.341517 in:0.0008089 6200
index: 208 var_x_pop: 2.6e-14 var_y_pop:1.49654e+08
index: 208 corr: 0.306707 in:0.0008088 6200
index: 209 var_x_pop: 1.64e-14 var_y_pop:1.49654e+08
index: 209 corr: 0.231707 in:0.0008088 0
index: 210 var_x_pop: 3.6e-15 var_y_pop:1.49654e+08
index: 211 var_x_pop: 3.6e-15 var_y_pop:8.0361e+06
index: 212 var_x_pop: 3.6e-15 var_y_pop:8.0361e+06
index: 213 var_x_pop: 4e-15 var_y_pop:8.0361e+06
index: 214 var_x_pop: 1.096e-13 var_y_pop:5.78684e+07
index: 214 corr: 0.96021 in:0.0008099 25700
index: 215 var_x_pop: 1.864e-13 var_y_pop:5.78684e+07
index: 215 corr: 0.574063 in:0.0008099 0
index: 216 var_x_pop: 1.849e-13 var_y_pop:3.55181e+08
index: 216 corr: 0.367836 in:0.0008093 62600
index: 217 var_x_pop: 1.841e-13 var_y_pop:3.64208e+08
index: 217 corr: 0.329232 in:0.0008093 0
index: 218 var_x_pop: 1.769e-13 var_y_pop:1.8001e+10
index: 218 corr: 0.210006 in:0.0008094 453200
index: 219 var_x_pop: 1.856e-13 var_y_pop:2.22318e+11
index: 219 corr: -0.309672 in:0.0008087 1.5688e+06
index: 220 var_x_pop: 2.044e-13 var_y_pop:2.17707e+11
index: 220 corr: -0.357478 in:0.0008086 298900
index: 221 var_x_pop: 2.224e-13 var_y_pop:2.17707e+11
index: 221 corr: -0.320752 in:0.0008086 0
index: 222 var_x_pop: 2.396e-13 var_y_pop:2.17707e+11
index: 222 corr: -0.287874 in:0.0008086 0
index: 223 var_x_pop: 7.72e-13 var_y_pop:2.11587e+11
index: 223 corr: -0.166248 in:0.0008067 248100
index: 224 var_x_pop: 7.036e-13 var_y_pop:2.09844e+11
index: 224 corr: -0.0687474 in:0.0008081 64900
index: 225 var_x_pop: 6.809e-13 var_y_pop:2.0578e+11
index: 225 corr: -0.0376253 in:0.0008098 88400
index: 226 var_x_pop: 2.0504e-12 var_y_pop:2.01042e+11
index: 226 corr: 0.0389333 in:0.0008126 372500
index: 227 var_x_pop: 2.6936e-12 var_y_pop:1.95718e+11
index: 227 corr: -0.034273 in:0.0008117 100800
index: 228 var_x_pop: 3.3644e-12 var_y_pop:1.9847e+11
index: 228 corr: -0.101697 in:0.000812 75300
index: 229 var_x_pop: 3.4796e-12 var_y_pop:1.47748e+10
index: 229 corr: 0.126735 in:0.0008111 76000
index: 230 var_x_pop: 3.5816e-12 var_y_pop:1.21491e+10
index: 230 corr: 0.293942 in:0.0008116 184800
index: 231 var_x_pop: 3.4385e-12 var_y_pop:1.05217e+10
index: 231 corr: 0.215567 in:0.0008113 130300
index: 232 var_x_pop: 3.1001e-12 var_y_pop:8.53924e+09
index: 232 corr: 0.0906728 in:0.0008104 162300
index: 233 var_x_pop: 1.5089e-12 var_y_pop:9.18828e+09
index: 233 corr: 0.547702 in:0.0008103 1600
index: 234 var_x_pop: 7.529e-13 var_y_pop:8.83328e+09
index: 234 corr: 0.529537 in:0.0008101 157300
index: 235 var_x_pop: 5.969e-13 var_y_pop:8.85225e+09
index: 235 corr: 0.540012 in:0.0008118 193800
index: 236 var_x_pop: 4.176e-13 var_y_pop:4.29584e+09
index: 236 corr: 0.0604132 in:0.0008115 6200
index: 237 var_x_pop: 7.049e-13 var_y_pop:1.16177e+10
index: 237 corr: 0.566333 in:0.000813 395100
index: 238 var_x_pop: 9.02e-13 var_y_pop:1.11934e+10
index: 238 corr: 0.578149 in:0.0008129 158700
index: 239 var_x_pop: 9.116e-13 var_y_pop:1.14171e+10
index: 239 corr: 0.508095 in:0.0008119 61500
index: 240 var_x_pop: 1.0949e-12 var_y_pop:1.14468e+10
index: 240 corr: 0.513195 in:0.0008129 188400
index: 241 var_x_pop: 1.2261e-12 var_y_pop:1.1648e+10
index: 241 corr: 0.42889 in:0.0008129 97000
index: 242 var_x_pop: 1.1036e-12 var_y_pop:1.33659e+10
index: 242 corr: 0.345814 in:0.0008129 0
index: 243 var_x_pop: 1.0316e-12 var_y_pop:8.04313e+10
index: 243 corr: 0.524613 in:0.0008139 1.014e+06
index: 244 var_x_pop: 5.984e-13 var_y_pop:8.0423e+10
index: 244 corr: 0.568076 in:0.0008139 157900
index: 245 var_x_pop: 5.585e-13 var_y_pop:3.01186e+11
index: 245 corr: 0.581632 in:0.0008137 1.7976e+06
index: 246 var_x_pop: 3.249e-13 var_y_pop:2.91582e+11
index: 246 corr: 0.576252 in:0.0008131 160000
index: 247 var_x_pop: 3.705e-13 var_y_pop:2.96763e+11
index: 247 corr: 0.581669 in:0.0008124 163800
index: 248 var_x_pop: 3.876e-13 var_y_pop:3.05603e+11
index: 248 corr: 0.598299 in:0.0008126 6200
index: 249 var_x_pop: 2.66e-13 var_y_pop:3.00183e+11
index: 249 corr: 0.627868 in:0.0008127 167600
index: 250 var_x_pop: 2.821e-13 var_y_pop:3.02939e+11
index: 250 corr: 0.552768 in:0.0008136 124500
index: 251 var_x_pop: 2.776e-13 var_y_pop:2.99338e+11
index: 251 corr: 0.518144 in:0.0008134 172700
index: 252 var_x_pop: 3.176e-13 var_y_pop:4.45136e+11
index: 252 corr: 0.11935 in:0.0008125 1.758e+06
index: 253 var_x_pop: 3.689e-13 var_y_pop:4.31104e+11
index: 253 corr: 0.101227 in:0.000812 173300
index: 254 var_x_pop: 3.196e-13 var_y_pop:4.42589e+11
index: 254 corr: 0.272582 in:0.0008122 6200
index: 255 var_x_pop: 3.469e-13 var_y_pop:2.41705e+11
index: 255 corr: -0.0980798 in:0.0008116 298100
index: 256 var_x_pop: 7.781e-13 var_y_pop:2.4218e+11
index: 256 corr: 0.0342279 in:0.0008103 144200
index: 257 var_x_pop: 1.1456e-12 var_y_pop:2.49104e+11
index: 257 corr: 0.137735 in:0.0008103 0
index: 258 var_x_pop: 1.1489e-12 var_y_pop:2.71721e+11
index: 258 corr: 0.104515 in:0.0008115 905400
index: 259 var_x_pop: 1.1129e-12 var_y_pop:2.69158e+11
index: 259 corr: 0.146933 in:0.0008115 241100
index: 260 var_x_pop: 8.609e-13 var_y_pop:2.61923e+11
index: 260 corr: 0.279533 in:0.0008108 369900
index: 261 var_x_pop: 5.201e-13 var_y_pop:2.94287e+11
index: 261 corr: 0.39652 in:0.000811 1.0866e+06
index: 262 var_x_pop: 5.66e-13 var_y_pop:1.40672e+11
index: 262 corr: -0.242169 in:0.0008098 860600
index: 263 var_x_pop: 5.849e-13 var_y_pop:1.49174e+11
index: 263 corr: -0.016469 in:0.0008099 31200
index: 264 var_x_pop: 4.584e-13 var_y_pop:1.35694e+11
index: 264 corr: 0.264989 in:0.0008099 247200
index: 265 var_x_pop: 4.261e-13 var_y_pop:1.44501e+11
index: 265 corr: 0.422558 in:0.0008097 91600
index: 266 var_x_pop: 4.821e-13 var_y_pop:4.01186e+11
index: 266 corr: 0.511724 in:0.0008113 2.1381e+06
index: 267 var_x_pop: 4.776e-13 var_y_pop:3.82708e+11
index: 267 corr: 0.464083 in:0.0008108 178800
index: 268 var_x_pop: 3.944e-13 var_y_pop:3.91981e+11
index: 268 corr: 0.413892 in:0.0008107 127700
index: 269 var_x_pop: 2.984e-13 var_y_pop:4.11494e+11
index: 269 corr: 0.515521 in:0.0008107 0
index: 270 var_x_pop: 3.204e-13 var_y_pop:4.19574e+11
index: 270 corr: 0.559277 in:0.0008098 189800
index: 271 var_x_pop: 2.885e-13 var_y_pop:3.86152e+11
index: 271 corr: 0.462565 in:0.0008099 675400
index: 272 var_x_pop: 2.804e-13 var_y_pop:3.82832e+11
index: 272 corr: 0.578198 in:0.0008099 0
index: 273 var_x_pop: 2.696e-13 var_y_pop:3.78951e+11
index: 273 corr: 0.568062 in:0.0008101 94100
index: 274 var_x_pop: 5.069e-13 var_y_pop:3.83138e+11
index: 274 corr: 0.311508 in:0.000812 130600
index: 275 var_x_pop: 4.621e-13 var_y_pop:3.8652e+11
index: 275 corr: 0.228713 in:0.0008111 34600
index: 276 var_x_pop: 4.621e-13 var_y_pop:3.89031e+10
index: 276 corr: -0.15147 in:0.0008113 392300
index: 277 var_x_pop: 4.664e-13 var_y_pop:3.89369e+10
index: 277 corr: -0.146394 in:0.0008109 202500
index: 278 var_x_pop: 5.885e-13 var_y_pop:4.92737e+10
index: 278 corr: 0.0989654 in:0.0008118 535800
index: 279 var_x_pop: 7.509e-13 var_y_pop:3.491e+11
index: 279 corr: 0.465867 in:0.0008121 2.0929e+06
index: 280 var_x_pop: 6.189e-13 var_y_pop:4.00692e+11
index: 280 corr: 0.417775 in:0.000811 1.2666e+06
index: 281 var_x_pop: 4.941e-13 var_y_pop:4.12209e+11
index: 281 corr: 0.469016 in:0.0008115 140700
index: 282 var_x_pop: 3.364e-13 var_y_pop:4.10317e+11
index: 282 corr: 0.321976 in:0.0008118 19700
index: 283 var_x_pop: 1.6e-13 var_y_pop:3.96649e+11
index: 283 corr: 0.254283 in:0.0008115 328700
index: 284 var_x_pop: 1.325e-13 var_y_pop:4.0821e+11
index: 284 corr: 0.355157 in:0.0008115 0
index: 285 var_x_pop: 1.189e-13 var_y_pop:4.11548e+11
index: 285 corr: 0.288833 in:0.0008115 0
index: 286 var_x_pop: 3.144e-13 var_y_pop:5.7649e+11
index: 286 corr: 0.572537 in:0.000813 1.8685e+06
index: 287 var_x_pop: 5.944e-13 var_y_pop:5.93584e+11
index: 287 corr: 0.152563 in:0.0008137 37300
index: 288 var_x_pop: 6.581e-13 var_y_pop:7.4309e+11
index: 288 corr: -0.0148599 in:0.0008111 1.9324e+06
index: 289 var_x_pop: 7.021e-13 var_y_pop:5.83019e+11
index: 289 corr: -0.00404708 in:0.0008111 0
index: 290 var_x_pop: 6.876e-13 var_y_pop:5.48253e+11
index: 290 corr: 0.147579 in:0.0008111 0
index: 291 var_x_pop: 7.216e-13 var_y_pop:5.44174e+11
index: 291 corr: 0.105873 in:0.0008125 220300
index: 292 var_x_pop: 7.956e-13 var_y_pop:5.45327e+11
index: 292 corr: 0.0351274 in:0.0008128 6200
index: 293 var_x_pop: 7.869e-13 var_y_pop:5.56284e+11
index: 293 corr: 0.047859 in:0.0008116 81700
index: 294 var_x_pop: 7.976e-13 var_y_pop:5.54653e+11
index: 294 corr: 0.052088 in:0.0008114 20100
index: 295 var_x_pop: 8.081e-13 var_y_pop:5.54653e+11
index: 295 corr: 0.0579719 in:0.0008114 0
index: 296 var_x_pop: 7.089e-13 var_y_pop:8.18726e+11
index: 296 corr: -0.293785 in:0.0008114 2.6083e+06
index: 297 var_x_pop: 8.304e-13 var_y_pop:8.18293e+11
index: 297 corr: -0.025299 in:0.0008092 42100
index: 298 var_x_pop: 1.2541e-12 var_y_pop:5.97339e+11
index: 298 corr: 0.0993429 in:0.0008092 0
index: 299 var_x_pop: 1.6056e-12 var_y_pop:5.87554e+11
index: 299 corr: 0.0913942 in:0.0008092 302300
index: 300 var_x_pop: 1.8849e-12 var_y_pop:5.87554e+11
index: 300 corr: 0.143588 in:0.0008092 0
index: 301 var_x_pop: 1.7364e-12 var_y_pop:5.96672e+11
index: 301 corr: 0.210675 in:0.0008092 0
index: 302 var_x_pop: 1.1604e-12 var_y_pop:5.95531e+11
index: 302 corr: 0.372117 in:0.0008096 25800
index: 303 var_x_pop: 9.305e-13 var_y_pop:5.97122e+11
index: 303 corr: 0.473795 in:0.0008097 48700
index: 304 var_x_pop: 6.976e-13 var_y_pop:5.98303e+11
index: 304 corr: 0.621806 in:0.0008097 0
index: 305 var_x_pop: 7.309e-13 var_y_pop:5.97671e+11
index: 305 corr: 0.605959 in:0.0008115 10600
index: 306 var_x_pop: 6.701e-13 var_y_pop:7.72236e+09
index: 306 corr: -0.242135 in:0.0008112 8100
index: 307 var_x_pop: 8.021e-13 var_y_pop:7.89586e+09
index: 307 corr: -0.288112 in:0.0008112 0
index: 308 var_x_pop: 8.541e-13 var_y_pop:7.89586e+09
index: 308 corr: -0.375525 in:0.0008112 0
index: 309 var_x_pop: 1.0821e-12 var_y_pop:2.40096e+08
index: 309 corr: -0.0174023 in:0.0008122 23300
index: 310 var_x_pop: 1.1301e-12 var_y_pop:2.40096e+08
index: 310 corr: -0.229204 in:0.0008122 0
index: 311 var_x_pop: 1.2324e-12 var_y_pop:2.62675e+08
index: 311 corr: -0.531985 in:0.0008089 33400
index: 312 var_x_pop: 1.4361e-12 var_y_pop:2.66803e+08
index: 312 corr: -0.294349 in:0.0008089 0
index: 313 var_x_pop: 1.6489e-12 var_y_pop:1.2679e+08
index: 313 corr: -0.0300573 in:0.0008089 0
index: 314 var_x_pop: 1.7401e-12 var_y_pop:1.55836e+08
index: 314 corr: 0.0812035 in:0.0008121 28200
index: 315 var_x_pop: 1.8529e-12 var_y_pop:1.6544e+08
index: 315 corr: 0.00394097 in:0.0008121 0
index: 316 var_x_pop: 1.9816e-12 var_y_pop:1.73289e+08
index: 316 corr: -0.051061 in:0.0008121 0
index: 317 var_x_pop: 2.0421e-12 var_y_pop:2.02612e+08
index: 317 corr: -0.113412 in:0.0008101 29800
index: 318 var_x_pop: 2.0784e-12 var_y_pop:2.02612e+08
index: 318 corr: -0.0509333 in:0.0008101 0
index: 319 var_x_pop: 1.8705e-12 var_y_pop:1.96344e+08
index: 319 corr: -0.129461 in:0.0008101 0
index: 320 var_x_pop: 1.6209e-12 var_y_pop:1.87848e+08
index: 320 corr: -0.0344653 in:0.0008096 7200
index: 321 var_x_pop: 1.4704e-12 var_y_pop:1.31002e+08
index: 321 corr: 0.258291 in:0.0008096 0
index: 322 var_x_pop: 1.7616e-12 var_y_pop:4.92447e+12
index: 322 corr: -0.546632 in:0.0008081 7.4042e+06
index: 323 var_x_pop: 1.5669e-12 var_y_pop:4.91362e+12
index: 323 corr: -0.61092 in:0.00081 76100
index: 324 var_x_pop: 1.2429e-12 var_y_pop:4.91679e+12
index: 324 corr: -0.63291 in:0.0008103 6700
index: 325 var_x_pop: 8.541e-13 var_y_pop:4.91679e+12
index: 325 corr: -0.697406 in:0.0008103 0
index: 326 var_x_pop: 1.6101e-12 var_y_pop:4.85407e+12
index: 326 corr: -0.444035 in:0.0008135 880300
index: 327 var_x_pop: 6.304e-12 var_y_pop:4.80757e+12
index: 327 corr: -0.162545 in:0.0008174 1.4733e+06
index: 328 var_x_pop: 8.7716e-12 var_y_pop:4.7682e+12
index: 328 corr: -0.21633 in:0.0008163 222500
index: 329 var_x_pop: 1.09089e-11 var_y_pop:4.72255e+12
index: 329 corr: -0.161628 in:0.0008168 1.9819e+06
index: 330 var_x_pop: 1.08305e-11 var_y_pop:4.65176e+12
index: 330 corr: -0.246277 in:0.0008152 345700
index: 331 var_x_pop: 1.03364e-11 var_y_pop:4.63712e+12
index: 331 corr: -0.359624 in:0.0008157 60400
index: 332 var_x_pop: 7.6804e-12 var_y_pop:4.3473e+11
index: 332 corr: 0.567762 in:0.0008161 187400
index: 333 var_x_pop: 5.9045e-12 var_y_pop:4.12697e+11
index: 333 corr: 0.535627 in:0.0008159 620400
index: 334 var_x_pop: 3.7556e-12 var_y_pop:4.13455e+11
index: 334 corr: 0.409366 in:0.000816 100
index: 335 var_x_pop: 9.569e-13 var_y_pop:4.13455e+11
index: 335 corr: 0.287932 in:0.000816 0
index: 336 var_x_pop: 3.721e-13 var_y_pop:4.21433e+11
index: 336 corr: 0.584546 in:0.0008169 94000
index: 337 var_x_pop: 3.289e-13 var_y_pop:4.07246e+11
index: 337 corr: 0.558826 in:0.0008172 1.3979e+06
index: 338 var_x_pop: 3.316e-13 var_y_pop:9.69697e+11
index: 338 corr: 0.445324 in:0.0008164 3.0385e+06
index: 339 var_x_pop: 2.996e-13 var_y_pop:8.4388e+11
index: 339 corr: 0.319302 in:0.0008164 0
index: 340 var_x_pop: 1.94e-13 var_y_pop:8.70451e+11
index: 340 corr: 0.315488 in:0.0008164 0
index: 341 var_x_pop: 1.541e-13 var_y_pop:8.76571e+11
index: 341 corr: 0.260932 in:0.0008164 0
index: 342 var_x_pop: 5.816e-13 var_y_pop:8.76544e+11
index: 342 corr: 0.226391 in:0.0008142 187800
index: 343 var_x_pop: 1.3356e-12 var_y_pop:8.9673e+11
index: 343 corr: 0.265856 in:0.0008133 41000
index: 344 var_x_pop: 1.8156e-12 var_y_pop:8.95239e+11
index: 344 corr: 0.315191 in:0.0008136 16000
index: 345 var_x_pop: 1.9221e-12 var_y_pop:8.69949e+11
index: 345 corr: 0.322091 in:0.0008145 553100
index: 346 var_x_pop: 2.2661e-12 var_y_pop:9.30019e+11
index: 346 corr: 0.186766 in:0.0008129 1.533e+06
index: 347 var_x_pop: 2.15e-12 var_y_pop:9.04265e+11
index: 347 corr: 0.152512 in:0.0008129 0
index: 348 var_x_pop: 1.8376e-12 var_y_pop:2.10623e+11
index: 348 corr: -0.411574 in:0.0008142 122800
index: 349 var_x_pop: 1.4284e-12 var_y_pop:2.10623e+11
index: 349 corr: -0.368402 in:0.0008142 0
index: 350 var_x_pop: 9.224e-13 var_y_pop:2.10623e+11
index: 350 corr: -0.335975 in:0.0008142 0
index: 351 var_x_pop: 3.196e-13 var_y_pop:2.10623e+11
index: 351 corr: -0.362713 in:0.0008142 0
index: 352 var_x_pop: 3.281e-13 var_y_pop:2.95992e+11
index: 352 corr: -0.146974 in:0.0008143 1.2278e+06
index: 353 var_x_pop: 3.121e-13 var_y_pop:2.98672e+11
index: 353 corr: -0.255987 in:0.0008143 0
index: 354 var_x_pop: 3.1e-13 var_y_pop:2.99749e+11
index: 354 corr: -0.333578 in:0.0008143 0
index: 355 var_x_pop: 6.625e-13 var_y_pop:3.03732e+11
index: 355 corr: -0.381994 in:0.000816 6700
index: 356 var_x_pop: 7.524e-13 var_y_pop:1.33839e+11
index: 356 corr: -0.0687157 in:0.000816 0
index: 357 var_x_pop: 8.301e-13 var_y_pop:1.33804e+11
index: 357 corr: -0.215427 in:0.0008166 1300
index: 358 var_x_pop: 1.0461e-12 var_y_pop:1.35482e+11
index: 358 corr: -0.248942 in:0.0008166 0
index: 359 var_x_pop: 1.3341e-12 var_y_pop:1.34421e+11
index: 359 corr: -0.21318 in:0.0008172 221400
index: 360 var_x_pop: 1.4421e-12 var_y_pop:1.34421e+11
index: 360 corr: -0.304332 in:0.0008172 0
index: 361 var_x_pop: 1.3701e-12 var_y_pop:1.34421e+11
index: 361 corr: -0.414093 in:0.0008172 0
index: 362 var_x_pop: 1.088e-12 var_y_pop:6.02225e+10
index: 362 corr: -0.063788 in:0.0008156 813600
index: 363 var_x_pop: 1.1249e-12 var_y_pop:5.98105e+10
index: 363 corr: -0.0748122 in:0.0008142 21800
index: 364 var_x_pop: 1.2404e-12 var_y_pop:5.96819e+10
index: 364 corr: -0.0642744 in:0.000814 6200
index: 365 var_x_pop: 1.6244e-12 var_y_pop:5.98205e+10
index: 365 corr: 0.0123128 in:0.000814 0
index: 366 var_x_pop: 2.0361e-12 var_y_pop:1.74859e+11
index: 366 corr: -0.358479 in:0.0008137 1.255e+06
index: 367 var_x_pop: 2.9249e-12 var_y_pop:1.73439e+11
index: 367 corr: -0.171652 in:0.0008122 34200
index: 368 var_x_pop: 3.704e-12 var_y_pop:1.72038e+11
index: 368 corr: -0.0206823 in:0.0008117 31700
index: 369 var_x_pop: 3.5821e-12 var_y_pop:1.76111e+11
index: 369 corr: 0.046196 in:0.0008119 26700
index: 370 var_x_pop: 2.8324e-12 var_y_pop:1.74967e+11
index: 370 corr: 0.204581 in:0.0008121 27700
index: 371 var_x_pop: 1.5076e-12 var_y_pop:1.71518e+11
index: 371 corr: 0.43177 in:0.0008124 395800
index: 372 var_x_pop: 8.644e-13 var_y_pop:1.40998e+11
index: 372 corr: 0.189071 in:0.0008132 6200
index: 373 var_x_pop: 7.024e-13 var_y_pop:1.41733e+11
index: 373 corr: 0.25702 in:0.0008132 0
index: 374 var_x_pop: 5.744e-13 var_y_pop:1.4195e+11
index: 374 corr: 0.331276 in:0.0008132 0
index: 375 var_x_pop: 4.336e-13 var_y_pop:1.4195e+11
index: 375 corr: 0.438599 in:0.0008132 0
index: 376 var_x_pop: 3.541e-13 var_y_pop:1.33071e+10
index: 376 corr: -0.241621 in:0.0008132 0
index: 377 var_x_pop: 7.016e-13 var_y_pop:1.34233e+10
index: 377 corr: -0.256928 in:0.0008147 13100
index: 378 var_x_pop: 8.036e-13 var_y_pop:1.36305e+10
index: 378 corr: -0.337076 in:0.0008147 0
index: 379 var_x_pop: 7.924e-13 var_y_pop:1.38028e+10
index: 379 corr: -0.423199 in:0.0008147 0
index: 380 var_x_pop: 5.909e-13 var_y_pop:1.3821e+10
index: 380 corr: -0.522366 in:0.0008134 69400
index: 381 var_x_pop: 4.636e-13 var_y_pop:8.73325e+08
index: 381 corr: 0.120806 in:0.0008143 81200
index: 382 var_x_pop: 4.449e-13 var_y_pop:8.90164e+08
index: 382 corr: 0.0497322 in:0.0008143 0
index: 383 var_x_pop: 4.209e-13 var_y_pop:1.02844e+09
index: 383 corr: -0.118675 in:0.0008134 61400
index: 384 var_x_pop: 4.569e-13 var_y_pop:1.3372e+09
index: 384 corr: 0.0627403 in:0.000815 88700
index: 385 var_x_pop: 4.281e-13 var_y_pop:1.3372e+09
index: 385 corr: -0.171261 in:0.000815 0
index: 386 var_x_pop: 3.345e-13 var_y_pop:1.3372e+09
index: 386 corr: -0.460818 in:0.000815 0
index: 387 var_x_pop: 3.576e-13 var_y_pop:1.40054e+09
index: 387 corr: -0.490435 in:0.000815 0
index: 388 var_x_pop: 1.2996e-12 var_y_pop:1.37887e+09
index: 388 corr: -0.0378109 in:0.0008177 63000
index: 389 var_x_pop: 2.0616e-12 var_y_pop:1.37887e+09
index: 389 corr: -0.234666 in:0.0008177 0
index: 390 var_x_pop: 2.5224e-12 var_y_pop:1.2898e+09
index: 390 corr: -0.198736 in:0.0008182 13800
index: 391 var_x_pop: 4.4196e-12 var_y_pop:6.36981e+09
index: 391 corr: 0.602944 in:0.0008205 269300
index: 392 var_x_pop: 5.548e-12 var_y_pop:6.25906e+09
index: 392 corr: 0.402813 in:0.0008205 12600
index: 393 var_x_pop: 4.5624e-12 var_y_pop:8.44794e+09
index: 393 corr: 0.533227 in:0.000819 206100
index: 394 var_x_pop: 3.9449e-12 var_y_pop:8.3876e+09
index: 394 corr: 0.609263 in:0.0008175 61100
index: 395 var_x_pop: 3.1889e-12 var_y_pop:8.31345e+09
index: 395 corr: 0.572684 in:0.0008178 6200
index: 396 var_x_pop: 2.2616e-12 var_y_pop:1.62125e+10
index: 396 corr: 0.39368 in:0.0008183 374700
index: 397 var_x_pop: 1.1165e-12 var_y_pop:1.62125e+10
index: 397 corr: 0.313354 in:0.0008183 0
index: 398 var_x_pop: 1.0416e-12 var_y_pop:1.60792e+10
index: 398 corr: 0.301069 in:0.0008184 88400
index: 399 var_x_pop: 9.569e-13 var_y_pop:1.60792e+10
index: 399 corr: 0.255861 in:0.0008184 0
index: 400 var_x_pop: 9.676e-13 var_y_pop:1.54107e+10
index: 400 corr: 0.196548 in:0.0008181 162600
index: 401 var_x_pop: 6.009e-13 var_y_pop:1.92871e+12
index: 401 corr: 0.0410355 in:0.0008186 4.7151e+06
index: 402 var_x_pop: 1.62e-13 var_y_pop:1.93011e+12
index: 402 corr: 0.265236 in:0.0008186 0
index: 403 var_x_pop: 6.356e-13 var_y_pop:1.9364e+12
index: 403 corr: 0.251957 in:0.0008158 125800
index: 404 var_x_pop: 1.0589e-12 var_y_pop:1.94275e+12
index: 404 corr: 0.261797 in:0.0008158 0
index: 405 var_x_pop: 1.4229e-12 var_y_pop:1.94343e+12
index: 405 corr: 0.291554 in:0.0008158 0
index: 406 var_x_pop: 1.6404e-12 var_y_pop:1.96895e+12
index: 406 corr: 0.326218 in:0.0008158 0
index: 407 var_x_pop: 1.7329e-12 var_y_pop:1.96895e+12
index: 407 corr: 0.386307 in:0.0008158 0
index: 408 var_x_pop: 1.7689e-12 var_y_pop:1.99017e+12
index: 408 corr: 0.361803 in:0.0008154 1.23e+06
index: 409 var_x_pop: 1.6036e-12 var_y_pop:1.94841e+12
index: 409 corr: 0.439098 in:0.0008155 818200
index: 410 var_x_pop: 1.3265e-12 var_y_pop:1.96561e+12
index: 410 corr: 0.537353 in:0.0008164 20800
index: 411 var_x_pop: 1.0241e-12 var_y_pop:3.4194e+11
index: 411 corr: 0.127372 in:0.0008178 1.6407e+06
index: 412 var_x_pop: 7.089e-13 var_y_pop:3.4194e+11
index: 412 corr: 0.215414 in:0.0008178 0
index: 413 var_x_pop: 7.944e-13 var_y_pop:3.485e+11
index: 413 corr: 0.108528 in:0.0008173 18600
index: 414 var_x_pop: 8.349e-13 var_y_pop:3.485e+11
index: 414 corr: 0.00218628 in:0.0008173 0
index: 415 var_x_pop: 8.304e-13 var_y_pop:3.485e+11
index: 415 corr: -0.101766 in:0.0008173 0
index: 416 var_x_pop: 8.145e-13 var_y_pop:3.43129e+11
index: 416 corr: -0.121914 in:0.0008159 79700
index: 417 var_x_pop: 7.716e-13 var_y_pop:3.42206e+11
index: 417 corr: -0.149047 in:0.0008161 12300
index: 418 var_x_pop: 6.196e-13 var_y_pop:2.67513e+11
index: 418 corr: 0.0773973 in:0.0008174 47400
index: 419 var_x_pop: 4.105e-13 var_y_pop:2.36815e+11
index: 419 corr: 0.364296 in:0.0008172 6200
index: 420 var_x_pop: 3.641e-13 var_y_pop:2.37526e+11
index: 420 corr: 0.34173 in:0.0008172 0
index: 421 var_x_pop: 3.656e-13 var_y_pop:6.40678e+08
index: 421 corr: -0.612991 in:0.0008163 35500
index: 422 var_x_pop: 2.916e-13 var_y_pop:6.39095e+08
index: 422 corr: -0.541176 in:0.0008168 400
index: 423 var_x_pop: 2.721e-13 var_y_pop:6.75476e+08
index: 423 corr: -0.53562 in:0.0008168 0
index: 424 var_x_pop: 2.476e-13 var_y_pop:6.75476e+08
index: 424 corr: -0.491323 in:0.0008168 0
index: 425 var_x_pop: 2.421e-13 var_y_pop:1.02186e+09
index: 425 corr: -0.532231 in:0.0008162 85400
index: 426 var_x_pop: 2.149e-13 var_y_pop:7.1598e+08
index: 426 corr: -0.349204 in:0.0008161 29200
index: 427 var_x_pop: 2.04e-13 var_y_pop:7.19416e+08
index: 427 corr: -0.358249 in:0.0008162 10600
index: 428 var_x_pop: 2.629e-13 var_y_pop:1.33311e+11
index: 428 corr: -0.688972 in:0.0008155 1.2327e+06
index: 429 var_x_pop: 2.2e-13 var_y_pop:1.32868e+11
index: 429 corr: -0.671415 in:0.0008161 23800
index: 430 var_x_pop: 1.489e-13 var_y_pop:1.32126e+11
index: 430 corr: -0.727654 in:0.0008163 28800
index: 431 var_x_pop: 2.081e-13 var_y_pop:1.31461e+11
index: 431 corr: -0.580033 in:0.0008155 71200
index: 432 var_x_pop: 2.12e-13 var_y_pop:1.31473e+11
index: 432 corr: -0.459407 in:0.0008155 0
index: 433 var_x_pop: 1.924e-13 var_y_pop:1.30836e+11
index: 433 corr: -0.360821 in:0.0008154 23100
index: 434 var_x_pop: 4.984e-13 var_y_pop:1.28398e+11
index: 434 corr: -0.192327 in:0.0008138 196500
index: 435 var_x_pop: 7.576e-13 var_y_pop:1.30501e+11
index: 435 corr: -0.0460601 in:0.0008138 0
index: 436 var_x_pop: 9.209e-13 var_y_pop:1.31351e+11
index: 436 corr: 0.0575791 in:0.0008138 0
index: 437 var_x_pop: 9.545e-13 var_y_pop:1.31675e+11
index: 437 corr: 0.160165 in:0.0008138 0
index: 438 var_x_pop: 1.0276e-12 var_y_pop:3.38188e+09
index: 438 corr: -0.087856 in:0.0008138 0
index: 439 var_x_pop: 1.1044e-12 var_y_pop:3.37001e+09
index: 439 corr: -0.065409 in:0.0008129 37800
index: 440 var_x_pop: 8.936e-13 var_y_pop:3.48463e+09
index: 440 corr: 0.0337407 in:0.0008129 0
index: 441 var_x_pop: 7.661e-13 var_y_pop:5.6484e+09
index: 441 corr: -0.273413 in:0.000813 189400
index: 442 var_x_pop: 5.136e-13 var_y_pop:5.6484e+09
index: 442 corr: -0.12654 in:0.000813 0
index: 443 var_x_pop: 2.16e-13 var_y_pop:5.70489e+09
index: 443 corr: -0.141724 in:0.0008142 12400
index: 444 var_x_pop: 6.156e-13 var_y_pop:3.16443e+09
index: 444 corr: -0.337472 in:0.0008156 1500
index: 445 var_x_pop: 9.504e-13 var_y_pop:3.16443e+09
index: 445 corr: -0.350738 in:0.0008156 0
index: 446 var_x_pop: 1.2204e-12 var_y_pop:3.16443e+09
index: 446 corr: -0.379352 in:0.0008156 0
index: 447 var_x_pop: 1.374e-12 var_y_pop:3.10588e+09
index: 447 corr: -0.385758 in:0.0008154 18600
index: 448 var_x_pop: 1.4384e-12 var_y_pop:3.10433e+09
index: 448 corr: -0.431141 in:0.0008152 300
index: 449 var_x_pop: 1.2521e-12 var_y_pop:3.14371e+09
index: 449 corr: -0.453899 in:0.0008152 0
index: 450 var_x_pop: 9.6e-13 var_y_pop:3.14371e+09
index: 450 corr: -0.611402 in:0.0008152 0
index: 451 var_x_pop: 6.036e-13 var_y_pop:3.94476e+07
index: 451 corr: -0.0445937 in:0.0008152 0
index: 452 var_x_pop: 2.305e-13 var_y_pop:4.94524e+07
index: 452 corr: -0.563354 in:0.0008143 14800
index: 453 var_x_pop: 2.124e-13 var_y_pop:4.43436e+07
index: 453 corr: -0.247379 in:0.0008143 0
index: 454 var_x_pop: 2.501e-13 var_y_pop:4.51521e+07
index: 454 corr: -0.115193 in:0.0008143 0
index: 455 var_x_pop: 2.54e-13 var_y_pop:4.51521e+07
index: 455 corr: 0.0150596 in:0.0008143 0
index: 456 var_x_pop: 2.241e-13 var_y_pop:4.51521e+07
index: 456 corr: 0.153758 in:0.0008143 0
index: 457 var_x_pop: 5.4e-13 var_y_pop:9.002e+09
index: 457 corr: -0.823893 in:0.0008127 317600
index: 458 var_x_pop: 7.224e-13 var_y_pop:8.94944e+09
index: 458 corr: -0.632527 in:0.0008128 9400
index: 459 var_x_pop: 7.896e-13 var_y_pop:8.94944e+09
index: 459 corr: -0.507428 in:0.0008128 0
index: 460 var_x_pop: 7.896e-13 var_y_pop:1.14922e+10
index: 460 corr: -0.187282 in:0.0008152 210300
index: 461 var_x_pop: 9.989e-13 var_y_pop:1.14694e+10
index: 461 corr: -0.19923 in:0.0008159 2100
index: 462 var_x_pop: 1.0109e-12 var_y_pop:1.12862e+10
index: 462 corr: -0.190308 in:0.0008145 61200
index: 463 var_x_pop: 1.2949e-12 var_y_pop:2.04687e+12
index: 463 corr: -0.482065 in:0.0008123 4.823e+06
index: 464 var_x_pop: 1.2781e-12 var_y_pop:2.04101e+12
index: 464 corr: -0.472382 in:0.0008139 56700
index: 465 var_x_pop: 1.2781e-12 var_y_pop:2.04033e+12
index: 465 corr: -0.472295 in:0.0008143 6200
index: 466 var_x_pop: 1.2581e-12 var_y_pop:2.03966e+12
index: 466 corr: -0.462386 in:0.0008139 6200
index: 467 var_x_pop: 1.1165e-12 var_y_pop:2.06345e+12
index: 467 corr: -0.505264 in:0.0008139 0
index: 468 var_x_pop: 9.724e-13 var_y_pop:2.06442e+12
index: 468 corr: -0.580624 in:0.0008139 0
index: 469 var_x_pop: 8.041e-13 var_y_pop:2.06442e+12
index: 469 corr: -0.682606 in:0.0008139 0
index: 470 var_x_pop: 1.1044e-12 var_y_pop:2.0774e+12
index: 470 corr: -0.49217 in:0.0008119 40600
index: 471 var_x_pop: 8.964e-13 var_y_pop:2.07761e+12
index: 471 corr: -0.400209 in:0.0008119 0
index: 472 var_x_pop: 2.2276e-12 var_y_pop:2.33805e+12
index: 472 corr: -0.497372 in:0.0008093 2.3175e+06
index: 473 var_x_pop: 2.7881e-12 var_y_pop:5.5572e+11
index: 473 corr: -0.865511 in:0.0008104 1.2335e+06
index: 474 var_x_pop: 2.9276e-12 var_y_pop:5.5863e+11
index: 474 corr: -0.761843 in:0.0008108 12500
index: 475 var_x_pop: 2.7141e-12 var_y_pop:5.59074e+11
index: 475 corr: -0.689292 in:0.0008108 0
index: 476 var_x_pop: 2.4864e-12 var_y_pop:5.59074e+11
index: 476 corr: -0.620848 in:0.0008106 6200
index: 477 var_x_pop: 2.0409e-12 var_y_pop:5.59074e+11
index: 477 corr: -0.573732 in:0.0008106 0
index: 478 var_x_pop: 1.3776e-12 var_y_pop:5.59074e+11
index: 478 corr: -0.56257 in:0.0008106 0
index: 479 var_x_pop: 4.965e-13 var_y_pop:5.59074e+11
index: 479 corr: -0.710952 in:0.0008106 0
index: 480 var_x_pop: 3.524e-13 var_y_pop:5.60712e+11
index: 480 corr: -0.764245 in:0.0008108 15900
index: 481 var_x_pop: 1.841e-13 var_y_pop:5.60712e+11
index: 481 corr: -0.934601 in:0.0008108 0
index: 482 var_x_pop: 1.76e-14 var_y_pop:1.36116e+11
index: 482 corr: -0.699695 in:0.0008108 0
index: 483 var_x_pop: 9.6e-15 var_y_pop:3.27784e+07
index: 484 var_x_pop: 5.44e-14 var_y_pop:2.72076e+07
index: 484 corr: -0.268948 in:0.00081 8700
index: 485 var_x_pop: 8.64e-14 var_y_pop:2.72076e+07
index: 485 corr: -0.0526998 in:0.00081 0
index: 486 var_x_pop: 1.4164e-12 var_y_pop:3.90263e+13
index: 486 corr: 0.969072 in:0.0008144 2.08264e+07
index: 487 var_x_pop: 1.6404e-12 var_y_pop:3.8774e+13
index: 487 corr: 0.864214 in:0.0008126 715500
index: 488 var_x_pop: 1.7376e-12 var_y_pop:3.87537e+13
index: 488 corr: 0.793233 in:0.0008124 47500
index: 489 var_x_pop: 1.8096e-12 var_y_pop:3.8731e+13
index: 489 corr: 0.72657 in:0.0008126 53100
index: 490 var_x_pop: 1.7601e-12 var_y_pop:3.83411e+13
index: 490 corr: 0.715646 in:0.0008119 1.2312e+06
index: 491 var_x_pop: 1.738e-12 var_y_pop:3.83274e+13
index: 491 corr: 0.672909 in:0.0008125 30300
index: 492 var_x_pop: 1.6581e-12 var_y_pop:3.83274e+13
index: 492 corr: 0.64007 in:0.0008125 0
index: 493 var_x_pop: 1.5204e-12 var_y_pop:3.83274e+13
index: 493 corr: 0.617401 in:0.0008125 0
index: 494 var_x_pop: 1.0129e-12 var_y_pop:3.83313e+13
index: 494 corr: 0.664785 in:0.0008125 0
index: 495 var_x_pop: 8.316e-13 var_y_pop:3.83056e+13
index: 495 corr: 0.537117 in:0.0008149 56900
index: 496 var_x_pop: 1.0061e-12 var_y_pop:1.58142e+11
index: 496 corr: -0.362986 in:0.0008149 0
index: 497 var_x_pop: 1.3661e-12 var_y_pop:1.32359e+11
index: 497 corr: -0.364847 in:0.000815 500
index: 498 var_x_pop: 1.838e-12 var_y_pop:1.62026e+11
index: 498 corr: -0.0638026 in:0.0008157 736100
index: 499 var_x_pop: 2.1449e-12 var_y_pop:1.63955e+11
index: 499 corr: -0.15808 in:0.0008157 0
Testing refactored Tot series functions (v2)...
C++ operator tests completed (including v2 functions).
Running Python operators test...
加载测试数据...
数据已加载。形状: (500, 518)
测试基本算术运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Add.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Minus.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Multiply.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Divide.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Sqrt.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Log.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/inv.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Power.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Abs.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Sign.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Exp.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Reverse.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Ceil.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Floor.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Round.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/SignedPower.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Softsign.csv
测试逻辑运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/And.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Or.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Not.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Xor.csv
测试比较运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Equal.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Mthan.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/MEthan.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Lthan.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/LEthan.csv
测试数据工具函数...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/FilterInf.csv
数据工具函数错误: 'float' object has no attribute 'copy'
测试归约运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Min.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Max.csv
测试时间序列运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Delay.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Mean.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Sum.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Stdev.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Min.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Max.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Delta.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Divide.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_ChgRate.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Argmax.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Argmin.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Rank.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Median.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Corr.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Cov.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Skewness.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Kurtosis.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Scale.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Product.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_TransNorm.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Decay.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Decay2.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Partial_corr.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Regression_A.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Regression_B.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Regression_C.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Regression_D.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Entropy.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_MaxDD.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_MeanChg.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Quantile_A.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Quantile_B.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Quantile_C.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Quantile_D.csv
测试面板运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_Mean.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_Rank.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_Stand.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_TransNorm.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_Rank2.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_RankCentered.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_FillMax.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_FillMin.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_TransStd.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_Winsor.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_Cut.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_CrossFit.csv
测试分组运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_GroupRank.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_GroupNorm.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_GroupNeutral.csv
测试Tot系列函数...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Mean.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Sum.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Stdev.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Delta.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Divide.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_ChgRate.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Rank.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_ArgMax.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_ArgMin.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Max.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Min.csv
Python 算子测试完成。
Comparing results...
在所有实现中找到 131 个唯一因子
C++: 131 个因子
Python: 89 个因子

比较结果:
================================================================================
因子                   C++        Python     C++ vs Python            
--------------------------------------------------------------------------------
Abs                  ✓          ✓          ✓                        
Add                  ✓          ✓          ✓                        
And                  ✓          ✓          ✓                        
Ceil                 ✓          ✓          ✗ (文件不相等 (最大差异: 1.00e+00 > 容差: 1.00e-08))
Divide               ✓          ✓          ✓                        
Equal                ✓          ✓          ✓                        
Exp                  ✓          ✓          ✓                        
FillNan              ✓          ✗          N/A                      
FilterInf            ✓          ✓          ✓                        
Floor                ✓          ✓          ✓                        
LEthan               ✓          ✓          ✓                        
Log                  ✓          ✓          ✓                        
Lthan                ✓          ✓          ✓                        
MEthan               ✓          ✓          ✓                        
Max                  ✓          ✓          ✓                        
Min                  ✓          ✓          ✓                        
Minus                ✓          ✓          ✓                        
Mthan                ✓          ✓          ✓                        
Multiply             ✓          ✓          ✓                        
Not                  ✓          ✓          ✓                        
Or                   ✓          ✓          ✓                        
Power                ✓          ✓          ✗ (文件不相等 (最大差异: 1.00e-03 > 容差: 1.00e-08))
Reverse              ✓          ✓          ✓                        
Round                ✓          ✓          ✗ (文件不相等 (最大差异: 1.00e+00 > 容差: 1.00e-08))
Sign                 ✓          ✓          ✓                        
SignedPower          ✓          ✓          ✓                        
Softsign             ✓          ✓          ✓                        
Sqrt                 ✓          ✓          ✓                        
Tot_ArgMax           ✓          ✓          ✓                        
Tot_ArgMax_v2        ✓          ✗          N/A                      
Tot_ArgMin           ✓          ✓          ✓                        
Tot_ArgMin_v2        ✓          ✗          N/A                      
Tot_ChgRate          ✓          ✓          ✓                        
Tot_ChgRate_v2       ✓          ✗          N/A                      
Tot_Delta            ✓          ✓          ✓                        
Tot_Delta_v2         ✓          ✗          N/A                      
Tot_Divide           ✓          ✓          ✓                        
Tot_Divide_v2        ✓          ✗          N/A                      
Tot_Max              ✓          ✓          ✓                        
Tot_Max_v2           ✓          ✗          N/A                      
Tot_Mean             ✓          ✓          ✓                        
Tot_Mean_v2          ✓          ✗          N/A                      
Tot_Min              ✓          ✓          ✓                        
Tot_Min_v2           ✓          ✗          N/A                      
Tot_Rank             ✓          ✓          ✓                        
Tot_Rank_v2          ✓          ✗          N/A                      
Tot_Stdev            ✓          ✓          ✓                        
Tot_Stdev_v2         ✓          ✗          N/A                      
Tot_Sum              ✓          ✓          ✓                        
Tot_Sum_v2           ✓          ✗          N/A                      
UnEqual              ✓          ✗          N/A                      
Xor                  ✓          ✓          ✓                        
getInf               ✓          ✗          N/A                      
getNan               ✓          ✗          N/A                      
inv                  ✓          ✓          ✓                        
pn_CrossFit          ✓          ✓          ✓                        
pn_Cut               ✓          ✓          ✓                        
pn_FillMax           ✓          ✓          ✓                        
pn_FillMin           ✓          ✓          ✓                        
pn_GroupNeutral      ✓          ✓          ✗ (文件不相等 (最大差异: 1.00e-07 > 容差: 1.00e-08))
pn_GroupNorm         ✓          ✓          ✓                        
pn_GroupRank         ✓          ✓          ✓                        
pn_Mean              ✓          ✓          ✓                        
pn_Rank              ✓          ✓          ✓                        
pn_Rank2             ✓          ✓          ✓                        
pn_RankCentered      ✓          ✓          ✓                        
pn_Stand             ✓          ✓          ✓                        
pn_TransNorm         ✓          ✓          ✗ (文件不相等 (最大差异: 5.21e+00 > 容差: 1.00e-08))
pn_TransStd          ✓          ✓          ✓                        
pn_Winsor            ✓          ✓          ✓                        
ts_Argmax            ✓          ✓          ✓                        
ts_Argmax_v2         ✓          ✗          N/A                      
ts_Argmin            ✓          ✓          ✓                        
ts_Argmin_v2         ✓          ✗          N/A                      
ts_ChgRate           ✓          ✓          ✓                        
ts_ChgRate_v2        ✓          ✗          N/A                      
ts_Corr              ✓          ✓          ✗ (文件不相等 (最大差异: 9.92e-05 > 容差: 1.00e-08))
ts_Corr_v2           ✓          ✗          N/A                      
ts_Cov               ✓          ✓          ✓                        
ts_Cov_v2            ✓          ✗          N/A                      
ts_Decay             ✓          ✓          ✓                        
ts_Decay2            ✓          ✓          ✓                        
ts_Decay2_v2         ✓          ✗          N/A                      
ts_Decay_v2          ✓          ✗          N/A                      
ts_Delay             ✓          ✓          ✓                        
ts_Delay_v2          ✓          ✗          N/A                      
ts_Delta             ✓          ✓          ✓                        
ts_Delta_v2          ✓          ✗          N/A                      
ts_Divide            ✓          ✓          ✓                        
ts_Divide_v2         ✓          ✗          N/A                      
ts_Entropy           ✓          ✓          ✗ (文件不相等 (最大差异: 4.19e-02 > 容差: 1.00e-08))
ts_Kurtosis          ✓          ✓          ✗ (文件不相等 (最大差异: 3.28e+04 > 容差: 1.00e-08))
ts_Kurtosis_v2       ✓          ✗          N/A                      
ts_Max               ✓          ✓          ✓                        
ts_MaxDD             ✓          ✓          ✓                        
ts_MaxDD_v2          ✓          ✗          N/A                      
ts_Max_v2            ✓          ✗          N/A                      
ts_Mean              ✓          ✓          ✓                        
ts_MeanChg           ✓          ✓          ✓                        
ts_MeanChg_v2        ✓          ✗          N/A                      
ts_Mean_v2           ✓          ✗          N/A                      
ts_Median            ✓          ✓          ✓                        
ts_Median_v2         ✓          ✗          N/A                      
ts_Min               ✓          ✓          ✓                        
ts_Min_v2            ✓          ✗          N/A                      
ts_Partial_corr      ✓          ✓          ✗ (文件不相等 (最大差异: 4.30e+00 > 容差: 1.00e-08))
ts_Product           ✓          ✓          ✓                        
ts_Product_v2        ✓          ✗          N/A                      
ts_Quantile_A        ✓          ✓          ✓                        
ts_Quantile_A_v2     ✓          ✗          N/A                      
ts_Quantile_B        ✓          ✓          ✓                        
ts_Quantile_B_v2     ✓          ✗          N/A                      
ts_Quantile_C        ✓          ✓          ✓                        
ts_Quantile_C_v2     ✓          ✗          N/A                      
ts_Quantile_D        ✓          ✓          ✓                        
ts_Quantile_D_v2     ✓          ✗          N/A                      
ts_Rank              ✓          ✓          ✓                        
ts_Rank_v2           ✓          ✗          N/A                      
ts_Regression_A      ✓          ✓          ✗ (文件不相等 (最大差异: 7.23e-02 > 容差: 1.00e-08))
ts_Regression_B      ✓          ✓          ✗ (文件不相等 (最大差异: 2.32e+02 > 容差: 1.00e-08))
ts_Regression_C      ✓          ✓          ✗ (文件不相等 (最大差异: 5.79e-04 > 容差: 1.00e-08))
ts_Regression_D      ✓          ✓          ✗ (文件不相等 (最大差异: 5.79e-04 > 容差: 1.00e-08))
ts_Scale             ✓          ✓          ✓                        
ts_Scale_v2          ✓          ✗          N/A                      
ts_Skewness          ✓          ✓          ✗ (文件不相等 (最大差异: 2.19e-01 > 容差: 1.00e-08))
ts_Skewness_v2       ✓          ✗          N/A                      
ts_Stdev             ✓          ✓          ✓                        
ts_Stdev_v2          ✓          ✗          N/A                      
ts_Sum               ✓          ✓          ✓                        
ts_Sum_v2            ✓          ✗          N/A                      
ts_TransNorm         ✓          ✓          ✓                        
--------------------------------------------------------------------------------

摘要:
C++ vs Python: 75/89 个因子匹配 (84.3%)

详细统计信息:

数值不一致的因子:
  Ceil (cpp_vs_py): 最大差异 1.00e+00，平均差异 9.91e-01，中位数差异 1.00e+00，形状 (500, 518)，共 259000 个元素
  Power (cpp_vs_py): 最大差异 1.00e-03，平均差异 5.41e-08，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  Round (cpp_vs_py): 最大差异 1.00e+00，平均差异 6.80e-04，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  pn_GroupNeutral (cpp_vs_py): 最大差异 1.00e-07，平均差异 7.07e-11，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  pn_TransNorm (cpp_vs_py): 最大差异 5.21e+00，平均差异 1.69e+00，中位数差异 1.03e+00，形状 (500, 518)，共 259000 个元素
  ts_Corr (cpp_vs_py): 最大差异 9.92e-05，平均差异 3.57e-09，中位数差异 1.00e-11，形状 (500, 518)，共 259000 个元素
  ts_Entropy (cpp_vs_py): 最大差异 4.19e-02，平均差异 4.74e-05，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  ts_Kurtosis (cpp_vs_py): 最大差异 3.28e+04，平均差异 1.55e+00，中位数差异 1.34e+00，形状 (500, 518)，共 259000 个元素
  ts_Partial_corr (cpp_vs_py): 最大差异 4.30e+00，平均差异 7.71e-05，中位数差异 3.40e-10，形状 (500, 518)，共 259000 个元素
  ts_Regression_A (cpp_vs_py): 最大差异 7.23e-02，平均差异 1.13e-06，中位数差异 2.30e-10，形状 (500, 518)，共 259000 个元素
  ts_Regression_B (cpp_vs_py): 最大差异 2.32e+02，平均差异 3.61e-03，中位数差异 3.90e-11，形状 (500, 518)，共 259000 个元素
  ts_Regression_C (cpp_vs_py): 最大差异 5.79e-04，平均差异 5.95e-09，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  ts_Regression_D (cpp_vs_py): 最大差异 5.79e-04，平均差异 6.27e-09，中位数差异 4.00e-14，形状 (500, 518)，共 259000 个元素
  ts_Skewness (cpp_vs_py): 最大差异 2.19e-01，平均差异 1.38e-06，中位数差异 4.41e-09，形状 (500, 518)，共 259000 个元素
Analyzing operator coverage...
C++算子总数: 90
Python算子总数: 98


算子覆盖分析:
================================================================================

在C++中但不在Python中的算子 (0):

在Python中但不在C++中的算子 (8):
  - Repmat
  - calc
  - calc2
  - delayed
  - get_dfs
  - is_number
  - njit
  - parse_expression

可能命名不一致的算子:
================================================================================

ts_系列函数:
  Python: 28 个
  C++: 28 个

Tot_系列函数:
  Python: 11 个
  C++: 11 个

分析结果已保存到: operator_coverage_analysis.txt
All tests completed.
Results are in /home/<USER>/git/feature_operators/test_right/test_results/comparison_results.txt
Operator coverage analysis is in /home/<USER>/git/feature_operators/test_right/operator_coverage_analysis.txt
