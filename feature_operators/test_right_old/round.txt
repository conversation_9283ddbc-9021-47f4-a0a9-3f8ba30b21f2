Configuring and building C++ tests...
-- Found generic BLAS: /usr/lib64/libopenblas.so
-- Eigen3 include directory: /usr/local/include/eigen3
-- Boost include directories: 
-- Boost libraries: 
-- Catch2 include directory: 
-- Configuring done (0.0s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/git/feature_operators/test_right/build
[  3%] Building CXX object feature_operators_build/CMakeFiles/feature_ops_lib.dir/src/rolling_aggregations.cpp.o
[  6%] Linking CXX static library libfeature_ops_lib.a
[ 35%] Built target feature_ops_lib
[ 38%] Linking CXX executable test_optimized_operators
[ 41%] Linking CXX executable benchmark_cpp_operators
[ 48%] Built target benchmark_cpp_operators
[ 48%] Built target test_optimized_operators
[ 51%] Linking CXX executable core_math_tests
[ 54%] Linking CXX executable data_utils_tests
[ 58%] Built target data_utils_tests
[ 61%] Linking CXX executable logical_ops_tests
[ 64%] Built target core_math_tests
[ 67%] Linking CXX executable comparison_ops_tests
[ 70%] Built target comparison_ops_tests
[ 74%] Linking CXX executable reduction_ops_tests
[ 77%] Built target logical_ops_tests
[ 80%] Linking CXX executable panel_ops_tests
[ 83%] Built target reduction_ops_tests
[ 87%] Linking CXX executable timeseries_ops_tests
[ 90%] Built target panel_ops_tests
[ 93%] Linking CXX executable group_ops_tests
[ 96%] Built target timeseries_ops_tests
[100%] Built target group_ops_tests
Running C++ operators test...
Loading test data...
Data loaded. Rows: 500, Cols: 518
Testing basic arithmetic operators...
Testing logical operators...
Testing comparison operators...
Testing data utility functions...
Testing reduction operators...
Testing time series operators...
Testing panel operators...
Testing group operators...
Testing Tot series functions...
Testing refactored time series operators (v2)...
rounding:0 origin:0.167386
rounding:0 origin:0.247788
rounding:0 origin:0.478612
rounding:0 origin:0.0268765
rounding:0 origin:0.215286
rounding:1 origin:0.845354
rounding:2 origin:2.17729
rounding:0 origin:0.0026399
rounding:0 origin:0.0823848
rounding:0 origin:0.184848
rounding:1 origin:0.687693
rounding:1 origin:0.844646
rounding:0 origin:0.296842
rounding:5 origin:4.90743
rounding:0 origin:0.133811
rounding:0 origin:0.0419756
rounding:15 origin:14.5506
rounding:0 origin:0.208552
rounding:0 origin:0.264259
rounding:0 origin:0.00982032
rounding:0 origin:0.0192112
rounding:0 origin:0.0251374
rounding:2 origin:1.85278
rounding:1 origin:0.70647
rounding:0 origin:0.00777752
rounding:0 origin:0.00143824
rounding:0 origin:0.0176976
rounding:0 origin:0.190579
rounding:0 origin:0.0820726
rounding:0 origin:0.0737438
rounding:0 origin:0.000812851
rounding:4 origin:3.90133
rounding:91 origin:91.4492
rounding:0 origin:0.0170434
rounding:1 origin:1.0742
rounding:0 origin:0.00927172
rounding:1 origin:0.634902
rounding:0 origin:0.481062
rounding:0 origin:0.0467254
rounding:0 origin:0.0612585
rounding:0 origin:0.0444499
rounding:0 origin:0.0475527
rounding:0 origin:0.249506
rounding:0 origin:0.198796
rounding:0 origin:0.0082428
rounding:0 origin:0.0221927
rounding:3223 origin:3222.86
rounding:0 origin:0.204874
rounding:0 origin:0.0325319
rounding:0 origin:0.224589
rounding:0 origin:0.0610817
rounding:0 origin:0.0711793
rounding:0 origin:0.148862
rounding:7 origin:7.04844
rounding:0 origin:0.0205189
rounding:0 origin:0.151966
rounding:0 origin:0.0656014
rounding:1 origin:0.624714
rounding:40 origin:40.4956
rounding:0 origin:0.0274266
rounding:0 origin:0.440549
rounding:6 origin:5.65752
rounding:0 origin:0.14511
rounding:0 origin:0.0150917
rounding:5537 origin:5536.59
rounding:1 origin:0.799955
rounding:0 origin:0.0330119
rounding:1 origin:1.00014
rounding:1 origin:1.20247
rounding:0 origin:0.199588
rounding:0 origin:0.0732879
rounding:0 origin:0.029166
rounding:9 origin:8.96438
rounding:0 origin:0.0181703
rounding:0 origin:0.0223338
rounding:0 origin:0.0833551
rounding:0 origin:0.0229098
rounding:0 origin:0.224286
rounding:1 origin:0.526648
rounding:0 origin:0.013751
rounding:1 origin:0.763595
rounding:0 origin:0.0547663
rounding:0 origin:0.016759
rounding:0 origin:0.209571
rounding:131 origin:131.243
rounding:0 origin:0.00311384
rounding:1726 origin:1725.88
rounding:3209 origin:3209
rounding:0 origin:0.195505
rounding:1 origin:0.786618
rounding:0 origin:0.324575
rounding:0 origin:0.00234047
rounding:0 origin:0.28595
rounding:0 origin:0.0711102
rounding:5 origin:4.61105
rounding:0 origin:0.0136871
rounding:0 origin:0.0682501
rounding:0 origin:0.204796
rounding:0 origin:0.0136653
rounding:5 origin:4.92661
rounding:0 origin:0.0202701
rounding:5 origin:5.13457
rounding:9 origin:9.27161
rounding:1 origin:0.91065
rounding:0 origin:0.00567508
rounding:0 origin:0.496028
rounding:392 origin:392.174
rounding:0 origin:0.00771172
rounding:0 origin:0.228412
rounding:0 origin:0.0714345
rounding:1 origin:0.637634
rounding:0 origin:0.10367
rounding:19 origin:19.3056
rounding:0 origin:0.160516
rounding:0 origin:0.0856872
rounding:0 origin:0.105411
rounding:1 origin:0.699805
rounding:0 origin:0.0329051
rounding:0 origin:0.0185593
rounding:0 origin:0.284873
rounding:0 origin:0.0490435
rounding:1 origin:0.59839
rounding:21 origin:21.4475
rounding:0 origin:0.149438
rounding:0 origin:0.0349585
rounding:0 origin:0.492244
rounding:0 origin:0.0580751
rounding:0 origin:0.0333888
rounding:0 origin:0.0276738
rounding:0 origin:0.0823249
rounding:1 origin:1.31047
rounding:0 origin:0.0185279
rounding:0 origin:0.257157
rounding:0 origin:0.00852057
rounding:0 origin:0.450054
rounding:0 origin:0.14544
rounding:15 origin:15.3179
rounding:0 origin:0.0419162
rounding:0 origin:0.191044
rounding:13 origin:13.3103
rounding:0 origin:0.0472113
rounding:1 origin:1.23512
rounding:0 origin:0.300549
rounding:0 origin:0.0307039
rounding:0 origin:0.243165
rounding:0 origin:0.0855888
rounding:0 origin:0.000487101
rounding:3 origin:3.09135
rounding:0 origin:0.222532
rounding:3 origin:2.77916
rounding:0 origin:0.114815
rounding:0 origin:0.0271969
rounding:18 origin:18.0979
rounding:0 origin:0.012109
rounding:0 origin:0.105512
rounding:0 origin:0.0631152
rounding:0 origin:0.180817
rounding:0 origin:0.0583784
rounding:6 origin:5.52008
rounding:0 origin:0.159346
rounding:0 origin:0.20757
rounding:0 origin:0.0125999
rounding:18 origin:18.0971
rounding:12 origin:11.5604
rounding:421 origin:421.475
rounding:0 origin:0.0248207
rounding:0 origin:0.344401
rounding:1 origin:1.20412
rounding:0 origin:0.215102
rounding:12 origin:12.0841
rounding:0 origin:0.0197588
rounding:0 origin:0.0833617
rounding:1 origin:0.62663
rounding:640 origin:640.429
rounding:0 origin:0.274766
rounding:1 origin:0.650372
rounding:0 origin:0.017445
rounding:0 origin:0.057026
rounding:0 origin:0.097309
rounding:0 origin:0.355595
rounding:0 origin:0.00862666
rounding:0 origin:0.0670275
rounding:1 origin:1.00284
rounding:1 origin:0.723804
rounding:0 origin:0.00521339
rounding:0 origin:0.00450832
rounding:1 origin:0.811667
rounding:0 origin:0.0055792
rounding:0 origin:0.488132
rounding:0 origin:0.120585
rounding:0 origin:0.266936
rounding:0 origin:0.138559
rounding:0 origin:0.0122023
rounding:1 origin:0.661094
rounding:0 origin:0.364365
rounding:0 origin:0.155341
rounding:1 origin:0.640344
rounding:0 origin:0.147672
rounding:0 origin:0.0185449
rounding:0 origin:0.432176
rounding:0 origin:0.0110748
rounding:0 origin:0.00346478
rounding:1 origin:0.519089
rounding:0 origin:0.0648373
rounding:0 origin:0.407513
rounding:1 origin:1.24303
rounding:0 origin:0.0476504
rounding:0 origin:0.0901189
rounding:5 origin:4.52469
rounding:0 origin:0.131301
rounding:0 origin:0.144399
rounding:0 origin:0.00250324
rounding:3 origin:3.06871
rounding:0 origin:0.0193929
rounding:1 origin:0.696777
rounding:0 origin:0.0036935
rounding:0 origin:0.00181196
rounding:0 origin:0.0612058
rounding:0 origin:0.00111538
rounding:2 origin:1.68965
rounding:0 origin:0.0602667
rounding:35 origin:35.1484
rounding:0 origin:0.480633
rounding:1 origin:1.28895
rounding:3 origin:3.45407
rounding:0 origin:0.0664696
rounding:5 origin:5.00262
rounding:0 origin:0.178418
rounding:0 origin:0.325804
rounding:4 origin:4.39257
rounding:0 origin:0.1493
rounding:0 origin:0.0119271
rounding:0 origin:0.201688
rounding:0 origin:0.023433
rounding:5 origin:5.34134
rounding:0 origin:0.043406
rounding:0 origin:0.241948
rounding:0 origin:0.052018
rounding:1 origin:0.931563
rounding:18 origin:18.2551
rounding:0 origin:0.351561
rounding:0 origin:0.0529983
rounding:0 origin:0.00290961
rounding:0 origin:0.365688
rounding:1 origin:0.575347
rounding:1 origin:0.611064
rounding:1 origin:0.651475
rounding:5 origin:4.66772
rounding:13 origin:12.6933
rounding:12 origin:11.6334
rounding:0 origin:0.0245469
rounding:0 origin:0.00233848
rounding:0 origin:0.012183
rounding:0 origin:0.147251
rounding:0 origin:0.292828
rounding:1 origin:0.79566
rounding:0 origin:0.0910504
rounding:0 origin:0.0189568
rounding:0 origin:0.175418
rounding:0 origin:0.120141
rounding:0 origin:0.0116345
rounding:0 origin:0.264681
rounding:0 origin:0.0380088
rounding:0 origin:0.175332
rounding:1 origin:1.37281
rounding:166 origin:166.288
rounding:0 origin:0.12317
rounding:0 origin:0.000174917
rounding:0 origin:0.150846
rounding:8 origin:8.21252
rounding:0 origin:0.00322446
rounding:3 origin:2.5732
rounding:0 origin:0.00338718
rounding:0 origin:0.121297
rounding:0 origin:0.420104
rounding:0 origin:0.151702
rounding:4 origin:3.71309
rounding:0 origin:0.0740432
rounding:0 origin:0.0665476
rounding:31 origin:30.7886
rounding:0 origin:0.0637332
rounding:0 origin:0.266454
rounding:0 origin:0.378986
rounding:9 origin:8.5022
rounding:0 origin:0.220657
rounding:0 origin:0.0141737
rounding:0 origin:0.0145114
rounding:0 origin:0.00218819
rounding:96 origin:96.4675
rounding:0 origin:0.0550448
rounding:0 origin:0.010273
rounding:0 origin:0.0180833
rounding:0 origin:0.17038
rounding:0 origin:0.0781318
rounding:2 origin:2.05443
rounding:0 origin:0.105583
rounding:1 origin:1.30865
rounding:0 origin:0.0632669
rounding:0 origin:0.0126162
rounding:0 origin:0.111691
rounding:0 origin:0.0046896
rounding:0 origin:0.000730792
rounding:0 origin:0.0136976
rounding:0 origin:0.270477
rounding:1 origin:1.32828
rounding:0 origin:0.328005
rounding:0 origin:0.0279413
rounding:5 origin:5.06863
rounding:2476 origin:2475.94
rounding:5 origin:4.71733
rounding:1 origin:0.913114
rounding:0 origin:0.000395771
rounding:1 origin:0.916333
rounding:0 origin:0.150369
rounding:0 origin:0.200997
rounding:0 origin:0.0714058
rounding:0 origin:0.0256271
rounding:0 origin:0.429061
rounding:0 origin:0.0277357
rounding:0 origin:0.127268
rounding:0 origin:0.00502266
rounding:0 origin:0.00750424
rounding:0 origin:0.0224208
rounding:0 origin:0.0865733
rounding:0 origin:0.0253018
rounding:1 origin:1.20775
rounding:0 origin:0.0594637
rounding:0 origin:0.00379728
rounding:1 origin:0.938721
rounding:0 origin:0.00931574
rounding:0 origin:0.118973
rounding:0 origin:0.18524
rounding:0 origin:0.0806805
rounding:0 origin:0.145002
rounding:0 origin:0.00159395
rounding:2 origin:2.49223
rounding:0 origin:0.0254297
rounding:0 origin:0.075386
rounding:0 origin:0.175937
rounding:0 origin:0.0430034
rounding:0 origin:0.0574525
rounding:0 origin:0.000852303
rounding:0 origin:0.00316307
rounding:1 origin:0.622298
rounding:1 origin:0.531002
rounding:0 origin:0.0425293
rounding:0 origin:0.120902
rounding:0 origin:0.00175036
rounding:0 origin:0.0903022
rounding:1 origin:0.979306
rounding:0 origin:0.0708575
rounding:0 origin:0.301331
rounding:0 origin:0.211934
rounding:0 origin:0.0933664
rounding:0 origin:0.00885148
rounding:0 origin:0.11431
rounding:2 origin:1.90281
rounding:3 origin:3.08047
rounding:0 origin:0.247105
rounding:0 origin:0.156827
rounding:0 origin:0.244449
rounding:0 origin:0.221982
rounding:0 origin:0.0748936
rounding:0 origin:0.109214
rounding:0 origin:0.00338453
rounding:1 origin:0.740608
rounding:3 origin:2.56477
rounding:1 origin:0.923824
rounding:3 origin:3.2921
rounding:0 origin:0.0826667
rounding:2 origin:1.52364
rounding:1 origin:0.624079
rounding:2 origin:2.22102
rounding:7 origin:7.16094
rounding:0 origin:0.0354311
rounding:1 origin:1.18125
rounding:1 origin:0.562839
rounding:13 origin:12.7423
rounding:0 origin:0.0126673
rounding:6 origin:5.72262
rounding:1 origin:0.87815
rounding:0 origin:0.0922595
rounding:0 origin:0.0583762
rounding:4 origin:3.78679
rounding:1 origin:0.999999
rounding:0 origin:0.0233525
rounding:0 origin:0.07438
rounding:3 origin:2.69985
rounding:0 origin:0.105947
rounding:222 origin:221.881
rounding:0 origin:0.337571
rounding:0 origin:0.370511
rounding:6 origin:6.41424
rounding:2 origin:1.579
rounding:0 origin:0.004359
rounding:0 origin:0.0120752
rounding:0 origin:0.0620472
rounding:0 origin:0.291716
rounding:0 origin:0.410539
rounding:1 origin:0.724738
rounding:9 origin:9.00822
rounding:3 origin:3.29662
rounding:0 origin:0.205494
rounding:0 origin:0.215323
rounding:0 origin:0.0644465
rounding:0 origin:0.0308648
rounding:0 origin:0.273859
rounding:0 origin:0.187702
rounding:0 origin:0.167096
rounding:2 origin:2.35057
rounding:336 origin:336.343
rounding:2 origin:1.6199
rounding:0 origin:0.0204611
rounding:0 origin:0.181275
rounding:0 origin:0.352245
rounding:0 origin:0.0411254
rounding:0 origin:0.325336
rounding:0 origin:0.0798193
rounding:0 origin:0.130956
rounding:1 origin:0.615512
rounding:1 origin:0.933226
rounding:3 origin:2.56642
rounding:0 origin:0.0121998
rounding:0 origin:0.000535779
rounding:0 origin:0.171398
rounding:0 origin:0.285639
rounding:0 origin:0.403614
rounding:0 origin:0.241439
rounding:0 origin:0.0224505
rounding:1 origin:0.833884
rounding:0 origin:0.0988926
rounding:0 origin:0.149005
rounding:0 origin:0.024494
rounding:1 origin:0.674823
rounding:0 origin:0.4426
rounding:0 origin:0.15834
rounding:0 origin:0.050274
rounding:0 origin:0.215215
rounding:0 origin:0.0904188
rounding:3 origin:2.78334
rounding:0 origin:0.0020951
rounding:0 origin:0.024004
rounding:43 origin:43.0362
rounding:2 origin:1.66931
rounding:0 origin:0.409434
rounding:0 origin:0.0352404
rounding:0 origin:0.348369
rounding:1 origin:1.1874
rounding:103159 origin:103159
rounding:2 origin:1.67918
rounding:0 origin:0.0750088
rounding:0 origin:0.0780747
rounding:0 origin:0.415619
rounding:0 origin:0.00993916
rounding:0 origin:0.00505606
rounding:0 origin:0.33817
rounding:0 origin:0.104687
rounding:21 origin:20.7105
rounding:2 origin:1.68112
rounding:0 origin:0.128998
rounding:0 origin:0.20303
rounding:0 origin:0.00666368
rounding:1 origin:0.81922
rounding:0 origin:0.0646508
rounding:1 origin:0.933967
rounding:0 origin:0.000602823
rounding:2 origin:1.50227
rounding:0 origin:0.0300905
rounding:3 origin:2.68858
rounding:0 origin:0.315549
rounding:4 origin:3.94457
rounding:0 origin:0.0899667
rounding:0 origin:0.123203
rounding:0 origin:0.227085
rounding:0 origin:0.090687
rounding:1 origin:0.964097
rounding:0 origin:0.215801
rounding:0 origin:0.0650997
rounding:23 origin:22.9047
rounding:0 origin:0.00051363
rounding:0 origin:0.00238505
rounding:0 origin:0.0516469
rounding:0 origin:0.0282095
rounding:0 origin:0.0231987
rounding:0 origin:0.0230738
rounding:0 origin:0.290212
rounding:0 origin:0.0800367
rounding:0 origin:0.378073
rounding:0 origin:0.0589046
rounding:1 origin:0.605172
rounding:1 origin:0.649194
rounding:0 origin:0.111642
rounding:27 origin:27.4158
rounding:0 origin:0.0860261
rounding:22 origin:22.2163
rounding:0 origin:0.118254
rounding:26 origin:26.089
rounding:0 origin:0.111404
rounding:1 origin:0.679568
rounding:2 origin:2.28921
rounding:0 origin:0.0579899
rounding:1 origin:0.694895
rounding:0 origin:0.375571
rounding:0 origin:0.156958
rounding:0 origin:0.0171018
rounding:0 origin:0.231471
rounding:3 origin:2.90777
rounding:6 origin:6.29475
rounding:0 origin:0.0024788
rounding:3 origin:3.05555
rounding:0 origin:0.139644
rounding:0 origin:0.0560699
rounding:0 origin:0.000450549
rounding:0 origin:0.0236539
rounding:0 origin:0.117759
rounding:1 origin:0.797599
Testing refactored Tot series functions (v2)...
C++ operator tests completed (including v2 functions).
Running Python operators test...
加载测试数据...
数据已加载。形状: (500, 518)
测试基本算术运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Add.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Minus.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Multiply.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Divide.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Sqrt.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Log.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/inv.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Power.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Abs.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Sign.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Exp.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Reverse.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Ceil.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Floor.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Round.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/SignedPower.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Softsign.csv
测试逻辑运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/And.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Or.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Not.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Xor.csv
测试比较运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Equal.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Mthan.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/MEthan.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Lthan.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/LEthan.csv
测试数据工具函数...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/FilterInf.csv
数据工具函数错误: 'float' object has no attribute 'copy'
测试归约运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Min.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Max.csv
测试时间序列运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Delay.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Mean.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Sum.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Stdev.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Min.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Max.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Delta.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Divide.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_ChgRate.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Argmax.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Argmin.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Rank.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Median.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Corr.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Cov.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Skewness.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Kurtosis.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Scale.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Product.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_TransNorm.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Decay.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Decay2.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Partial_corr.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Regression_A.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Regression_B.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Regression_C.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Regression_D.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Entropy.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_MaxDD.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_MeanChg.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Quantile_A.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Quantile_B.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Quantile_C.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Quantile_D.csv
测试面板运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_Mean.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_Rank.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_Stand.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_TransNorm.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_Rank2.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_RankCentered.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_FillMax.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_FillMin.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_TransStd.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_Winsor.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_Cut.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_CrossFit.csv
测试分组运算符...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_GroupRank.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_GroupNorm.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/pn_GroupNeutral.csv
测试Tot系列函数...
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Mean.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Sum.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Stdev.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Delta.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Divide.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_ChgRate.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Rank.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_ArgMax.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_ArgMin.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Max.csv
已保存到 /home/<USER>/git/feature_operators/test_right/test_results/python/Tot_Min.csv
Python 算子测试完成。
Comparing results...
在所有实现中找到 131 个唯一因子
C++: 131 个因子
Python: 89 个因子

比较结果:
================================================================================
因子                   C++        Python     C++ vs Python            
--------------------------------------------------------------------------------
Abs                  ✓          ✓          ✓                        
Add                  ✓          ✓          ✓                        
And                  ✓          ✓          ✓                        
Ceil                 ✓          ✓          ✗ (文件不相等 (最大差异: 1.00e+00 > 容差: 1.00e-08))
Divide               ✓          ✓          ✓                        
Equal                ✓          ✓          ✓                        
Exp                  ✓          ✓          ✓                        
FillNan              ✓          ✗          N/A                      
FilterInf            ✓          ✓          ✓                        
Floor                ✓          ✓          ✓                        
LEthan               ✓          ✓          ✓                        
Log                  ✓          ✓          ✓                        
Lthan                ✓          ✓          ✓                        
MEthan               ✓          ✓          ✓                        
Max                  ✓          ✓          ✓                        
Min                  ✓          ✓          ✓                        
Minus                ✓          ✓          ✓                        
Mthan                ✓          ✓          ✓                        
Multiply             ✓          ✓          ✓                        
Not                  ✓          ✓          ✓                        
Or                   ✓          ✓          ✓                        
Power                ✓          ✓          ✗ (文件不相等 (最大差异: 1.00e-03 > 容差: 1.00e-08))
Reverse              ✓          ✓          ✓                        
Round                ✓          ✓          ✗ (文件不相等 (最大差异: 1.00e+00 > 容差: 1.00e-08))
Sign                 ✓          ✓          ✓                        
SignedPower          ✓          ✓          ✓                        
Softsign             ✓          ✓          ✓                        
Sqrt                 ✓          ✓          ✓                        
Tot_ArgMax           ✓          ✓          ✓                        
Tot_ArgMax_v2        ✓          ✗          N/A                      
Tot_ArgMin           ✓          ✓          ✓                        
Tot_ArgMin_v2        ✓          ✗          N/A                      
Tot_ChgRate          ✓          ✓          ✓                        
Tot_ChgRate_v2       ✓          ✗          N/A                      
Tot_Delta            ✓          ✓          ✓                        
Tot_Delta_v2         ✓          ✗          N/A                      
Tot_Divide           ✓          ✓          ✓                        
Tot_Divide_v2        ✓          ✗          N/A                      
Tot_Max              ✓          ✓          ✓                        
Tot_Max_v2           ✓          ✗          N/A                      
Tot_Mean             ✓          ✓          ✓                        
Tot_Mean_v2          ✓          ✗          N/A                      
Tot_Min              ✓          ✓          ✓                        
Tot_Min_v2           ✓          ✗          N/A                      
Tot_Rank             ✓          ✓          ✓                        
Tot_Rank_v2          ✓          ✗          N/A                      
Tot_Stdev            ✓          ✓          ✓                        
Tot_Stdev_v2         ✓          ✗          N/A                      
Tot_Sum              ✓          ✓          ✓                        
Tot_Sum_v2           ✓          ✗          N/A                      
UnEqual              ✓          ✗          N/A                      
Xor                  ✓          ✓          ✓                        
getInf               ✓          ✗          N/A                      
getNan               ✓          ✗          N/A                      
inv                  ✓          ✓          ✓                        
pn_CrossFit          ✓          ✓          ✓                        
pn_Cut               ✓          ✓          ✓                        
pn_FillMax           ✓          ✓          ✓                        
pn_FillMin           ✓          ✓          ✓                        
pn_GroupNeutral      ✓          ✓          ✗ (文件不相等 (最大差异: 1.00e-07 > 容差: 1.00e-08))
pn_GroupNorm         ✓          ✓          ✓                        
pn_GroupRank         ✓          ✓          ✓                        
pn_Mean              ✓          ✓          ✓                        
pn_Rank              ✓          ✓          ✓                        
pn_Rank2             ✓          ✓          ✓                        
pn_RankCentered      ✓          ✓          ✓                        
pn_Stand             ✓          ✓          ✓                        
pn_TransNorm         ✓          ✓          ✗ (文件不相等 (最大差异: 5.21e+00 > 容差: 1.00e-08))
pn_TransStd          ✓          ✓          ✓                        
pn_Winsor            ✓          ✓          ✓                        
ts_Argmax            ✓          ✓          ✓                        
ts_Argmax_v2         ✓          ✗          N/A                      
ts_Argmin            ✓          ✓          ✓                        
ts_Argmin_v2         ✓          ✗          N/A                      
ts_ChgRate           ✓          ✓          ✓                        
ts_ChgRate_v2        ✓          ✗          N/A                      
ts_Corr              ✓          ✓          ✗ (文件不相等 (最大差异: 9.92e-05 > 容差: 1.00e-08))
ts_Corr_v2           ✓          ✗          N/A                      
ts_Cov               ✓          ✓          ✓                        
ts_Cov_v2            ✓          ✗          N/A                      
ts_Decay             ✓          ✓          ✓                        
ts_Decay2            ✓          ✓          ✓                        
ts_Decay2_v2         ✓          ✗          N/A                      
ts_Decay_v2          ✓          ✗          N/A                      
ts_Delay             ✓          ✓          ✓                        
ts_Delay_v2          ✓          ✗          N/A                      
ts_Delta             ✓          ✓          ✓                        
ts_Delta_v2          ✓          ✗          N/A                      
ts_Divide            ✓          ✓          ✓                        
ts_Divide_v2         ✓          ✗          N/A                      
ts_Entropy           ✓          ✓          ✗ (文件不相等 (最大差异: 4.19e-02 > 容差: 1.00e-08))
ts_Kurtosis          ✓          ✓          ✗ (文件不相等 (最大差异: 3.28e+04 > 容差: 1.00e-08))
ts_Kurtosis_v2       ✓          ✗          N/A                      
ts_Max               ✓          ✓          ✓                        
ts_MaxDD             ✓          ✓          ✓                        
ts_MaxDD_v2          ✓          ✗          N/A                      
ts_Max_v2            ✓          ✗          N/A                      
ts_Mean              ✓          ✓          ✓                        
ts_MeanChg           ✓          ✓          ✓                        
ts_MeanChg_v2        ✓          ✗          N/A                      
ts_Mean_v2           ✓          ✗          N/A                      
ts_Median            ✓          ✓          ✓                        
ts_Median_v2         ✓          ✗          N/A                      
ts_Min               ✓          ✓          ✓                        
ts_Min_v2            ✓          ✗          N/A                      
ts_Partial_corr      ✓          ✓          ✗ (文件不相等 (最大差异: 4.30e+00 > 容差: 1.00e-08))
ts_Product           ✓          ✓          ✓                        
ts_Product_v2        ✓          ✗          N/A                      
ts_Quantile_A        ✓          ✓          ✓                        
ts_Quantile_A_v2     ✓          ✗          N/A                      
ts_Quantile_B        ✓          ✓          ✓                        
ts_Quantile_B_v2     ✓          ✗          N/A                      
ts_Quantile_C        ✓          ✓          ✓                        
ts_Quantile_C_v2     ✓          ✗          N/A                      
ts_Quantile_D        ✓          ✓          ✓                        
ts_Quantile_D_v2     ✓          ✗          N/A                      
ts_Rank              ✓          ✓          ✓                        
ts_Rank_v2           ✓          ✗          N/A                      
ts_Regression_A      ✓          ✓          ✗ (文件不相等 (最大差异: 7.23e-02 > 容差: 1.00e-08))
ts_Regression_B      ✓          ✓          ✗ (文件不相等 (最大差异: 2.32e+02 > 容差: 1.00e-08))
ts_Regression_C      ✓          ✓          ✗ (文件不相等 (最大差异: 5.79e-04 > 容差: 1.00e-08))
ts_Regression_D      ✓          ✓          ✗ (文件不相等 (最大差异: 5.79e-04 > 容差: 1.00e-08))
ts_Scale             ✓          ✓          ✓                        
ts_Scale_v2          ✓          ✗          N/A                      
ts_Skewness          ✓          ✓          ✗ (文件不相等 (最大差异: 2.19e-01 > 容差: 1.00e-08))
ts_Skewness_v2       ✓          ✗          N/A                      
ts_Stdev             ✓          ✓          ✓                        
ts_Stdev_v2          ✓          ✗          N/A                      
ts_Sum               ✓          ✓          ✓                        
ts_Sum_v2            ✓          ✗          N/A                      
ts_TransNorm         ✓          ✓          ✓                        
--------------------------------------------------------------------------------

摘要:
C++ vs Python: 75/89 个因子匹配 (84.3%)

详细统计信息:

数值不一致的因子:
  Ceil (cpp_vs_py): 最大差异 1.00e+00，平均差异 9.91e-01，中位数差异 1.00e+00，形状 (500, 518)，共 259000 个元素
  Power (cpp_vs_py): 最大差异 1.00e-03，平均差异 5.41e-08，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  Round (cpp_vs_py): 最大差异 1.00e+00，平均差异 6.80e-04，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  pn_GroupNeutral (cpp_vs_py): 最大差异 1.00e-07，平均差异 7.07e-11，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  pn_TransNorm (cpp_vs_py): 最大差异 5.21e+00，平均差异 1.69e+00，中位数差异 1.03e+00，形状 (500, 518)，共 259000 个元素
  ts_Corr (cpp_vs_py): 最大差异 9.92e-05，平均差异 3.57e-09，中位数差异 1.00e-11，形状 (500, 518)，共 259000 个元素
  ts_Entropy (cpp_vs_py): 最大差异 4.19e-02，平均差异 4.74e-05，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  ts_Kurtosis (cpp_vs_py): 最大差异 3.28e+04，平均差异 1.83e-01，中位数差异 3.48e-06，形状 (500, 518)，共 259000 个元素
  ts_Partial_corr (cpp_vs_py): 最大差异 4.30e+00，平均差异 7.71e-05，中位数差异 3.40e-10，形状 (500, 518)，共 259000 个元素
  ts_Regression_A (cpp_vs_py): 最大差异 7.23e-02，平均差异 1.13e-06，中位数差异 2.30e-10，形状 (500, 518)，共 259000 个元素
  ts_Regression_B (cpp_vs_py): 最大差异 2.32e+02，平均差异 3.61e-03，中位数差异 3.90e-11，形状 (500, 518)，共 259000 个元素
  ts_Regression_C (cpp_vs_py): 最大差异 5.79e-04，平均差异 5.95e-09，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素
  ts_Regression_D (cpp_vs_py): 最大差异 5.79e-04，平均差异 6.27e-09，中位数差异 4.00e-14，形状 (500, 518)，共 259000 个元素
  ts_Skewness (cpp_vs_py): 最大差异 2.19e-01，平均差异 1.38e-06，中位数差异 4.41e-09，形状 (500, 518)，共 259000 个元素
Analyzing operator coverage...
C++算子总数: 90
Python算子总数: 98


算子覆盖分析:
================================================================================

在C++中但不在Python中的算子 (0):

在Python中但不在C++中的算子 (8):
  - Repmat
  - calc
  - calc2
  - delayed
  - get_dfs
  - is_number
  - njit
  - parse_expression

可能命名不一致的算子:
================================================================================

ts_系列函数:
  Python: 28 个
  C++: 28 个

Tot_系列函数:
  Python: 11 个
  C++: 11 个

分析结果已保存到: operator_coverage_analysis.txt
All tests completed.
Results are in /home/<USER>/git/feature_operators/test_right/test_results/comparison_results.txt
Operator coverage analysis is in /home/<USER>/git/feature_operators/test_right/operator_coverage_analysis.txt
