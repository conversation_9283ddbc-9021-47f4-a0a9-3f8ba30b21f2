{"cells": [{"cell_type": "code", "execution_count": 1, "id": "06334758", "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "import glob\n", "\n", "def load_csv(filepath):\n", "    \"\"\"加载 CSV 文件到 pandas DataFrame。\"\"\"\n", "    df = pd.read_csv(filepath,  dtype=float,header=None)\n", "    return df\n", "    "]}, {"cell_type": "code", "execution_count": 8, "id": "3b11bc79", "metadata": {}, "outputs": [], "source": ["file1 = \"/home/<USER>/git/feature_operators/test_right/test_results/cpp/ts_Kurtosis_v2.csv\"\n", "file2 = \"/home/<USER>/git/feature_operators/test_right/test_results/python/ts_Kurtosis.csv\"\n", "file_close = \"/home/<USER>/git/cross_selction_strategy/test/operator/test_right/test_data/close.csv\"\n", "file_volume = \"/home/<USER>/git/cross_selction_strategy/test/operator/test_right/test_data/volume.csv\"\n", "\n", "df1 = load_csv(file1)\n", "df2 = load_csv(file2)\n", "df_close = pd.read_csv(file_close, index_col=0)\n", "df_close = df_close.reset_index(drop=True)\n", "df_volume= pd.read_csv(file_volume, index_col=0)\n", "df_volume = df_volume.reset_index(drop=True)\n"]}, {"cell_type": "code", "execution_count": 9, "id": "b4dc99e1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SERAPHUSDT</th>\n", "      <th>PROMPTUSDT</th>\n", "      <th>JUPUSDT</th>\n", "      <th>ALPHAUSDT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.16545</td>\n", "      <td>0.24365</td>\n", "      <td>0.4753</td>\n", "      <td>0.02692</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.16560</td>\n", "      <td>0.24289</td>\n", "      <td>0.4747</td>\n", "      <td>0.02688</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.16560</td>\n", "      <td>0.24209</td>\n", "      <td>0.4748</td>\n", "      <td>0.02682</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.16554</td>\n", "      <td>0.24154</td>\n", "      <td>0.4743</td>\n", "      <td>0.02680</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   SERAPHUSDT  PROMPTUSDT  JUPUSDT  ALPHAUSDT\n", "0     0.16545     0.24365   0.4753    0.02692\n", "1     0.16560     0.24289   0.4747    0.02688\n", "2     0.16560     0.24209   0.4748    0.02682\n", "3     0.16554     0.24154   0.4743    0.02680"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_subset = df_close.iloc[:4, :4]\n", "df_subset = df_subset.reset_index(drop=True)\n", "df_subset"]}, {"cell_type": "code", "execution_count": 15, "id": "168824f6", "metadata": {}, "outputs": [], "source": ["sample_close = df_close.iloc[:, 31:32]\n", "# .to_csv(\"sample_close.csv\")"]}, {"cell_type": "code", "execution_count": 17, "id": "fd57c174", "metadata": {}, "outputs": [], "source": ["sample_volume = df_volume.iloc[:, 31:32]\n", "\n", "# .to_csv(\"sample_volume.csv\")"]}, {"cell_type": "code", "execution_count": 3, "id": "5a16dd7b", "metadata": {}, "outputs": [], "source": ["\n", "def ts_Corr(s1, s2, n):\n", "    n = int(n)\n", "    if n <= 1:\n", "        n = 1\n", "    tem1 = s1.copy()\n", "    tem2 = s2.copy()\n", "    tem1[tem2.isna()] = np.nan\n", "    tem2[tem1.isna()] = np.nan\n", "    tem1_m = tem1.rolling(n, axis=0, min_periods=1).mean()\n", "    tem2_m = tem2.rolling(n, axis=0, min_periods=1).mean()\n", "    tem_prod_m = (tem1 * tem2).rolling(n, axis=0, min_periods=1).mean()\n", "    tem1_std = tem1.rolling(n, axis=0, min_periods=1).std(ddof=0)\n", "    tem2_std = tem2.rolling(n, axis=0, min_periods=1).std(ddof=0)\n", "    res = (tem_prod_m - tem1_m * tem2_m) / (tem1_std * tem2_std)\n", "    return res.replace([-np.inf, np.inf], np.nan)\n", "\n"]}, {"cell_type": "code", "execution_count": 26, "id": "9a02d16e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DENTUSDT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-0.649741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-0.515133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-0.445014</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>-0.116886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0.010839</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>-0.084586</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0.013303</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0.051256</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   DENTUSDT\n", "0       NaN\n", "1 -1.000000\n", "2 -0.649741\n", "3 -0.515133\n", "4 -0.445014\n", "5 -0.116886\n", "6  0.010839\n", "7 -0.084586\n", "8  0.013303\n", "9  0.051256"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["ts_Corr(sample_close, sample_volume, 10)[0:10]"]}, {"cell_type": "code", "execution_count": null, "id": "3fa51da6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>31</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>210</th>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>0.96021</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          31\n", "210      NaN\n", "211      NaN\n", "212      NaN\n", "213      NaN\n", "214  0.96021"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df1.iloc[200:215, 31:32]"]}, {"cell_type": "code", "execution_count": 34, "id": "b04ccbcb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>31</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>-0.335445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>-0.510996</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>-0.857768</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>-0.196027</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>0.545436</td>\n", "    </tr>\n", "    <tr>\n", "      <th>205</th>\n", "      <td>0.481593</td>\n", "    </tr>\n", "    <tr>\n", "      <th>206</th>\n", "      <td>0.388721</td>\n", "    </tr>\n", "    <tr>\n", "      <th>207</th>\n", "      <td>0.341517</td>\n", "    </tr>\n", "    <tr>\n", "      <th>208</th>\n", "      <td>0.306707</td>\n", "    </tr>\n", "    <tr>\n", "      <th>209</th>\n", "      <td>0.231707</td>\n", "    </tr>\n", "    <tr>\n", "      <th>210</th>\n", "      <td>0.164850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>0.219886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>0.219886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>0.691622</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>0.960210</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           31\n", "200 -0.335445\n", "201 -0.510996\n", "202 -0.857768\n", "203 -0.196027\n", "204  0.545436\n", "205  0.481593\n", "206  0.388721\n", "207  0.341517\n", "208  0.306707\n", "209  0.231707\n", "210  0.164850\n", "211  0.219886\n", "212  0.219886\n", "213  0.691622\n", "214  0.960210"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["df2.iloc[200:215, 31:32]"]}, {"cell_type": "code", "execution_count": 10, "id": "73a2c5e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          0                   1                   2                   3    \\\n", "         self     other      self     other      self     other      self   \n", "3    0.326033  0.318632 -1.704025 -1.704026  1.289605  1.290078 -3.033255   \n", "4    0.917941  0.895774 -1.176786 -1.176784  0.947289  0.947274 -1.610528   \n", "5    1.187378  1.203334 -0.386119 -0.386119 -0.782986 -0.783005 -0.589280   \n", "6    1.741640  1.724562 -0.708011 -0.708011 -0.880904 -0.880902 -0.576002   \n", "7    6.051556  6.051687  0.952721  0.952721 -0.128864 -0.128865 -0.477299   \n", "..        ...       ...       ...       ...       ...       ...       ...   \n", "495 -0.997907 -0.997764 -1.082119 -1.082093 -2.207942 -2.207942 -1.880959   \n", "496 -1.064425 -1.064282 -0.700890 -0.700996 -1.994535 -1.994536 -2.070138   \n", "497 -1.166416 -1.166432 -0.262102 -0.262108 -1.220664 -1.220664 -1.622274   \n", "498 -1.051154 -1.051128  0.075431  0.075366  0.282676  0.282677  0.316475   \n", "499 -1.000868 -1.000822 -0.953333 -0.953346  1.593254  1.593255  0.566850   \n", "\n", "                    4              ...       513                 514  \\\n", "        other      self     other  ...      self     other      self   \n", "3   -3.033198  1.501173  1.500864  ...  1.512522  1.511046  2.821809   \n", "4   -1.610547  1.929026  1.928916  ...  0.898449  0.897897  3.316848   \n", "5   -0.589280  1.251851  1.251727  ...  0.257598  0.257547  2.360343   \n", "6   -0.576001  0.493788  0.493775  ...  1.703497  1.703523  1.173088   \n", "7   -0.477301  0.239188  0.239191  ...  0.317965  0.317964  0.598322   \n", "..        ...       ...       ...  ...       ...       ...       ...   \n", "495 -1.880988 -1.054502 -1.054557  ... -1.755808 -1.755804 -2.111826   \n", "496 -2.070116 -1.568121 -1.568139  ... -1.739579 -1.739579 -1.810620   \n", "497 -1.622304 -1.207807 -1.207815  ... -1.222671 -1.222682 -0.817007   \n", "498  0.316457 -1.021118 -1.021124  ... -0.025100 -0.025105  1.363489   \n", "499  0.566836 -0.394506 -0.394534  ...  0.748055  0.748076  1.692368   \n", "\n", "                    515                 516                 517            \n", "        other      self     other      self     other      self     other  \n", "3    2.821773  1.803518  1.804062  2.889394  2.889334  1.869657  1.869658  \n", "4    3.316850 -1.815197 -1.815124  3.250783  3.250789  2.243639  2.243639  \n", "5    2.360347 -1.617073 -1.616606  3.957594  3.957535  1.457068  1.457068  \n", "6    1.173092  1.043146  1.043099  3.491746  3.491743  0.671829  0.671829  \n", "7    0.598321  1.056685  1.056684  2.040148  2.040143  0.264552  0.264552  \n", "..        ...       ...       ...       ...       ...       ...       ...  \n", "495 -2.111826 -1.240210 -1.240162 -2.282575 -2.282573 -2.019619 -2.019619  \n", "496 -1.810619 -1.240210 -1.240162 -2.410933 -2.410930 -2.226651 -2.226651  \n", "497 -0.817009 -1.767578 -1.767589 -2.218050 -2.218049 -1.798379 -1.798378  \n", "498  1.363487 -0.887485 -0.887486 -1.270127 -1.270127 -0.437643 -0.437643  \n", "499  1.692369 -1.480157 -1.480150  0.574703  0.574705  0.773701  0.773701  \n", "\n", "[497 rows x 1028 columns]\n"]}], "source": ["# file1 = \"/home/<USER>/git/feature_operators/test_right/test_results/cpp/Power.csv\"\n", "# file2 = \"/home/<USER>/git/feature_operators/test_right/test_results/python/Power.csv\"\n", "# df1 = load_csv(file1)\n", "# df2 = load_csv(file2)\n", "diff = df1.compare(df2)\n", "print(diff)\n", "import numpy as np\n", "diff[31].to_csv(\"diff.csv\")\n"]}, {"cell_type": "code", "execution_count": 25, "id": "5e72158f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NaN position mismatch: 36 differences\n", "\n", "Locations (row, column) of NaN mismatches:\n", "(210, 31)\n", "(211, 31)\n", "(212, 31)\n", "(213, 31)\n", "(483, 31)\n", "(231, 88)\n", "(1, 147)\n", "(335, 147)\n", "(336, 147)\n", "(1, 303)\n", "(200, 303)\n", "(201, 303)\n", "(202, 303)\n", "(203, 303)\n", "(204, 303)\n", "(205, 303)\n", "(245, 303)\n", "(246, 303)\n", "(247, 303)\n", "(248, 303)\n", "(312, 303)\n", "(313, 303)\n", "(315, 303)\n", "(345, 303)\n", "(346, 303)\n", "(347, 303)\n", "(348, 303)\n", "(349, 303)\n", "(350, 303)\n", "(351, 303)\n", "(352, 303)\n", "(353, 303)\n", "(354, 303)\n", "(355, 303)\n", "(1, 313)\n", "(402, 481)\n", "\n", "DataFrame showing NaN mismatch locations (True means mismatch):\n", "       0      1      2      3      4      5      6      7      8      9    \\\n", "0    False  False  False  False  False  False  False  False  False  False   \n", "1    False  False  False  False  False  False  False  False  False  False   \n", "2    False  False  False  False  False  False  False  False  False  False   \n", "3    False  False  False  False  False  False  False  False  False  False   \n", "4    False  False  False  False  False  False  False  False  False  False   \n", "..     ...    ...    ...    ...    ...    ...    ...    ...    ...    ...   \n", "495  False  False  False  False  False  False  False  False  False  False   \n", "496  False  False  False  False  False  False  False  False  False  False   \n", "497  False  False  False  False  False  False  False  False  False  False   \n", "498  False  False  False  False  False  False  False  False  False  False   \n", "499  False  False  False  False  False  False  False  False  False  False   \n", "\n", "     ...    508    509    510    511    512    513    514    515    516    517  \n", "0    ...  False  False  False  False  False  False  False  False  False  False  \n", "1    ...  False  False  False  False  False  False  False  False  False  False  \n", "2    ...  False  False  False  False  False  False  False  False  False  False  \n", "3    ...  False  False  False  False  False  False  False  False  False  False  \n", "4    ...  False  False  False  False  False  False  False  False  False  False  \n", "..   ...    ...    ...    ...    ...    ...    ...    ...    ...    ...    ...  \n", "495  ...  False  False  False  False  False  False  False  False  False  False  \n", "496  ...  False  False  False  False  False  False  False  False  False  False  \n", "497  ...  False  False  False  False  False  False  False  False  False  False  \n", "498  ...  False  False  False  False  False  False  False  False  False  False  \n", "499  ...  False  False  False  False  False  False  False  False  False  False  \n", "\n", "[500 rows x 518 columns]\n"]}], "source": ["df1_nan = pd.isna(df1)\n", "df2_nan = pd.isna(df2)\n", "\n", "# Check if NaN positions are consistent\n", "nan_mismatch = (df1_nan != df2_nan) # Simplified this line\n", "\n", "if nan_mismatch.any().any():\n", "    nan_diff_count = nan_mismatch.sum().sum()\n", "    print(f\"NaN position mismatch: {nan_diff_count} differences\")\n", "\n", "    # Find the locations of the mismatches\n", "    mismatch_locations = []\n", "    for col in nan_mismatch.columns:\n", "        for row in nan_mismatch.index:\n", "            if nan_mismatch.loc[row, col]:\n", "                mismatch_locations.append((row, col))\n", "    \n", "    print(\"\\nLocations (row, column) of NaN mismatches:\")\n", "    for loc in mismatch_locations:\n", "        print(loc)\n", "        \n", "    # You can also get a DataFrame showing the mismatch locations directly\n", "    # where True indicates a mismatch in NaN status\n", "    print(\"\\nDataFrame showing NaN mismatch locations (True means mismatch):\")\n", "    print(nan_mismatch)\n", "\n", "else:\n", "    print(\"NaN positions are consistent between the two DataFrames.\")"]}, {"cell_type": "code", "execution_count": 12, "id": "11c710cf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully loaded files:\n", "- /home/<USER>/git/feature_operators/test_right/test_results/cpp/ts_Kurt<PERSON>_v2.csv\n", "- /home/<USER>/git/feature_operators/test_right/test_results/python/ts_Kurtosis.csv\n", "- /home/<USER>/git/cross_selction_strategy/test/operator/test_right/test_data/close.csv\n", "\n", "Differences found in Kurtosis data. Calculating largest difference locations...\n", "all_diff_details: [{'row_position': 267, 'kurtosis_col_position': 454, 'df1_value': 623172.72843, 'df2_value': 590350.8462, 'absolute_diff': 32821.88222999999}, {'row_position': 268, 'kurtosis_col_position': 454, 'df1_value': -90922.785384, 'df2_value': -80236.603715, 'absolute_diff': 10686.181668999998}, {'row_position': 261, 'kurtosis_col_position': 443, 'df1_value': 6436.5272736, 'df2_value': 7078.1139641, 'absolute_diff': 641.5866905000003}, {'row_position': 262, 'kurtosis_col_position': 443, 'df1_value': 6436.5272736, 'df2_value': 7078.1139641, 'absolute_diff': 641.5866905000003}, {'row_position': 463, 'kurtosis_col_position': 80, 'df1_value': 773.93649711, 'df2_value': 1195.9674353, 'absolute_diff': 422.03093819000003}, {'row_position': 129, 'kurtosis_col_position': 0, 'df1_value': 19114.128901, 'df2_value': 18731.395784, 'absolute_diff': 382.73311699999977}, {'row_position': 455, 'kurtosis_col_position': 80, 'df1_value': -2920.2178651, 'df2_value': -3134.666857, 'absolute_diff': 214.44899190000024}, {'row_position': 456, 'kurtosis_col_position': 80, 'df1_value': 2432.2236765, 'df2_value': 2643.8790479, 'absolute_diff': 211.65537140000015}, {'row_position': 462, 'kurtosis_col_position': 80, 'df1_value': 917.61371974, 'df2_value': 711.21252706, 'absolute_diff': 206.40119268}, {'row_position': 254, 'kurtosis_col_position': 202, 'df1_value': 1608.340421, 'df2_value': 1440.4235848, 'absolute_diff': 167.91683620000003}]\n", "\n", "Found the 1 largest differences:\n", "kurtosis_col_pos: 454\n", "\n", "--- Largest Difference #1 ---\n", "**Row Position (in Kurtosis files)**: 267\n", "**Kurtosis Column Position**: 454\n", "**Kurtosis Column Name (for reference)**: '454'\n", "**df1_Kurtosis Value**: 623172.72843\n", "**df2_Kurtosis Value**: 590350.8462\n", "**Absolute Difference**: 32821.8822300000\n", "\n", "**df1_Kurtosis context for column at position 454 around row 267 (30 rows above/below)**:\n", "237        -0.958521\n", "238        -0.836665\n", "239        -0.792544\n", "240        -0.460218\n", "241        -0.303745\n", "242        -0.830039\n", "243        -0.979446\n", "244        -1.195049\n", "245        -1.581113\n", "246        -1.642461\n", "247        -1.663459\n", "248         3.919445\n", "249         0.304787\n", "250        -1.344359\n", "251        -2.078841\n", "252        -2.157674\n", "253        -2.083940\n", "254        -0.875148\n", "255         0.065063\n", "256         2.719429\n", "257         0.157016\n", "258         0.141804\n", "259         0.214708\n", "260         0.230492\n", "261         0.348161\n", "262         0.468212\n", "263         0.638191\n", "264         6.620765\n", "265         9.682077\n", "266        -3.000000\n", "267    623172.728430\n", "268    -90922.785384\n", "269       152.278152\n", "270        13.475329\n", "271        -1.697563\n", "272        -0.122545\n", "273         5.264421\n", "274         1.782601\n", "275        -0.634946\n", "276        -0.732241\n", "277        -0.976197\n", "278        -1.039104\n", "279        -1.199312\n", "280        -1.373273\n", "281         1.699949\n", "282         1.480587\n", "283        -0.200101\n", "284        -0.238374\n", "285        -0.078728\n", "286        -0.444894\n", "287         0.027342\n", "288         0.571304\n", "289         0.688936\n", "290         0.875239\n", "291         2.857031\n", "292         1.620725\n", "293         1.973753\n", "294         0.449983\n", "295         2.137591\n", "296         2.043926\n", "297        -0.127543\n", "Name: 454, dtype: float64\n", "\n", "**df2_Kurtosis context for column at position 454 around row 267 (30 rows above/below)**:\n", "237        -0.958470\n", "238        -0.836623\n", "239        -0.792465\n", "240        -0.460140\n", "241        -0.303793\n", "242        -0.829846\n", "243        -0.979391\n", "244        -1.194844\n", "245        -1.580777\n", "246        -1.642548\n", "247        -1.663410\n", "248         3.919383\n", "249         0.304797\n", "250        -1.344372\n", "251        -2.078851\n", "252        -2.157693\n", "253        -2.083927\n", "254        -0.875152\n", "255         0.065069\n", "256         2.719457\n", "257         0.157185\n", "258         0.141804\n", "259         0.214902\n", "260         0.230605\n", "261         0.348161\n", "262         0.468090\n", "263         0.638298\n", "264         6.620293\n", "265         9.677256\n", "266        -3.000000\n", "267    590350.846200\n", "268    -80236.603715\n", "269       155.402844\n", "270        12.275855\n", "271        -1.502861\n", "272        -0.125284\n", "273         5.264983\n", "274         1.782046\n", "275        -0.635566\n", "276        -0.731634\n", "277        -0.976393\n", "278        -1.038335\n", "279        -1.199592\n", "280        -1.373091\n", "281         1.699961\n", "282         1.480600\n", "283        -0.200138\n", "284        -0.238349\n", "285        -0.078609\n", "286        -0.444808\n", "287         0.027208\n", "288         0.571367\n", "289         0.688811\n", "290         0.875048\n", "291         2.857210\n", "292         1.621106\n", "293         1.973040\n", "294         0.449610\n", "295         2.137504\n", "296         2.043941\n", "297        -0.127642\n", "Name: 454, dtype: float64\n", "\n", "**'close.csv' context for column at position 0 around row 267 (30 rows above/below)**:\n", "237    0.41219\n", "238    0.41285\n", "239    0.41276\n", "240    0.41285\n", "241    0.41285\n", "242    0.41240\n", "243    0.41293\n", "244    0.41228\n", "245    0.41243\n", "246    0.41255\n", "247    0.41252\n", "248    0.41379\n", "249    0.41391\n", "250    0.41371\n", "251    0.41363\n", "252    0.41363\n", "253    0.41395\n", "254    0.41458\n", "255    0.41458\n", "256    0.41406\n", "257    0.41375\n", "258    0.41375\n", "259    0.41375\n", "260    0.41375\n", "261    0.41375\n", "262    0.41375\n", "263    0.41375\n", "264    0.41375\n", "265    0.41375\n", "266    0.41375\n", "267    0.41374\n", "268    0.41374\n", "269    0.41366\n", "270    0.41364\n", "271    0.41357\n", "272    0.41349\n", "273    0.41423\n", "274    0.41401\n", "275    0.41419\n", "276    0.41358\n", "277    0.41386\n", "278    0.41381\n", "279    0.41345\n", "280    0.41343\n", "281    0.41264\n", "282    0.41343\n", "283    0.41274\n", "284    0.41319\n", "285    0.41335\n", "286    0.41391\n", "287    0.41343\n", "288    0.41319\n", "289    0.41320\n", "290    0.41334\n", "291    0.41322\n", "292    0.41363\n", "293    0.41343\n", "294    0.41364\n", "295    0.41256\n", "296    0.41303\n", "297    0.41248\n", "Name: GOMININGUSDT, dtype: float64\n"]}], "source": ["pd.set_option('display.max_rows', 100)\n", "\n", "\n", "# --- File Paths ---\n", "file1_kurtosis = \"/home/<USER>/git/feature_operators/test_right/test_results/cpp/ts_<PERSON>_v2.csv\"\n", "file2_kurtosis = \"/home/<USER>/git/feature_operators/test_right/test_results/python/ts_Kurtosis.csv\"\n", "file_close = \"/home/<USER>/git/cross_selction_strategy/test/operator/test_right/test_data/close.csv\"\n", "\n", "# --- Parameters ---\n", "n_diffs_to_find = 1  # Number of largest differences to find\n", "n_context_rows = 30   # Number of rows above and below the difference to display\n", "\n", "# IMPORTANT: Specify the INTEGER POSITION of the column you want from your close.csv\n", "# For example: 0 for the first data column (after the index), 1 for the second, etc.\n", "close_column_position = 0 # <--- **Set this to the correct integer position (0-indexed)**\n", "\n", "# --- Load Data ---\n", "try:\n", "    # df1_kurtosis and df2_kurtosis are loaded without index_col, so they get a default integer index (0, 1, 2...)\n", "    df1_kurtosis = df1\n", "    df2_kurtosis = df2\n", "    # Load close.csv, ensuring index_col=0 as specified.\n", "    # Its rows will align positionally with the Kurtosis DFs if they have the same number of rows.\n", "    # df_close = pd.read_csv(file_close, index_col=0)\n", "    print(f\"Successfully loaded files:\\n- {file1_kurtosis}\\n- {file2_kurtosis}\\n- {file_close}\")\n", "except FileNotFoundError as e:\n", "    print(f\"Error: One or more files not found. Please check the paths.\\n{e}\")\n", "    exit()\n", "except Exception as e:\n", "    print(f\"An error occurred while loading CSV files:\\n{e}\")\n", "    exit()\n", "\n", "# --- Validate close_column_position ---\n", "if not (0 <= close_column_position < df_close.shape[1]):\n", "    print(f\"Error: Specified close_column_position ({close_column_position}) is out of bounds for df_close, which has {df_close.shape[1]} data columns.\")\n", "    exit()\n", "\n", "# Get the name of the column at the specified position in df_close for display purposes\n", "\n", "# --- Find Differences in Kurtosis Data ---\n", "# Use compare to find differences. It returns a MultiIndex DataFrame.\n", "diff_df_kurtosis = df1_kurtosis.compare(df2_kurtosis)\n", "\n", "if diff_df_kurtosis.empty:\n", "    print(\"\\nNo differences found between the two Kurtosis DataFrames.\")\n", "else:\n", "    print(\"\\nDifferences found in Kurtosis data. Calculating largest difference locations...\")\n", "\n", "    # --- Prepare a list to store difference details ---\n", "    all_diff_details = []\n", "\n", "    # Iterate through the columns of the *original* df1_kurtosis to get their integer positions.\n", "    for col_pos in range(df1_kurtosis.shape[1]): # Iterate through column positions (0, 1, 2, ...)\n", "        # Get the actual column name from df1_kurtosis for clarity in output and for diff_df_kurtosis lookup\n", "        original_kurtosis_col_name = df1_kurtosis.columns[col_pos]\n", "\n", "        # Check if both 'self' and 'other' parts of this column exist in diff_df_kurtosis\n", "        if (original_kurtosis_col_name, 'self') in diff_df_kurtosis.columns and \\\n", "           (original_kurtosis_col_name, 'other') in diff_df_kurtosis.columns:\n", "\n", "            # Get the series of differences for this column, dropping NaNs (where there's no actual difference)\n", "            diff_series = diff_df_kurtosis[(original_kurtosis_col_name, 'self')].dropna() - \\\n", "                          diff_df_kurtosis[(original_kurtosis_col_name, 'other')].dropna()\n", "\n", "            # Iterate through each difference found in this column\n", "            for row_pos, diff_val in diff_series.items(): # row_pos here is the integer row index\n", "                all_diff_details.append({\n", "                    'row_position': row_pos,\n", "                    'kurtosis_col_position': col_pos, # Store the integer position of the column\n", "                    'df1_value': df1_kurtosis.iloc[row_pos, col_pos],\n", "                    'df2_value': df2_kurtosis.iloc[row_pos, col_pos],\n", "                    'absolute_diff': abs(diff_val)\n", "                })\n", "\n", "    # --- Sort by absolute difference in descending order ---\n", "    all_diff_details.sort(key=lambda x: x['absolute_diff'], reverse=True)\n", "    print(\"all_diff_details:\",all_diff_details[0:10])\n", "\n", "    # --- Print largest differences and their column context, including close.csv specific column context ---\n", "    print(f\"\\nFound the {min(n_diffs_to_find, len(all_diff_details))} largest differences:\")\n", "    for i in range(min(n_diffs_to_find, len(all_diff_details))):\n", "        diff_info = all_diff_details[i]\n", "        row_pos = diff_info['row_position'] # The numerical row position (0, 1, 2, ...)\n", "        kurtosis_col_pos = diff_info['kurtosis_col_position'] # The integer position of the column\n", "        print(\"kurtosis_col_pos:\",kurtosis_col_pos)\n", "\n", "        # Get the actual column name from df1_kurtosis using its position for display purposes\n", "        kurtosis_col_display_name = df1_kurtosis.columns[kurtosis_col_pos]\n", "\n", "        # Determine context start and end rows (using positional indices)\n", "        start_row = max(0, row_pos - n_context_rows)\n", "        end_row = min(len(df1_kurtosis) - 1, row_pos + n_context_rows) # Ensure end_row is within bounds\n", "\n", "        # Extract context data for the specific kurtosis column using .iloc for both rows and column\n", "        # Note: slicing with iloc[start:end+1] includes the 'end' index\n", "        df1_kurtosis_column_context = df1_kurtosis.iloc[start_row : end_row + 1, kurtosis_col_pos]\n", "        df2_kurtosis_column_context = df2_kurtosis.iloc[start_row : end_row + 1, kurtosis_col_pos]\n", "\n", "        # --- Extract context from df_close for the specified column position ---\n", "        try:\n", "            # .iloc[row_start : row_end + 1, column_position] for both row and column positional slicing\n", "            df_close_specific_column_context = df_close.iloc[start_row : end_row + 1, kurtosis_col_pos]\n", "        except IndexError:\n", "            # This handles cases where start_row/end_row or close_column_position might be out of bounds for df_close\n", "            print(f\"\\nWarning: Data access out of bounds for 'close.csv' for difference at row {row_pos} (column position {close_column_position}). Skipping context for this file.\")\n", "            df_close_specific_column_context = \"N/A (Access out of bounds in close.csv)\"\n", "\n", "        print(f\"\\n--- Largest Difference #{i+1} ---\")\n", "        print(f\"**Row Position (in Kurtosis files)**: {row_pos}\")\n", "        print(f\"**Kurtosis Column Position**: {kurtosis_col_pos}\")\n", "        print(f\"**Kurtosis Column Name (for reference)**: '{kurtosis_col_display_name}'\") # Show the actual name if available\n", "        print(f\"**df1_Kurtosis Value**: {diff_info['df1_value']}\")\n", "        print(f\"**df2_Kurtosis Value**: {diff_info['df2_value']}\")\n", "        print(f\"**Absolute Difference**: {diff_info['absolute_diff']:.10f}\") # Format to show more decimal places\n", "\n", "        print(f\"\\n**df1_Kurtosis context for column at position {kurtosis_col_pos} around row {row_pos} ({n_context_rows} rows above/below)**:\")\n", "        print(df1_kurtosis_column_context)\n", "\n", "        print(f\"\\n**df2_Kurtosis context for column at position {kurtosis_col_pos} around row {row_pos} ({n_context_rows} rows above/below)**:\")\n", "        print(df2_kurtosis_column_context)\n", "\n", "        print(f\"\\n**'close.csv' context for column at position {close_column_position} around row {row_pos} ({n_context_rows} rows above/below)**:\")\n", "        print(df_close_specific_column_context)"]}, {"cell_type": "code", "execution_count": 89, "id": "b40b3d9e", "metadata": {}, "outputs": [{"data": {"text/plain": ["0           NaN\n", "1           NaN\n", "2           NaN\n", "3      0.982010\n", "4      0.874415\n", "         ...   \n", "495   -1.993881\n", "496   -2.140250\n", "497   -1.465946\n", "498    0.119675\n", "499    1.249103\n", "Name: 129, Length: 500, dtype: float64"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["df1[129]"]}, {"cell_type": "code", "execution_count": 90, "id": "0a9da518", "metadata": {}, "outputs": [{"data": {"text/plain": ["0               NaN\n", "1               NaN\n", "2               NaN\n", "3      0.000000e+00\n", "4      0.000000e+00\n", "           ...     \n", "495    0.000000e+00\n", "496   -1.000000e-10\n", "497    0.000000e+00\n", "498    0.000000e+00\n", "499    0.000000e+00\n", "Name: 47, Length: 500, dtype: float64"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["df2[47] - df1[47]"]}, {"cell_type": "code", "execution_count": null, "id": "57245878", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 91, "id": "e0a5664a", "metadata": {}, "outputs": [], "source": ["close = pd.read_csv(\"/home/<USER>/git/cross_selction_strategy/test/operator/test_right/test_data/close.csv\",index_col=0)\n", "open = pd.read_csv(\"/home/<USER>/git/cross_selction_strategy/test/operator/test_right/test_data/open.csv\",index_col=0)\n", "volume = pd.read_csv(\"/home/<USER>/git/cross_selction_strategy/test/operator/test_right/test_data/volume.csv\",index_col=0)\n", "volume = volume.reset_index(drop=True)\n", "open = open.reset_index(drop=True)\n", "close = close.reset_index(drop=True)\n", "# close.iloc[:, 17].to_csv(\"c.csv\")"]}, {"cell_type": "code", "execution_count": 92, "id": "e0ff1c30", "metadata": {}, "outputs": [], "source": ["def ts_Skewness(s1, n):\n", "    n = int(n)\n", "    if n <= 2:\n", "        n = 3\n", "    tem = s1.copy()\n", "    tem1 = tem.rolling(n, axis=0, min_periods=1).skew()\n", "    return tem1"]}, {"cell_type": "code", "execution_count": 93, "id": "17faec9a", "metadata": {}, "outputs": [{"data": {"text/plain": ["0           NaN\n", "1           NaN\n", "2      1.152070\n", "3      1.002408\n", "4      1.089884\n", "         ...   \n", "495    0.144393\n", "496   -0.234033\n", "497   -0.729575\n", "498   -1.141057\n", "499   -0.868235\n", "Name: VETUSDT, Length: 500, dtype: float64"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["res = ts_Skewness(close.iloc[:,129], 10)\n", "res"]}, {"cell_type": "code", "execution_count": 94, "id": "1840c852", "metadata": {}, "outputs": [{"data": {"text/plain": ["0     0.16545\n", "1     0.16560\n", "2     0.16560\n", "3     0.16554\n", "4     0.16554\n", "5     0.16554\n", "6     0.16554\n", "7     0.16511\n", "8     0.16511\n", "9     0.16494\n", "10    0.16494\n", "11    0.16494\n", "12    0.16494\n", "13    0.16494\n", "14    0.16494\n", "15    0.16494\n", "16    0.16494\n", "17    0.16494\n", "18    0.16494\n", "19    0.16494\n", "Name: SERAPHUSDT, dtype: float64"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["close.iloc[0:20,0]"]}, {"cell_type": "code", "execution_count": 95, "id": "0442f3a5", "metadata": {}, "outputs": [{"ename": "IndexingError", "evalue": "Too many indexers", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mIndexingError\u001b[0m                             Traceback (most recent call last)", "Cell \u001b[0;32mIn[95], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m df_list \u001b[38;5;241m=\u001b[39m [close, volume,res,df1]  \n\u001b[0;32m----> 2\u001b[0m result \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mconcat([df\u001b[38;5;241m.\u001b[39miloc[:, \u001b[38;5;241m0\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m df \u001b[38;5;129;01min\u001b[39;00m df_list], axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m)\n\u001b[1;32m      3\u001b[0m result\u001b[38;5;241m.\u001b[39mto_csv(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtest.csv\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[0;32mIn[95], line 2\u001b[0m, in \u001b[0;36m<listcomp>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m      1\u001b[0m df_list \u001b[38;5;241m=\u001b[39m [close, volume,res,df1]  \n\u001b[0;32m----> 2\u001b[0m result \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mconcat([\u001b[43mdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43miloc\u001b[49m\u001b[43m[\u001b[49m\u001b[43m:\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m df \u001b[38;5;129;01min\u001b[39;00m df_list], axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m)\n\u001b[1;32m      3\u001b[0m result\u001b[38;5;241m.\u001b[39mto_csv(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtest.csv\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/pandas/core/indexing.py:1068\u001b[0m, in \u001b[0;36m_LocationIndexer.__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   1066\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_scalar_access(key):\n\u001b[1;32m   1067\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_get_value(\u001b[38;5;241m*\u001b[39mkey, takeable\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_takeable)\n\u001b[0;32m-> 1068\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_getitem_tuple\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1069\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1070\u001b[0m     \u001b[38;5;66;03m# we by definition only have the 0th axis\u001b[39;00m\n\u001b[1;32m   1071\u001b[0m     axis \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxis \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;241m0\u001b[39m\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/pandas/core/indexing.py:1564\u001b[0m, in \u001b[0;36m_iLocIndexer._getitem_tuple\u001b[0;34m(self, tup)\u001b[0m\n\u001b[1;32m   1562\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_getitem_tuple\u001b[39m(\u001b[38;5;28mself\u001b[39m, tup: \u001b[38;5;28mtuple\u001b[39m):\n\u001b[0;32m-> 1564\u001b[0m     tup \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_tuple_indexer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtup\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1565\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m suppress(IndexingError):\n\u001b[1;32m   1566\u001b[0m         \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_lowerdim(tup)\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/pandas/core/indexing.py:870\u001b[0m, in \u001b[0;36m_LocationIndexer._validate_tuple_indexer\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m    865\u001b[0m \u001b[38;5;129m@final\u001b[39m\n\u001b[1;32m    866\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_validate_tuple_indexer\u001b[39m(\u001b[38;5;28mself\u001b[39m, key: \u001b[38;5;28mtuple\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mtuple\u001b[39m:\n\u001b[1;32m    867\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    868\u001b[0m \u001b[38;5;124;03m    Check the key for valid keys across my indexer.\u001b[39;00m\n\u001b[1;32m    869\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 870\u001b[0m     key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_key_length\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    871\u001b[0m     key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_expand_ellipsis(key)\n\u001b[1;32m    872\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m i, k \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(key):\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/pandas/core/indexing.py:909\u001b[0m, in \u001b[0;36m_LocationIndexer._validate_key_length\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m    907\u001b[0m             \u001b[38;5;28;01mraise\u001b[39;00m IndexingError(_one_ellipsis_message)\n\u001b[1;32m    908\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_key_length(key)\n\u001b[0;32m--> 909\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m IndexingError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mToo many indexers\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    910\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m key\n", "\u001b[0;31mIndexingError\u001b[0m: Too many indexers"]}], "source": ["df_list = [close, volume,res,df1]  \n", "result = pd.concat([df.iloc[:, 0] for df in df_list], axis=1)\n", "result.to_csv(\"test.csv\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "46fb0614", "metadata": {}, "outputs": [], "source": ["  pn_TransNorm (cpp_vs_py): 最大差异 5.21e+00，平均差异 1.69e+00，中位数差异 1.03e+00，形状 (500, 518)，共 259000 个元素\n", "  ts_Corr (cpp_vs_py): 最大差异 9.92e-05，平均差异 3.57e-09，中位数差异 1.00e-11，形状 (500, 518)，共 259000 个元素\n", "  ts_Entropy (cpp_vs_py): 最大差异 4.19e-02，平均差异 4.74e-05，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素\n", "  ts_Kurtosis (cpp_vs_py): 最大差异 1.70e+05，平均差异 8.12e-01，中位数差异 1.32e-05，形状 (500, 518)，共 259000 个元素\n", "  ts_Partial_corr (cpp_vs_py): 最大差异 4.30e+00，平均差异 7.71e-05，中位数差异 3.40e-10，形状 (500, 518)，共 259000 个元素\n", "  ts_Regression_A (cpp_vs_py): 最大差异 7.23e-02，平均差异 1.13e-06，中位数差异 2.30e-10，形状 (500, 518)，共 259000 个元素\n", "  ts_Regression_B (cpp_vs_py): 最大差异 2.32e+02，平均差异 3.61e-03，中位数差异 3.90e-11，形状 (500, 518)，共 259000 个元素\n", "  ts_Regression_C (cpp_vs_py): 最大差异 5.79e-04，平均差异 5.95e-09，中位数差异 0.00e+00，形状 (500, 518)，共 259000 个元素\n", "  ts_Regression_D (cpp_vs_py): 最大差异 5.79e-04，平均差异 6.27e-09，中位数差异 4.00e-14，形状 (500, 518)，共 259000 个元素\n", "  ts_Skewness (cpp_vs_py): 最大差异 6.92e+02，平均差异 1.31e-01，中位数差异 1.03e-07，形状 (500, 518)，共 259000 个元素\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}