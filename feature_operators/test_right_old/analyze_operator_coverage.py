#!/usr/bin/env python3
import os
import re
import sys
import inspect

# 添加 feature_operator_funcs.py 的路径
sys.path.append('./')
from feature_operator_funcs import *

def extract_cpp_operators(header_file):
    """从C++头文件中提取算子函数名"""
    operators = []

    with open(header_file, 'r') as f:
        content = f.read()

    # 使用正则表达式匹配函数声明
    # 匹配形式如: DataFrame function_name(const DataFrame &s1, ...);
    pattern = r'DataFrame\s+([a-zA-Z0-9_]+)\s*\('
    matches = re.finditer(pattern, content)

    for match in matches:
        func_name = match.group(1)
        # 排除以下划线开头的辅助函数
        if not func_name.startswith('_'):
            operators.append(func_name)

    return operators

def extract_python_operators():
    """从Python模块中提取算子函数"""
    operators = []

    # 获取模块中所有函数
    module_functions = inspect.getmembers(sys.modules['feature_operator_funcs'], inspect.isfunction)

    for name, func in module_functions:
        # 排除以下划线开头的辅助函数和内部函数
        if not name.startswith('_') and not name.startswith('Util_'):
            operators.append(name)

    return operators

def main():
    # 定义路径
    base_dir = "/home/<USER>/git/feature_operators"
    # 当前项目使用模块化头文件结构
    header_files = [
        f"{base_dir}/include/feature_operators/core_math.hpp",
        f"{base_dir}/include/feature_operators/logical_ops.hpp",
        f"{base_dir}/include/feature_operators/comparison_ops.hpp",
        f"{base_dir}/include/feature_operators/data_utils.hpp",
        f"{base_dir}/include/feature_operators/reduction_ops.hpp",
        f"{base_dir}/include/feature_operators/panel_ops.hpp",
        f"{base_dir}/include/feature_operators/timeseries_ops.hpp",
        f"{base_dir}/include/feature_operators/group_ops.hpp"
    ]

    # 提取C++算子 - 从所有头文件中提取
    all_cpp_ops = []
    for header_file in header_files:
        if os.path.exists(header_file):
            ops = extract_cpp_operators(header_file)
            all_cpp_ops.extend(ops)

    # 提取Python算子
    python_ops = extract_python_operators()

    # 排序算子列表
    all_cpp_ops = sorted(set(all_cpp_ops))  # 去重并排序
    python_ops.sort()

    # 分析覆盖情况
    in_cpp_not_python = set(all_cpp_ops) - set(python_ops)
    in_python_not_cpp = set(python_ops) - set(all_cpp_ops)

    # 打印结果
    print(f"C++算子总数: {len(all_cpp_ops)}")
    print(f"Python算子总数: {len(python_ops)}")
    print("\n")

    print("算子覆盖分析:")
    print("=" * 80)

    print(f"\n在C++中但不在Python中的算子 ({len(in_cpp_not_python)}):")
    for op in sorted(in_cpp_not_python):
        print(f"  - {op}")

    print(f"\n在Python中但不在C++中的算子 ({len(in_python_not_cpp)}):")
    for op in sorted(in_python_not_cpp):
        print(f"  - {op}")

    # 检查命名不一致的情况
    print("\n可能命名不一致的算子:")
    print("=" * 80)

    # 检查ts_和Tot_系列函数
    ts_funcs_py = [f for f in python_ops if f.startswith('ts_')]
    ts_funcs_cpp = [f for f in all_cpp_ops if f.startswith('ts_')]

    tot_funcs_py = [f for f in python_ops if f.startswith('Tot_')]
    tot_funcs_cpp = [f for f in all_cpp_ops if f.startswith('Tot_')]

    print(f"\nts_系列函数:")
    print(f"  Python: {len(ts_funcs_py)} 个")
    print(f"  C++: {len(ts_funcs_cpp)} 个")

    print(f"\nTot_系列函数:")
    print(f"  Python: {len(tot_funcs_py)} 个")
    print(f"  C++: {len(tot_funcs_cpp)} 个")

    # 保存结果到文件
    output_file = "operator_coverage_analysis.txt"
    with open(output_file, "w") as f:
        f.write(f"C++算子总数: {len(all_cpp_ops)}\n")
        f.write(f"Python算子总数: {len(python_ops)}\n\n")

        f.write("算子覆盖分析:\n")
        f.write("=" * 80 + "\n")

        f.write(f"\n在C++中但不在Python中的算子 ({len(in_cpp_not_python)}):\n")
        for op in sorted(in_cpp_not_python):
            f.write(f"  - {op}\n")

        f.write(f"\n在Python中但不在C++中的算子 ({len(in_python_not_cpp)}):\n")
        for op in sorted(in_python_not_cpp):
            f.write(f"  - {op}\n")

        f.write("\n可能命名不一致的算子:\n")
        f.write("=" * 80 + "\n")

        f.write(f"\nts_系列函数:\n")
        f.write(f"  Python: {len(ts_funcs_py)} 个\n")
        f.write(f"  C++: {len(ts_funcs_cpp)} 个\n")

        f.write(f"\nTot_系列函数:\n")
        f.write(f"  Python: {len(tot_funcs_py)} 个\n")
        f.write(f"  C++: {len(tot_funcs_cpp)} 个\n")

    print(f"\n分析结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
