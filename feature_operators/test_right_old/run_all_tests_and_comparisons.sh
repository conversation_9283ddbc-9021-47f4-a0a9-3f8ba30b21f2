#!/bin/bash

# 运行所有测试和比较的脚本
# 包括：编译、运行测试、生成结果、比较正确性和性能

set -e  # 遇到错误立即退出

echo "=========================================="
echo "开始运行完整的测试和比较流程"
echo "=========================================="

# 设置路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/build"
TEST_RESULTS_DIR="$SCRIPT_DIR/test_results"

echo "项目根目录: $PROJECT_ROOT"
echo "构建目录: $BUILD_DIR"
echo "测试结果目录: $TEST_RESULTS_DIR"

# 创建测试结果目录
mkdir -p "$TEST_RESULTS_DIR/cpp"
mkdir -p "$TEST_RESULTS_DIR/python"

echo ""
echo "=========================================="
echo "步骤 1: 编译项目"
echo "=========================================="

cd "$PROJECT_ROOT"

# 检查是否存在构建目录
if [ ! -d "$BUILD_DIR" ]; then
    echo "创建构建目录..."
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    cmake ..
else
    cd "$BUILD_DIR"
fi

# 编译项目
echo "编译项目..."
make -j4

if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

echo "编译成功！"

echo ""
echo "=========================================="
echo "步骤 2: 编译测试程序"
echo "=========================================="

cd "$SCRIPT_DIR"

# 编译测试程序
echo "编译 C++ 测试程序..."
g++ -std=c++17 -I ../include -I /usr/local/include/eigen3 \
    test_optimized_operators.cpp ../build/libfeature_ops_lib.a \
    -o test_optimized_operators

if [ $? -ne 0 ]; then
    echo "编译测试程序失败！"
    exit 1
fi

# 编译基准测试程序
echo "编译 C++ 基准测试程序..."
g++ -std=c++17 -I ../include -I /usr/local/include/eigen3 \
    benchmark_cpp_operators.cpp ../build/libfeature_ops_lib.a \
    -o benchmark_cpp_operators

if [ $? -ne 0 ]; then
    echo "编译基准测试程序失败！"
    exit 1
fi

echo "测试程序编译成功！"

echo ""
echo "=========================================="
echo "步骤 3: 运行 C++ 测试生成结果"
echo "=========================================="

echo "运行 C++ 测试程序..."
./test_optimized_operators

if [ $? -ne 0 ]; then
    echo "C++ 测试运行失败！"
    exit 1
fi

echo "C++ 测试完成！"

echo ""
echo "=========================================="
echo "步骤 4: 运行 Python 测试生成参考结果"
echo "=========================================="

echo "运行 Python 测试程序..."
cd "$PROJECT_ROOT"
python3 test_right/test_python_operators.py

if [ $? -ne 0 ]; then
    echo "Python 测试运行失败！"
    exit 1
fi

echo "Python 测试完成！"

echo ""
echo "=========================================="
echo "步骤 5: 运行性能基准测试"
echo "=========================================="

cd "$SCRIPT_DIR"
echo "运行 C++ 性能基准测试..."
./benchmark_cpp_operators

if [ $? -ne 0 ]; then
    echo "性能基准测试运行失败！"
    exit 1
fi

echo "性能基准测试完成！"

echo ""
echo "=========================================="
echo "步骤 6: 比较正确性 (C++ v2 vs Python)"
echo "=========================================="

echo "比较 C++ v2 版本和 Python 版本的正确性..."
python3 compare_v1_v2_operators.py

if [ $? -ne 0 ]; then
    echo "正确性比较失败！"
    exit 1
fi

echo "正确性比较完成！"

echo ""
echo "=========================================="
echo "步骤 7: 比较性能 (C++ v1 vs v2)"
echo "=========================================="

echo "比较 C++ 原始版本和 v2 版本的性能..."
python3 compare_cpp_v1_v2_performance.py

if [ $? -ne 0 ]; then
    echo "性能比较失败！"
    exit 1
fi

echo "性能比较完成！"

echo ""
echo "=========================================="
echo "测试和比较流程全部完成！"
echo "=========================================="

echo ""
echo "结果文件位置："
echo "  C++ 测试结果: $TEST_RESULTS_DIR/cpp/"
echo "  Python 测试结果: $TEST_RESULTS_DIR/python/"
echo "  正确性比较结果: $TEST_RESULTS_DIR/cpp/v2_vs_python_comparison_results.json"
echo "  性能比较结果: $TEST_RESULTS_DIR/cpp/v1_vs_v2_performance_comparison.json"
echo "  性能基准数据: $TEST_RESULTS_DIR/cpp/benchmark_cpp_results.json"

echo ""
echo "可以查看以下文件了解详细结果："
echo "  cat $TEST_RESULTS_DIR/cpp/v2_vs_python_comparison_results.json"
echo "  cat $TEST_RESULTS_DIR/cpp/v1_vs_v2_performance_comparison.json"

echo ""
echo "全部完成！"
