#!/usr/bin/env python3
"""
简单的ts_Corr测试脚本
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_test_data():
    """加载测试数据"""
    try:
        close_df = pd.read_csv('sample_close.csv', index_col=0)
        volume_df = pd.read_csv('sample_volume.csv', index_col=0)
        print(f"数据加载成功: close {close_df.shape}, volume {volume_df.shape}")
        return close_df, volume_df
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None, None

def manual_ts_corr(s1, s2, n):
    """手动实现ts_Corr来验证算法"""
    n = int(n)
    if n <= 1:
        n = 1
    
    # 复制数据
    tem1 = s1.copy()
    tem2 = s2.copy()
    
    # NaN传播
    tem1[tem2.isna()] = np.nan
    tem2[tem1.isna()] = np.nan
    
    # 计算滚动统计量
    tem1_m = tem1.rolling(n, axis=0, min_periods=1).mean()
    tem2_m = tem2.rolling(n, axis=0, min_periods=1).mean()
    tem_prod_m = (tem1 * tem2).rolling(n, axis=0, min_periods=1).mean()
    tem1_std = tem1.rolling(n, axis=0, min_periods=1).std(ddof=0)
    tem2_std = tem2.rolling(n, axis=0, min_periods=1).std(ddof=0)
    
    # 计算相关系数
    res = (tem_prod_m - tem1_m * tem2_m) / (tem1_std * tem2_std)
    return res.replace([-np.inf, np.inf], np.nan)

def test_simple_case():
    """测试简单案例"""
    print("=== 测试简单案例 ===")
    
    # 创建简单的测试数据
    data1 = pd.DataFrame([1, 2, 3, 4, 5], columns=['A'])
    data2 = pd.DataFrame([2, 4, 6, 8, 10], columns=['A'])
    
    print("测试数据:")
    print(f"data1:\n{data1}")
    print(f"data2:\n{data2}")
    
    # 测试不同窗口大小
    for n in [1, 2, 3, 5]:
        result = manual_ts_corr(data1, data2, n)
        print(f"\nn={n}的结果:")
        print(result)

def test_with_sample_data():
    """使用样本数据测试"""
    print("\n=== 使用样本数据测试 ===")
    
    close_df, volume_df = load_test_data()
    if close_df is None or volume_df is None:
        print("无法加载样本数据")
        return
    
    print(f"Close数据前5行:\n{close_df.head()}")
    print(f"Volume数据前5行:\n{volume_df.head()}")
    
    # 测试ts_Corr
    for n in [5, 10]:
        print(f"\n测试窗口大小 n={n}")
        result = manual_ts_corr(close_df, volume_df, n)
        print(f"结果形状: {result.shape}")
        print(f"前10行结果:\n{result.head(10)}")
        print(f"NaN数量: {result.isna().sum().sum()}")
        
        # 检查有效值
        valid_values = result.dropna()
        if len(valid_values) > 0:
            print(f"有效值数量: {len(valid_values)}")
            print(f"有效值范围: [{valid_values.min().min():.6f}, {valid_values.max().max():.6f}]")

def compare_results():
    """比较Python和C++结果"""
    print("\n=== 比较Python和C++结果 ===")
    
    try:
        # 读取已有的结果文件
        python_file = 'test_results/python/ts_Corr.csv'
        cpp_file = 'test_results/cpp/ts_Corr_v2.csv'
        
        if os.path.exists(python_file) and os.path.exists(cpp_file):
            python_result = pd.read_csv(python_file, index_col=0)
            cpp_result = pd.read_csv(cpp_file, index_col=0)
            
            print(f"Python结果形状: {python_result.shape}")
            print(f"C++结果形状: {cpp_result.shape}")
            
            # 计算差异
            diff = python_result - cpp_result
            abs_diff = np.abs(diff)
            
            print(f"最大绝对差异: {abs_diff.max().max():.10f}")
            print(f"平均绝对差异: {abs_diff.mean().mean():.10f}")
            print(f"显著差异数量 (>1e-8): {(abs_diff > 1e-8).sum().sum()}")
            
            # 显示前几行比较
            print("\n前5行详细比较:")
            for i in range(min(5, len(python_result))):
                py_val = python_result.iloc[i, 0]
                cpp_val = cpp_result.iloc[i, 0]
                diff_val = diff.iloc[i, 0]
                print(f"Row {i}: Python={py_val:.8f}, C++={cpp_val:.8f}, Diff={diff_val:.8f}")
        else:
            print(f"结果文件不存在: {python_file} 或 {cpp_file}")
    except Exception as e:
        print(f"比较结果时出错: {e}")

def main():
    """主函数"""
    print("=== ts_Corr 简单测试 ===")
    
    # 测试简单案例
    test_simple_case()
    
    # 测试样本数据
    test_with_sample_data()
    
    # 比较结果
    compare_results()

if __name__ == "__main__":
    main()
