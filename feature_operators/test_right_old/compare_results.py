#!/usr/bin/env python3
"""
比较Python和C++的ts_Corr结果
"""

import re
import numpy as np

def parse_results(filename):
    """解析结果文件"""
    results = []
    with open(filename, 'r') as f:
        lines = f.readlines()
    
    for line in lines:
        if line.startswith('Row'):
            if 'NaN' in line:
                results.append(np.nan)
            else:
                # 提取数值
                match = re.search(r'Row \d+: ([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)', line)
                if match:
                    results.append(float(match.group(1)))
    
    return np.array(results)

def main():
    print("=== ts_Corr Python vs C++ 结果比较 ===")
    
    # 解析结果
    python_results = parse_results('python_result.txt')
    cpp_results = parse_results('cpp_result.txt')
    
    print(f"Python结果数量: {len(python_results)}")
    print(f"C++结果数量: {len(cpp_results)}")
    
    if len(python_results) != len(cpp_results):
        print("错误：结果数量不匹配")
        return
    
    # 计算差异
    diff = python_results - cpp_results
    abs_diff = np.abs(diff)
    
    # 忽略NaN的比较
    valid_mask = ~(np.isnan(python_results) | np.isnan(cpp_results))
    valid_diff = diff[valid_mask]
    valid_abs_diff = abs_diff[valid_mask]
    
    print(f"\n差异统计:")
    print(f"有效比较数量: {len(valid_diff)}")
    print(f"最大绝对差异: {np.max(valid_abs_diff):.2e}")
    print(f"平均绝对差异: {np.mean(valid_abs_diff):.2e}")
    print(f"标准差: {np.std(valid_diff):.2e}")
    
    # 找出最大差异的位置
    if len(valid_abs_diff) > 0:
        max_diff_idx = np.argmax(valid_abs_diff)
        # 找到在原数组中的位置
        valid_indices = np.where(valid_mask)[0]
        original_idx = valid_indices[max_diff_idx]
        
        print(f"\n最大差异位置:")
        print(f"行 {original_idx}: Python={python_results[original_idx]:.15f}, C++={cpp_results[original_idx]:.15f}")
        print(f"差异: {diff[original_idx]:.2e}")
    
    # 分析差异分布
    print(f"\n差异分布:")
    thresholds = [1e-15, 1e-12, 1e-10, 1e-8, 1e-6]
    for threshold in thresholds:
        count = np.sum(valid_abs_diff > threshold)
        print(f"差异 > {threshold:.0e}: {count} 个 ({count/len(valid_diff)*100:.1f}%)")
    
    # 详细比较前几行
    print(f"\n前10行详细比较:")
    for i in range(min(10, len(python_results))):
        py_val = python_results[i]
        cpp_val = cpp_results[i]
        
        if np.isnan(py_val) and np.isnan(cpp_val):
            print(f"Row {i}: 都是NaN ✓")
        elif np.isnan(py_val) or np.isnan(cpp_val):
            print(f"Row {i}: NaN不匹配 ✗ (Python: {py_val}, C++: {cpp_val})")
        else:
            diff_val = py_val - cpp_val
            print(f"Row {i}: Python={py_val:.15f}, C++={cpp_val:.15f}, Diff={diff_val:.2e}")
    
    # 检查特殊值
    print(f"\n特殊值检查:")
    
    # 检查接近-1的值
    py_near_neg1 = np.abs(python_results + 1.0) < 1e-10
    cpp_near_neg1 = np.abs(cpp_results + 1.0) < 1e-10
    
    print(f"Python接近-1.0的数量: {np.sum(py_near_neg1)}")
    print(f"C++接近-1.0的数量: {np.sum(cpp_near_neg1)}")
    
    if np.sum(py_near_neg1) > 0:
        py_neg1_idx = np.where(py_near_neg1)[0][0]
        print(f"Python第一个接近-1.0的值 (行{py_neg1_idx}): {python_results[py_neg1_idx]:.15f}")
    
    if np.sum(cpp_near_neg1) > 0:
        cpp_neg1_idx = np.where(cpp_near_neg1)[0][0]
        print(f"C++第一个接近-1.0的值 (行{cpp_neg1_idx}): {cpp_results[cpp_neg1_idx]:.15f}")
    else:
        # 找最接近-1的值
        cpp_closest_to_neg1 = np.argmin(np.abs(cpp_results + 1.0))
        print(f"C++最接近-1.0的值 (行{cpp_closest_to_neg1}): {cpp_results[cpp_closest_to_neg1]:.15f}")
        print(f"与-1.0的差异: {cpp_results[cpp_closest_to_neg1] + 1.0:.2e}")
    
    # 总结
    print(f"\n=== 总结 ===")
    max_diff = np.max(valid_abs_diff) if len(valid_abs_diff) > 0 else 0
    
    if max_diff < 1e-14:
        print("✓ 结果基本一致 (差异 < 1e-14)")
    elif max_diff < 1e-10:
        print("⚠ 有轻微数值精度差异 (差异 < 1e-10)")
    elif max_diff < 1e-6:
        print("⚠ 有明显数值差异 (差异 < 1e-6)")
    else:
        print("✗ 有显著差异")
    
    print(f"\n主要问题:")
    print(f"1. 最大绝对差异: {max_diff:.2e}")
    print(f"2. 数值精度问题主要出现在接近完美相关性的情况")
    print(f"3. C++版本在Row 1的-1.0值有轻微偏差: {cpp_results[1] + 1.0:.2e}")

if __name__ == "__main__":
    main()
