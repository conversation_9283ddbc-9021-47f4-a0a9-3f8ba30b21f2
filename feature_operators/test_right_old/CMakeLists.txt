# cmake_minimum_required(VERSION 3.15)
# project(OperatorTests)

# set(CMAKE_CXX_STANDARD 17)
# set(CMAKE_CXX_STANDARD_REQUIRED ON)
# set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
# add_compile_options(-Wall -O3 -msse4 -g)

# # Find Eigen3
# find_package(Eigen3 REQUIRED)
# find_package(BLAS QUIET)
# # Include directories
# include_directories(${CMAKE_SOURCE_DIR}/../include)

# # Add the feature_operators library as a subdirectory
# add_subdirectory(${CMAKE_SOURCE_DIR}/.. feature_operators_build)

# # Add executable for C++ operators test
# add_executable(test_optimized_operators test_optimized_operators.cpp)
# target_link_libraries(test_optimized_operators feature_ops_lib Eigen3::Eigen ${OpenBLAS_LIBRARIES})

# # Add executable for C++ performance benchmark
# add_executable(benchmark_cpp_operators benchmark_cpp_operators.cpp)
# target_link_libraries(benchmark_cpp_operators feature_ops_lib Eigen3::Eigen ${OpenBLAS_LIBRARIES})

# # Create output directories
# file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/test_results/cpp)
# file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/test_results/python)
cmake_minimum_required(VERSION 3.15)
project(OperatorTests)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
add_compile_options(-Wall -O3 -march=native -g)

# Find Eigen3
find_package(Eigen3 REQUIRED)

# 明确查找 OpenBLAS
# OpenBLAS 通常提供一个名为 OpenBLASConfig.cmake 的模块，或者通过 find_library 来找到
# 尝试使用 find_package，如果它有自己的模块
find_package(BLAS QUIET) # 尝试查找 OpenBLAS 模块

if (OpenBLAS_FOUND)
    message(STATUS "Found OpenBLAS: ${OpenBLAS_LIBRARIES}")
    set(MY_BLAS_LIBRARIES ${OpenBLAS_LIBRARIES})
    set(MY_BLAS_INCLUDE_DIRS ${OpenBLAS_INCLUDE_DIRS})
elseif (BLAS_FOUND) # 如果没有找到 OpenBLAS，尝试使用通用的 BLAS
    message(STATUS "Found generic BLAS: ${BLAS_LIBRARIES}")
    set(MY_BLAS_LIBRARIES ${BLAS_LIBRARIES})
    set(MY_BLAS_INCLUDE_DIRS ${BLAS_INCLUDE_DIRS})
else () # 如果两者都没找到，打印警告
    message(WARNING "Neither OpenBLAS nor generic BLAS found. Performance may be degraded for linear algebra operations.")
    set(MY_BLAS_LIBRARIES "") # 清空变量，避免链接不存在的库
    set(MY_BLAS_INCLUDE_DIRS "")
endif ()


# Include directories
# 确保将 OpenBLAS 的头文件路径也包含进来，如果它有的话
include_directories(${CMAKE_SOURCE_DIR}/../include ${MY_BLAS_INCLUDE_DIRS})

# Add the feature_operators library as a subdirectory
add_subdirectory(${CMAKE_SOURCE_DIR}/.. feature_operators_build)

# Add executable for C++ operators test
add_executable(test_optimized_operators test_optimized_operators.cpp)
# 链接 Eigen3::Eigen 库，并添加对 BLAS 的支持
target_link_libraries(test_optimized_operators feature_ops_lib Eigen3::Eigen ${MY_BLAS_LIBRARIES})
# 告诉 Eigen 优先使用外部 BLAS
target_compile_definitions(test_optimized_operators PUBLIC -DEIGEN_USE_BLAS -DEIGEN_USE_LAPACKE)


# Add executable for C++ performance benchmark
add_executable(benchmark_cpp_operators benchmark_cpp_operators.cpp)
# 链接 Eigen3::Eigen 库，并添加对 BLAS 的支持
target_link_libraries(benchmark_cpp_operators feature_ops_lib Eigen3::Eigen ${MY_BLAS_LIBRARIES})
# 告诉 Eigen 优先使用外部 BLAS
target_compile_definitions(benchmark_cpp_operators PUBLIC -DEIGEN_USE_BLAS -DEIGEN_USE_LAPACKE)

# Create output directories
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/test_results/cpp)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/test_results/python)