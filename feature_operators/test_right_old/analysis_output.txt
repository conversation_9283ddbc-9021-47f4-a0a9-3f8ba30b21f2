=== ts_Corr Python vs C++ 差异分析 ===
Python结果形状: (499, 517)
C++ v2结果形状: (499, 517)

差异统计:
  最大绝对差异: 1.729553780750000
  平均绝对差异: 0.091779037244335
  显著差异数量 (>1e-8): 21984
  显著差异数量 (>1e-10): 25152
  显著差异数量 (>1e-12): 61895

最大差异位置: (nan, 'Unnamed: 278')
最大差异值: 1.729553780750000

前10行详细比较:
Row 0: Python=-1.000000000000, C++=-0.999999999980, Diff=nan
Row 1: Python=-0.579414191160, C++=-0.579414191160, Diff=nan
Row 2: Python=0.124106986760, C++=0.124106986760, Diff=nan
Row 3: Python=-0.266995427800, C++=-0.266995427800, Diff=nan
Row 4: Python=-0.677888266690, C++=-0.677888266690, Diff=nan
Row 5: Python=-0.770786282020, C++=-0.770786282020, Diff=nan
Row 6: Python=-0.838127562150, C++=-0.838127562150, Diff=nan
Row 7: Python=-0.681492632970, C++=-0.681492632970, Diff=nan
Row 8: Python=-0.685663502410, C++=-0.685663502410, Diff=nan
Row 9: Python=-0.640739702280, C++=-0.640739702280, Diff=nan

Python结果中的特殊值:
  NaN数量: 2349
  Inf数量: 0
  1.0数量: 192
  -1.0数量: 321

C++结果中的特殊值:
  NaN数量: 2383
  Inf数量: 0
  1.0数量: 37
  -1.0数量: 69

第一行分析:
Python第一行: -1.0
C++第一行: -0.99999999998
差异: -2.000000165480742e-11

系统性偏差分析:
Traceback (most recent call last):
  File "/home/<USER>/git/feature_operators/test_right/analyze_ts_corr_diff.py", line 154, in <module>
    main()
  File "/home/<USER>/git/feature_operators/test_right/analyze_ts_corr_diff.py", line 130, in main
    python_result, cpp_result, diff = analyze_differences()
  File "/home/<USER>/git/feature_operators/test_right/analyze_ts_corr_diff.py", line 69, in analyze_differences
    valid_mask = ~(python_result.isna() | cpp_v2_result.isna())
  File "/home/<USER>/freqtrade/.venv/lib/python3.10/site-packages/pandas/core/generic.py", line 1571, in __invert__
    new_data = self._mgr.apply(operator.invert)
  File "/home/<USER>/freqtrade/.venv/lib/python3.10/site-packages/pandas/core/internals/managers.py", line 361, in apply
    applied = b.apply(f, **kwargs)
  File "/home/<USER>/freqtrade/.venv/lib/python3.10/site-packages/pandas/core/internals/blocks.py", line 393, in apply
    result = func(self.values, **kwargs)
TypeError: ufunc 'invert' not supported for the input types, and the inputs could not be safely coerced to any supported types according to the casting rule ''safe''
