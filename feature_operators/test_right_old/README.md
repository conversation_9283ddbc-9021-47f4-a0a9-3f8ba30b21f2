# Feature Operators 测试系统

完整的feature_operators测试系统，包含正确性验证和性能测试功能。

## 系统结构

```
test_right/
├── feature_operator_funcs.py    # Python算子实现
├── test_optimized_operators.cpp # C++正确性测试
├── test_python_operators.py     # Python正确性测试
├── simple_compare.py           # 结果比较脚本
├── benchmark_cpp_operators.cpp  # C++性能测试
├── benchmark_python_operators.py # Python性能测试
├── compare_performance.py       # 性能比较分析
├── CMakeLists.txt              # 构建配置
├── run_tests.sh               # 正确性测试脚本
├── run_benchmark.sh           # 性能测试脚本
├── test_data/                 # 测试数据
│   ├── close.csv
│   ├── open.csv
│   ├── high.csv
│   ├── low.csv
│   └── volume.csv
└── test_results/              # 测试结果输出
    ├── cpp/                   # C++算子输出
    ├── python/                # Python算子输出
    └── comparison_results.txt # 比较结果
```

## 快速使用

### 正确性验证
```bash
# 完整测试
bash run_tests.sh
```

### 性能测试
```bash
# 默认100次迭代
bash run_benchmark.sh

# 自定义迭代次数
bash run_benchmark.sh --iterations 50
```

### 查看结果
- 正确性验证：`test_results/comparison_results.txt`
- 性能测试：`test_results/performance_comparison.txt`

## 算子覆盖

- **基本运算**: Add, Minus, Multiply, Divide, Sqrt, Log, Abs, Max, Min等
- **逻辑运算**: And, Or, Not, Xor
- **比较运算**: Equal, Mthan, Lthan等
- **时间序列**: ts_Mean, ts_Sum, ts_Corr, ts_Rank等
- **面板运算**: pn_Rank, pn_Stand, pn_GroupRank等
- **Tot系列**: Tot_Mean, Tot_Sum, Tot_Rank等
- **数据工具**: FilterInf, FillNan等

总计93个算子，覆盖所有主要功能类别。

## 测试结果

### 正确性验证
- 验证C++与Python实现的一致性
- 数值差异分析和统计报告
- 当前匹配率: 81.2% (69/85个算子完全匹配)

### 性能测试
- 微秒级精度的执行时间测量
- C++相对Python的加速比分析
- 分类性能统计和排名

## 环境要求

- C++17编译器
- Python 3.6+
- Eigen3库
- pandas, numpy等Python库

## 故障排除

1. **编译错误**: 检查Eigen3路径 (`/usr/local/include/eigen3`)
2. **Python导入错误**: 确认当前目录包含`feature_operator_funcs.py`
3. **数据加载失败**: 确认`test_data/`目录存在且包含CSV文件
4. **权限问题**: `chmod +x *.sh`

这个测试系统为feature_operators项目提供了全面的质量保证和性能评估能力。
