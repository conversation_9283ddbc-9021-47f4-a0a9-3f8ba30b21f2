#!/bin/bash

# Feature Operators 性能测试脚本

set -e  # 遇到错误时退出

# 定义路径
BASE_DIR="/home/<USER>/git/feature_operators"
TEST_DIR="$BASE_DIR/test_right"
RESULTS_DIR="$TEST_DIR/test_results"
BUILD_DIR="$TEST_DIR/build"

# 默认迭代次数
ITERATIONS=30

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --iterations)
            ITERATIONS="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [--iterations N]"
            echo "  --iterations N    设置迭代次数 (默认: 100)"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助"
            exit 1
            ;;
    esac
done

echo "=========================================="
echo "Feature Operators 性能测试"
echo "迭代次数: $ITERATIONS"
echo "=========================================="

# 检查是否在正确的目录
if [ ! -d "$TEST_DIR" ]; then
    echo "错误: 测试目录不存在: $TEST_DIR"
    exit 1
fi

cd "$TEST_DIR"

# 创建结果目录
mkdir -p "$RESULTS_DIR"

echo "当前工作目录: $(pwd)"
echo "结果目录: $RESULTS_DIR"

# 检查测试数据是否存在
if [ ! -f "test_data/close.csv" ]; then
    echo "错误: 测试数据不存在，请先运行验证测试生成数据"
    exit 1
fi

echo "✓ 测试数据检查通过"

# 构建C++性能测试程序
echo ""
echo "1. 构建C++性能测试程序..."
echo "----------------------------------------"

if [ ! -d "$BUILD_DIR" ]; then
    mkdir -p "$BUILD_DIR"
fi

cd "$BUILD_DIR"

# 配置和构建
echo "配置CMake..."
cmake .. || {
    echo "错误: CMake配置失败"
    exit 1
}

echo "编译C++程序..."
make -j$(nproc) || {
    echo "错误: 编译失败"
    exit 1
}

# 检查可执行文件是否存在
if [ ! -f "benchmark_cpp_operators" ]; then
    echo "错误: C++性能测试程序编译失败"
    exit 1
fi

echo "✓ C++性能测试程序编译成功"

# 运行C++性能测试
echo ""
echo "2. 运行C++性能测试..."
echo "----------------------------------------"

cd "$TEST_DIR"
echo "运行C++性能测试 ($ITERATIONS次迭代)..."

# 运行C++性能测试
"$BUILD_DIR/benchmark_cpp_operators" "$ITERATIONS" || {
    echo "错误: C++性能测试运行失败"
    exit 1
}

echo "✓ C++性能测试完成"

# 运行Python性能测试
echo ""
echo "3. 运行Python性能测试..."
echo "----------------------------------------"

echo "运行Python性能测试 ($ITERATIONS次迭代)..."

python3 benchmark_python_operators.py --iterations "$ITERATIONS" || {
    echo "错误: Python性能测试运行失败"
    exit 1
}

echo "✓ Python性能测试完成"

# 比较性能结果
echo ""
echo "4. 分析性能结果..."
echo "----------------------------------------"

echo "生成性能比较报告..."

python3 compare_performance.py || {
    echo "错误: 性能比较分析失败"
    exit 1
}

echo "✓ 性能比较分析完成"

# 显示结果摘要
echo ""
echo "5. 测试结果摘要"
echo "=========================================="

if [ -f "$RESULTS_DIR/performance_comparison.txt" ]; then
    echo "性能比较报告:"
    echo "----------------------------------------"
    head -20 "$RESULTS_DIR/performance_comparison.txt"
    echo ""
    echo "完整报告请查看: $RESULTS_DIR/performance_comparison.txt"
else
    echo "警告: 性能比较报告未生成"
fi

# 列出生成的文件
echo ""
echo "生成的文件:"
echo "----------------------------------------"
ls -la "$RESULTS_DIR"/*.json "$RESULTS_DIR"/*.txt "$RESULTS_DIR"/*.csv 2>/dev/null || echo "没有找到结果文件"

echo ""
echo "=========================================="
echo "性能测试完成!"
echo "=========================================="
echo ""
echo "结果文件位置:"
echo "• C++性能结果: $RESULTS_DIR/benchmark_cpp_results.json"
echo "• Python性能结果: $RESULTS_DIR/benchmark_python_results.json"
echo "• 性能比较报告: $RESULTS_DIR/performance_comparison.txt"
echo "• 详细数据: $RESULTS_DIR/performance_comparison_detailed.csv"



echo ""
echo "使用方法:"
echo "• 查看报告: cat $RESULTS_DIR/performance_comparison.txt"
echo "• 查看详细数据: cat $RESULTS_DIR/performance_comparison_detailed.csv"
