cmake_minimum_required(VERSION 3.15)
project(FeatureOperators VERSION 1.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
add_compile_options(-Wall -O3 -msse4 -g)

# Common paths for Eigen3 - adjust if your system is different
set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} "/usr/lib/eigen3/cmake" "/usr/local/lib/cmake/eigen3" "/opt/homebrew/share/eigen3/cmake")

find_package(Eigen3 REQUIRED CONFIG)
# find_package(Boost REQUIRED COMPONENTS math)
find_package(Catch2 REQUIRED)

add_library(feature_ops_lib STATIC)
target_sources(feature_ops_lib PRIVATE
    src/core_math.cpp
    src/data_utils.cpp
    src/logical_ops.cpp
    src/comparison_ops.cpp
    src/reduction_ops.cpp
    src/panel_ops.cpp
    src/timeseries_ops.cpp
    src/rolling_aggregations.cpp
    src/timeseries_ops_v2.cpp
    src/group_ops.cpp
)
target_include_directories(feature_ops_lib PUBLIC include) # PUBLIC so consumers get include path
target_link_libraries(feature_ops_lib PUBLIC Eigen3::Eigen) # Link Eigen and Boost to the lib

message(STATUS "Eigen3 include directory: ${EIGEN3_INCLUDE_DIR}")
message(STATUS "Boost include directories: ${Boost_INCLUDE_DIRS}")
message(STATUS "Boost libraries: ${Boost_LIBRARIES}")
message(STATUS "Catch2 include directory: ${Catch2_INCLUDE_DIR}")

# Setup for Tests
enable_testing()

add_executable(core_math_tests tests/test_core_math.cpp)
target_link_libraries(core_math_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME CoreMathTest COMMAND core_math_tests)

add_executable(data_utils_tests tests/test_data_utils.cpp)
target_link_libraries(data_utils_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME DataUtilsTest COMMAND data_utils_tests)

add_executable(logical_ops_tests tests/test_logical_ops.cpp)
target_link_libraries(logical_ops_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME LogicalOpsTest COMMAND logical_ops_tests)

add_executable(comparison_ops_tests tests/test_comparison_ops.cpp)
target_link_libraries(comparison_ops_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME ComparisonOpsTest COMMAND comparison_ops_tests)

add_executable(reduction_ops_tests tests/test_reduction_ops.cpp)
target_link_libraries(reduction_ops_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME ReductionOpsTest COMMAND reduction_ops_tests)

add_executable(panel_ops_tests tests/test_panel_ops.cpp)
target_link_libraries(panel_ops_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME PanelOpsTest COMMAND panel_ops_tests)

add_executable(timeseries_ops_tests tests/test_timeseries_ops.cpp)
target_link_libraries(timeseries_ops_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME TimeSeriesOpsTest COMMAND timeseries_ops_tests)

add_executable(group_ops_tests tests/test_group_ops.cpp)
target_link_libraries(group_ops_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME GroupOpsTest COMMAND group_ops_tests)