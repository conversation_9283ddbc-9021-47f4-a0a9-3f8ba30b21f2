cmake_minimum_required(VERSION 3.15)
project(FeatureOperators VERSION 1.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
add_compile_options(-Wall -O3 -msse4 -g)

# Common paths for Eigen3 - adjust if your system is different
set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} "/usr/lib/eigen3/cmake" "/usr/local/lib/cmake/eigen3" "/opt/homebrew/share/eigen3/cmake")

find_package(Eigen3 REQUIRED CONFIG)
# find_package(Boost REQUIRED COMPONENTS math)
find_package(Catch2 REQUIRED)

add_library(feature_ops_lib STATIC)
target_sources(feature_ops_lib PRIVATE
    src/core_math.cpp
    src/data_utils.cpp
    src/logical_ops.cpp
    src/comparison_ops.cpp
    src/reduction_ops.cpp
    src/panel_ops.cpp
    src/timeseries_ops.cpp
    src/rolling_aggregations.cpp
    src/timeseries_ops_v2.cpp
    src/group_ops.cpp
)
target_include_directories(feature_ops_lib PUBLIC include) # PUBLIC so consumers get include path
target_link_libraries(feature_ops_lib PUBLIC Eigen3::Eigen) # Link Eigen and Boost to the lib

message(STATUS "Eigen3 include directory: ${EIGEN3_INCLUDE_DIR}")
message(STATUS "Boost include directories: ${Boost_INCLUDE_DIRS}")
message(STATUS "Boost libraries: ${Boost_LIBRARIES}")
message(STATUS "Catch2 include directory: ${Catch2_INCLUDE_DIR}")

# Setup for Tests
enable_testing()

add_executable(core_math_tests tests/test_core_math.cpp)
target_link_libraries(core_math_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME CoreMathTest COMMAND core_math_tests)

add_executable(data_utils_tests tests/test_data_utils.cpp)
target_link_libraries(data_utils_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME DataUtilsTest COMMAND data_utils_tests)

add_executable(logical_ops_tests tests/test_logical_ops.cpp)
target_link_libraries(logical_ops_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME LogicalOpsTest COMMAND logical_ops_tests)

add_executable(comparison_ops_tests tests/test_comparison_ops.cpp)
target_link_libraries(comparison_ops_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME ComparisonOpsTest COMMAND comparison_ops_tests)

add_executable(reduction_ops_tests tests/test_reduction_ops.cpp)
target_link_libraries(reduction_ops_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME ReductionOpsTest COMMAND reduction_ops_tests)

add_executable(panel_ops_tests tests/test_panel_ops.cpp)
target_link_libraries(panel_ops_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME PanelOpsTest COMMAND panel_ops_tests)

add_executable(timeseries_ops_tests tests/test_timeseries_ops.cpp)
target_link_libraries(timeseries_ops_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME TimeSeriesOpsTest COMMAND timeseries_ops_tests)

add_executable(group_ops_tests tests/test_group_ops.cpp)
target_link_libraries(group_ops_tests PRIVATE feature_ops_lib Catch2::Catch2WithMain)
add_test(NAME GroupOpsTest COMMAND group_ops_tests)

# Factor Framework Library
file(GLOB FACTOR_FRAMEWORK_SOURCES
    "src/factor_framework/*.cpp"
)

add_library(factor_framework_lib STATIC ${FACTOR_FRAMEWORK_SOURCES})
target_include_directories(factor_framework_lib PUBLIC include)
target_link_libraries(factor_framework_lib PUBLIC feature_ops_lib Eigen3::Eigen)

# Factor Framework Demo
add_executable(framework_demo examples/framework_demo.cpp)
target_link_libraries(framework_demo PRIVATE factor_framework_lib feature_ops_lib Eigen3::Eigen)

# Simple Factor Test
add_executable(simple_factor_test examples/simple_factor_test.cpp)
target_link_libraries(simple_factor_test PRIVATE feature_ops_lib Eigen3::Eigen)

# Custom target for generating factor code
add_custom_target(generate_factors
    COMMAND python3 ${CMAKE_CURRENT_SOURCE_DIR}/scripts/factor_code_generator.py
            --input ${CMAKE_CURRENT_SOURCE_DIR}/feature.csv
            --output_dir ${CMAKE_CURRENT_SOURCE_DIR}/generated_factors
            --max_factors 5
            --verbose
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/scripts
    COMMENT "Generating factor code from feature.csv"
)

# Auto-Registered Generated Factors Demo (correct design)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/generated_factors_full/include)
    message(STATUS "Found generated factors headers, creating auto-registered demo...")

    # Collect generated factor source files
    file(GLOB GENERATED_FACTOR_SOURCES
        "${CMAKE_CURRENT_SOURCE_DIR}/generated_factors_full/src/factor*.cpp"
        "${CMAKE_CURRENT_SOURCE_DIR}/generated_factors_full/src/all_factors.cpp"
    )

    if(GENERATED_FACTOR_SOURCES)
        # Auto-Registered Generated Factors Demo
        add_executable(auto_registered_demo
            examples/auto_registered_demo.cpp
            ${GENERATED_FACTOR_SOURCES}
        )
        target_link_libraries(auto_registered_demo PRIVATE
            factor_framework_lib
            feature_ops_lib
            Eigen3::Eigen
        )
        target_include_directories(auto_registered_demo PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/generated_factors_full/include
        )

        message(STATUS "Auto-registered factors demo target created")

        # Simplified Demo (shows the new simplified architecture)
        add_executable(simplified_demo
            examples/simplified_demo.cpp
            ${GENERATED_FACTOR_SOURCES}
        )
        target_link_libraries(simplified_demo PRIVATE
            factor_framework_lib
            feature_ops_lib
            Eigen3::Eigen
        )
        target_include_directories(simplified_demo PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/generated_factors_full/include
        )

        # Correct Usage Demo (shows the right way to use the framework)
        add_executable(correct_usage_demo
            examples/correct_usage_demo.cpp
            ${GENERATED_FACTOR_SOURCES}
        )
        target_link_libraries(correct_usage_demo PRIVATE
            factor_framework_lib
            feature_ops_lib
            Eigen3::Eigen
        )
        target_include_directories(correct_usage_demo PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/generated_factors_full/include
        )

        # Also keep the simple demo for comparison
        add_executable(simple_generated_demo
            examples/simple_generated_demo.cpp
            ${GENERATED_FACTOR_SOURCES}
        )
        target_link_libraries(simple_generated_demo PRIVATE
            factor_framework_lib
            feature_ops_lib
            Eigen3::Eigen
        )
        target_include_directories(simple_generated_demo PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/generated_factors_full/include
        )

    else()
        message(STATUS "No generated factor source files found")
    endif()
else()
    message(STATUS "Generated factors headers not found, skipping generated demos")
endif()

# Help target
add_custom_target(show_help
    COMMAND ${CMAKE_COMMAND} -E echo ""
    COMMAND ${CMAKE_COMMAND} -E echo "Available targets:"
    COMMAND ${CMAKE_COMMAND} -E echo "  framework_demo         - Factor framework demonstration"
    COMMAND ${CMAKE_COMMAND} -E echo "  simple_factor_test     - Simple factor calculation test"
    COMMAND ${CMAKE_COMMAND} -E echo "  generated_factors_demo - Demo using generated factor library"
    COMMAND ${CMAKE_COMMAND} -E echo "  generate_factors       - Generate C++ factor code from CSV"
    COMMAND ${CMAKE_COMMAND} -E echo "  test                   - Run all tests"
    COMMAND ${CMAKE_COMMAND} -E echo ""
)
