# 自动生成的因子CMake文件
cmake_minimum_required(VERSION 3.10)

# 添加生成的因子源文件
set(GENERATED_FACTOR_SOURCES
    src/all_factors.cpp
    src/factorp1corrs0.cpp
    src/factorp1corrs1.cpp
    src/factorp1corrs2.cpp
    src/factorp1corrs3.cpp
    src/factorp1corrs4.cpp
    src/factorp1corrs5.cpp
    src/factorp1corrs6.cpp
    src/factorp1corrs7.cpp
    src/factorp1corrs8.cpp
    src/factorp1corrs9.cpp
)

# 添加生成的因子头文件目录
set(GENERATED_FACTOR_INCLUDE_DIRS
    include
)

# 创建生成因子库
add_library(generated_factors STATIC ${GENERATED_FACTOR_SOURCES})

# 设置包含目录
target_include_directories(generated_factors PUBLIC
    ${GENERATED_FACTOR_INCLUDE_DIRS}
    ${CMAKE_SOURCE_DIR}/include
)

# 链接feature_operators库
target_link_libraries(generated_factors
    feature_ops_lib
)

# 设置编译选项
target_compile_features(generated_factors PUBLIC cxx_std_17)
target_compile_options(generated_factors PRIVATE
    $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra -O3>
    $<$<CXX_COMPILER_ID:Clang>:-Wall -Wextra -O3>
    $<$<CXX_COMPILER_ID:MSVC>:/W4 /O2>
)

# 安装规则
install(TARGETS generated_factors
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.hpp"
)