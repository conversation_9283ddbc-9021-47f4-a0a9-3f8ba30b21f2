#ifndef GENERATED_FACTORS_ALL_FACTORS_HPP
#define GENERATED_FACTORS_ALL_FACTORS_HPP

/**
 * 自动生成的因子头文件
 * 包含所有从feature.csv生成的因子类
 *
 * 生成的因子数量: 3
 */

// 包含因子框架
#include "factor_framework/factor_framework.hpp"

// 包含所有生成的因子
#include "generated_factors/factorp1corrs0.hpp"
#include "generated_factors/factorp1corrs1.hpp"
#include "generated_factors/factorp1corrs2.hpp"

namespace generated_factors {

/**
 * 获取所有生成的因子名称
 */
std::vector<std::string> getAllGeneratedFactorNames();

/**
 * 获取所有生成的因子ID
 */
std::vector<int> getAllGeneratedFactorIds();

/**
 * 初始化所有生成的因子（确保注册）
 */
void initializeAllFactors();

} // namespace generated_factors

#endif // GENERATED_FACTORS_ALL_FACTORS_HPP