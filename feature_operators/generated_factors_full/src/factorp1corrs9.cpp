#include "generated_factors/factorp1corrs9.hpp"
#include <stdexcept>

namespace generated_factors {

FactorP1corrs9::FactorP1corrs9(int factor_id, const std::string& factor_name, const std::string& formula)
    : FactorBase(factor_id, factor_name, formula) {
}

feature_operators::DataFrame FactorP1corrs9::calculate(
    const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) {

    // 验证输入数据
    if (!validateInput(data_map)) {
        throw std::runtime_error("Invalid input data for factor " + getName());
    }

    try {
        // 执行因子计算
        auto result = feature_operators::ts_Corr(getField(data_map, "VWAP")/feature_operators::ts_Delay(getField(data_map, "VWAP"),1)-1,getField(data_map, "Volume"),60);
        return result;
    } catch (const std::exception& e) {
        throw std::runtime_error("Error calculating factor " + getName() + ": " + e.what());
    }
}

std::vector<std::string> FactorP1corrs9::getRequiredFields() const {
    return {"VWAP", "Volume"};
}

} // namespace generated_factors

// 注册因子
REGISTER_FACTOR(generated_factors::FactorP1corrs9, "p1_corrs9")