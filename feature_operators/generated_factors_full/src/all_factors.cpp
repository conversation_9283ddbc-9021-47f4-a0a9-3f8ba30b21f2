#include "generated_factors/all_factors.hpp"

namespace generated_factors {

std::vector<std::string> getAllGeneratedFactorNames() {
    return {"p1_corrs0", "p1_corrs1", "p1_corrs2"};
}

std::vector<int> getAllGeneratedFactorIds() {
    return {0, 1, 2};
}

void initializeAllFactors() {
    static bool initialized = false;
    if (!initialized) {
        auto& registry = factor_framework::FactorRegistry::getInstance();

        // 手动注册所有生成的因子
        registry.registerFactory("p1_corrs0", std::make_unique<FactorP1corrs0Factory>());
        registry.registerFactory("p1_corrs1", std::make_unique<FactorP1corrs1Factory>());
        registry.registerFactory("p1_corrs2", std::make_unique<FactorP1corrs2Factory>());

        initialized = true;
        std::cout << "✓ Registered " << 3 << " generated factors" << std::endl;
    }
}

// 因子工厂类定义

class FactorP1corrs0Factory : public factor_framework::FactorFactory {
public:
    std::unique_ptr<factor_framework::FactorBase> createFactor(
        int factor_id,
        const std::string& factor_name,
        const std::string& formula) override {
        return std::make_unique<FactorP1corrs0>(factor_id, factor_name, formula);
    }
};

class FactorP1corrs1Factory : public factor_framework::FactorFactory {
public:
    std::unique_ptr<factor_framework::FactorBase> createFactor(
        int factor_id,
        const std::string& factor_name,
        const std::string& formula) override {
        return std::make_unique<FactorP1corrs1>(factor_id, factor_name, formula);
    }
};

class FactorP1corrs2Factory : public factor_framework::FactorFactory {
public:
    std::unique_ptr<factor_framework::FactorBase> createFactor(
        int factor_id,
        const std::string& factor_name,
        const std::string& formula) override {
        return std::make_unique<FactorP1corrs2>(factor_id, factor_name, formula);
    }
};

} // namespace generated_factors