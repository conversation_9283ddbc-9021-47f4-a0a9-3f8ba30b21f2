#include "generated_factors/all_factors.hpp"
#include <iostream>
#include <algorithm>

namespace generated_factors {

std::vector<std::string> getAllGeneratedFactorNames() {
    return {"p1_corrs0", "p1_corrs1", "p1_corrs2"};
}

std::vector<int> getAllGeneratedFactorIds() {
    return {0, 1, 2};
}

void initializeAllFactors() {
    // 这个函数确保所有因子类被链接器包含
    static bool initialized = false;
    if (!initialized) {
        std::cout << "✓ Generated factors library initialized" << std::endl;
        std::cout << "✓ Available factors: " << 3 << std::endl;

        // 显示所有可用的因子
        auto factor_names = getAllGeneratedFactorNames();
        for (const auto& factor_name : factor_names) {
            std::cout << "  - " << factor_name << std::endl;
        }

        initialized = true;
    }
}

/**
 * 批量注册所有生成的因子到FactorManager
 * @param manager 因子管理器
 * @return 成功注册的因子数量
 */
int registerAllFactorsToManager(std::shared_ptr<factor_framework::FactorManager> manager) {
    if (!manager) {
        std::cerr << "Error: FactorManager is null" << std::endl;
        return 0;
    }

    // 从静态注册列表创建所有因子实例
    auto& registration_list = factor_framework::getFactorRegistrationList();

    int registered_count = 0;
    for (auto& creator : registration_list) {
        try {
            auto factor = creator();
            if (factor && manager->registerFactor(std::move(factor))) {
                registered_count++;
            }
        } catch (const std::exception& e) {
            std::cerr << "Error creating factor: " << e.what() << std::endl;
        }
    }

    std::cout << "✓ Registered " << registered_count << " generated factors to manager" << std::endl;
    return registered_count;
}

} // namespace generated_factors