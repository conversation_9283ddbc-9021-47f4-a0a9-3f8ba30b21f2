[{"id": 0, "name": "p1_corrs0", "formula": "ts_Corr(Close,Volume,60)", "class_name": "FactorP1corrs0", "required_fields": ["Volume", "Close"]}, {"id": 1, "name": "p1_corrs1", "formula": "ts_<PERSON>rr(Close/ts_Delay(Close,1)-1,Volume,60)", "class_name": "FactorP1corrs1", "required_fields": ["Volume", "Close"]}, {"id": 2, "name": "p1_corrs2", "formula": "ts_<PERSON>rr(ts_<PERSON>ay(Close,1),Volume,60)", "class_name": "FactorP1corrs2", "required_fields": ["Volume", "Close"]}, {"id": 3, "name": "p1_corrs3", "formula": "ts_<PERSON><PERSON>(Close,ts_Delay(Volume,1),60)", "class_name": "FactorP1corrs3", "required_fields": ["Volume", "Close"]}, {"id": 4, "name": "p1_corrs4", "formula": "ts_<PERSON><PERSON>(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close/ts_Delay(Close,1)-1,60)", "class_name": "FactorP1corrs4", "required_fields": ["Close"]}, {"id": 5, "name": "p1_corrs5", "formula": "ts_<PERSON><PERSON>(ts_Delay(Close/ts_Delay(Close,1)-1,1),Close,60)", "class_name": "FactorP1corrs5", "required_fields": ["Close"]}, {"id": 6, "name": "p1_corrs6", "formula": "ts_<PERSON><PERSON>(Volume,Volume-ts_Delay(Volume,1),60)", "class_name": "FactorP1corrs6", "required_fields": ["Volume"]}, {"id": 7, "name": "p1_corrs7", "formula": "ts_<PERSON><PERSON>(Volume-ts_Delay(Volume,1), Volume-ts_Delay(Volume,1) - ts_Delay(Volume-ts_Delay(Volume,1),1),60)", "class_name": "FactorP1corrs7", "required_fields": ["Volume"]}, {"id": 8, "name": "p1_corrs8", "formula": "ts_Corr(VWAP,Volume,60)", "class_name": "FactorP1corrs8", "required_fields": ["VWAP", "Volume"]}, {"id": 9, "name": "p1_corrs9", "formula": "ts_Corr(VWAP/ts_Delay(VWAP,1)-1,Volume,60)", "class_name": "FactorP1corrs9", "required_fields": ["VWAP", "Volume"]}]