/**
 * 因子框架演示程序
 * 
 * 这个程序展示了如何使用因子框架：
 * 1. 加载因子配置
 * 2. 准备数据
 * 3. 选择因子
 * 4. 执行计算
 * 5. 处理结果
 */

#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"
#include <iostream>
#include <chrono>

using namespace factor_framework;

// 创建测试数据
std::unique_ptr<data_interface> createTestData() {
    auto data_interface = std::make_unique<data_interface>();
    
    // 创建标准市场数据（1000行 x 100列）
    data_interface->createStandardMarketData(1000, 100, 1.0);
    
    // 添加一些随机变化
    auto close_data = data_interface->getData("Close");
    auto volume_data = data_interface->getData("Volume");
    
    // 模拟价格变化
    for (int i = 1; i < close_data.rows(); ++i) {
        for (int j = 0; j < close_data.cols(); ++j) {
            double change = (rand() % 200 - 100) / 10000.0; // -1% to +1%
            close_data(i, j) = close_data(i-1, j) * (1.0 + change);
        }
    }
    
    // 模拟成交量变化
    for (int i = 0; i < volume_data.rows(); ++i) {
        for (int j = 0; j < volume_data.cols(); ++j) {
            volume_data(i, j) = 1000000 + (rand() % 500000); // 100万到150万
        }
    }
    
    // 更新数据
    data_interface->addData("Close", close_data);
    data_interface->addData("Volume", volume_data);
    
    // 计算VWAP
    auto vwap_data = close_data; // 简化：VWAP = Close
    data_interface->addData("VWAP", vwap_data);
    
    return data_interface;
}

int main() {
    try {
        std::cout << "=== 因子框架演示程序 ===" << std::endl;
        std::cout << "框架版本: " << FACTOR_FRAMEWORK_VERSION << std::endl;
        
        // 1. 初始化框架
        std::cout << "\n1. 初始化框架..." << std::endl;
        FACTOR_FRAMEWORK_INIT();
        
        // 初始化生成的因子
        generated_factors::initialize_all_factors();
        std::cout << "✓ 框架初始化完成" << std::endl;
        
        // 2. 创建因子管理器
        std::cout << "\n2. 创建因子管理器..." << std::endl;
        auto factor_manager = std::make_shared<factor_manager>();
        
        // 加载因子配置
        if (factor_manager->load_factors_from_csv("feature.csv")) {
            std::cout << "✓ 因子配置加载成功" << std::endl;
        } else {
            std::cout << "⚠ 因子配置加载失败，使用默认配置" << std::endl;
            // 手动添加一些测试因子
            factor_manager->add_factor(0, "test_corr", "ts_Corr(Close,Volume,60)");
            factor_manager->add_factor(1, "test_mean", "ts_Mean(Close,20)");
            factor_manager->add_factor(2, "test_stdev", "ts_Stdev(Close,20)");
        }
        
        // 3. 创建数据接口
        std::cout << "\n3. 准备测试数据..." << std::endl;
        auto data_interface = createTestData();
        data_interface->printDataInfo();
        std::cout << "✓ 测试数据准备完成" << std::endl;
        
        // 4. 创建计算引擎
        std::cout << "\n4. 创建计算引擎..." << std::endl;
        FactorEngine engine(factor_manager, data_interface);
        
        // 配置引擎
        engine.setNumThreads(4);
        engine.enablePerformanceStats(true);
        engine.enableVerboseLogging(true);
        
        std::cout << "✓ 计算引擎创建完成" << std::endl;
        
        // 5. 验证环境
        std::cout << "\n5. 验证计算环境..." << std::endl;
        auto [valid, error_msg] = engine.validateEnvironment();
        if (valid) {
            std::cout << "✓ 计算环境验证通过" << std::endl;
        } else {
            std::cout << "❌ 计算环境验证失败: " << error_msg << std::endl;
            return 1;
        }
        
        // 6. 选择要计算的因子
        std::cout << "\n6. 选择计算因子..." << std::endl;
        auto all_factor_ids = factor_manager->getAllFactorIds();
        
        if (all_factor_ids.empty()) {
            std::cout << "❌ 没有可用的因子" << std::endl;
            return 1;
        }
        
        // 选择前5个因子（如果有的话）
        std::vector<int> selected_ids;
        for (size_t i = 0; i < std::min(size_t(5), all_factor_ids.size()); ++i) {
            selected_ids.push_back(all_factor_ids[i]);
        }
        
        factor_manager->select_factors(selected_ids);
        factor_manager->print_selected_factor_info();
        
        // 7. 预热引擎
        std::cout << "\n7. 预热计算引擎..." << std::endl;
        engine.warmUp();
        std::cout << "✓ 引擎预热完成" << std::endl;
        
        // 8. 执行计算
        std::cout << "\n8. 执行因子计算..." << std::endl;
        auto start_time = std::chrono::high_resolution_clock::now();
        
        auto result = engine.calculate_selected_factors();
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        // 9. 处理结果
        std::cout << "\n9. 处理计算结果..." << std::endl;
        if (result.success) {
            std::cout << "✅ 计算成功完成！" << std::endl;
            std::cout << "计算耗时: " << duration.count() << " ms" << std::endl;
            std::cout << "计算因子数量: " << result.factor_results.size() << std::endl;
            
            // 显示结果摘要
            for (const auto& [factor_name, factor_data] : result.factor_results) {
                std::cout << "因子: " << factor_name 
                         << ", 维度: " << factor_data.rows() << "x" << factor_data.cols() << std::endl;
                
                // 显示一些统计信息
                double sum = factor_data.sum();
                double mean = sum / (factor_data.rows() * factor_data.cols());
                std::cout << "  均值: " << mean << std::endl;
            }
            
            // 显示性能统计
            if (engine.getLastPerformanceStats().factors_calculated > 0) {
                const auto& stats = engine.getLastPerformanceStats();
                std::cout << "\n性能统计:" << std::endl;
                std::cout << "  总计算时间: " << stats.total_time.count() << " ms" << std::endl;
                std::cout << "  平均每因子时间: " << stats.getAverageTimePerFactor() << " ms" << std::endl;
                std::cout << "  数据处理速度: " << stats.getDataProcessingRate() << " 点/秒" << std::endl;
            }
            
        } else {
            std::cout << "❌ 计算失败: " << result.error_message << std::endl;
            return 1;
        }
        
        // 10. 清理
        std::cout << "\n10. 清理资源..." << std::endl;
        FACTOR_FRAMEWORK_CLEANUP();
        std::cout << "✓ 清理完成" << std::endl;
        
        std::cout << "\n🎉 演示程序执行完成！" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
}
