/**
 * 简化架构演示程序
 *
 * 展示重构后的简化架构：
 * 1. 移除FactorRegistry，只使用FactorManager
 * 2. 因子直接创建为FactorBase实例注册到FactorManager
 * 3. 简化的注册流程，消除工厂模式复杂性
 */

#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"
#include <iostream>
#include <iomanip>

using namespace factor_framework;

int main() {
    try {
        std::cout << "=== 简化架构演示程序 ===" << std::endl;
        std::cout << "展示重构后的简化架构设计" << std::endl;
        std::cout << "框架版本: " << FACTOR_FRAMEWORK_VERSION << std::endl;

        // 1. 初始化框架
        std::cout << "\n1. 初始化因子框架..." << std::endl;
        FACTOR_FRAMEWORK_INIT();

        // 2. 初始化生成的因子库
        std::cout << "\n2. 初始化生成的因子库..." << std::endl;
        generated_factors::initialize_all_factors();

        // 3. 创建测试数据
        std::cout << "\n3. 创建测试数据..." << std::endl;
        auto data_interface = std::make_shared<factor_framework::data_interface>();
        data_interface->createStandardMarketData(80, 40);  // 80天，40只股票

        // 4. 创建因子管理器
        std::cout << "\n4. 创建因子管理器..." << std::endl;
        auto factor_manager = std::make_shared<factor_framework::factor_manager>();

        // 🎯 关键改进：直接批量注册所有生成的因子
        std::cout << "\n5. 批量注册生成的因子..." << std::endl;
        int registered_count = generated_factors::register_all_factors_to_manager(factor_manager);

        if (registered_count == 0) {
            std::cout << "❌ 没有注册任何因子" << std::endl;
            return 1;
        }

        factor_manager->print_factor_info();

        // 6. 创建计算引擎
        std::cout << "\n6. 创建计算引擎..." << std::endl;
        FactorEngine engine(factor_manager, data_interface);
        engine.setNumThreads(4);
        engine.enablePerformanceStats(true);

        // 7. 用户的主要操作：选择要计算的因子
        std::cout << "\n7. 选择要计算的因子..." << std::endl;

        // 方式1：按ID选择所有因子
        std::cout << "\n方式1：按ID选择所有因子" << std::endl;
        factor_manager->select_factors({0, 1, 2});
        auto result1 = engine.calculate_selected_factors();
        std::cout << "✓ 按ID选择计算了 " << result1.factor_results.size() << " 个因子" << std::endl;

        // 方式2：按名称选择部分因子
        std::cout << "\n方式2：按名称选择部分因子" << std::endl;
        std::vector<std::string> selected_names = {"p1_corrs0", "p1_corrs2"};
        factor_manager->select_factors(selected_names);
        auto result2 = engine.calculate_selected_factors();
        std::cout << "✓ 按名称选择计算了 " << result2.factor_results.size() << " 个因子" << std::endl;

        // 8. 分析计算结果
        std::cout << "\n8. 分析计算结果..." << std::endl;

        if (result1.success) {
            std::cout << "✅ 计算成功！" << std::endl;
            std::cout << "数据规模: 80天 x 40股票 = 3,200个数据点/因子" << std::endl;

            for (const auto& [factor_name, data] : result1.factor_results) {
                std::cout << "\n📊 因子: " << factor_name << std::endl;
                std::cout << "   维度: " << data.rows() << "x" << data.cols() << std::endl;

                // 统计有效值
                int valid_count = 0;
                int nan_count = 0;
                double sum = 0.0;

                for (int i = 0; i < data.rows(); ++i) {
                    for (int j = 0; j < data.cols(); ++j) {
                        if (std::isfinite(data(i, j))) {
                            valid_count++;
                            sum += data(i, j);
                        } else {
                            nan_count++;
                        }
                    }
                }

                if (valid_count > 0) {
                    double mean = sum / valid_count;
                    std::cout << "   有效值: " << valid_count << " ("
                             << std::fixed << std::setprecision(1)
                             << (100.0 * valid_count / (data.rows() * data.cols())) << "%)" << std::endl;
                    std::cout << "   NaN值: " << nan_count << std::endl;
                    std::cout << "   均值: " << std::setprecision(6) << mean << std::endl;
                }
            }
        } else {
            std::cout << "❌ 计算失败: " << result1.error_message << std::endl;
        }

        // 9. 展示架构简化的优势
        std::cout << "\n9. 架构简化总结..." << std::endl;
        std::cout << "🎯 新架构的优势：" << std::endl;
        std::cout << "  ✅ 移除了FactorRegistry，简化了架构" << std::endl;
        std::cout << "  ✅ 因子直接注册为FactorBase实例" << std::endl;
        std::cout << "  ✅ 消除了工厂模式的复杂性" << std::endl;
        std::cout << "  ✅ 一行代码批量注册所有因子" << std::endl;
        std::cout << "  ✅ 用户代码更加简洁明了" << std::endl;

        std::cout << "\n📝 用户使用流程：" << std::endl;
        std::cout << "  1. 初始化框架和生成的因子库" << std::endl;
        std::cout << "  2. 创建FactorManager" << std::endl;
        std::cout << "  3. 一行代码批量注册: register_all_factors_to_manager()" << std::endl;
        std::cout << "  4. 选择要计算的因子" << std::endl;
        std::cout << "  5. 执行计算" << std::endl;

        std::cout << "\n🚫 不再需要的操作：" << std::endl;
        std::cout << "  ❌ 管理FactorRegistry" << std::endl;
        std::cout << "  ❌ 创建和管理FactorFactory" << std::endl;
        std::cout << "  ❌ 手动逐个添加因子" << std::endl;
        std::cout << "  ❌ 处理工厂创建逻辑" << std::endl;

        // 10. 性能基准测试
        std::cout << "\n10. 性能基准测试..." << std::endl;
        auto benchmark_result = framework_utils::runBenchmark(engine, {0, 1, 2}, 10);
        std::cout << benchmark_result << std::endl;

        // 11. 清理
        std::cout << "\n11. 清理资源..." << std::endl;
        FACTOR_FRAMEWORK_CLEANUP();

        std::cout << "\n🎉 简化架构演示完成！" << std::endl;
        std::cout << "📊 成功演示了重构后的简化架构" << std::endl;
        std::cout << "⚡ 架构更简洁，使用更方便" << std::endl;
        std::cout << "🚀 性能保持优秀，功能完全一致" << std::endl;

        return 0;

    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
}

/*
=== 简化后的完整用户代码示例 ===

#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"

int main() {
    // 1. 初始化
    FACTOR_FRAMEWORK_INIT();
    generated_factors::initialize_all_factors();

    // 2. 创建组件
    auto data_interface = std::make_shared<data_interface>();
    auto factor_manager = std::make_shared<factor_manager>();

    // 3. 加载数据和因子
    data_interface->createStandardMarketData(100, 50);
    generated_factors::register_all_factors_to_manager(factor_manager);  // 🎯 一行代码搞定！

    // 4. 选择和计算
    factor_manager->select_factors({0, 1, 2});
    FactorEngine engine(factor_manager, data_interface);
    auto result = engine.calculate_selected_factors();

    // 5. 使用结果
    if (result.success) {
        for (const auto& [name, data] : result.factor_results) {
            // 处理计算结果...
        }
    }

    return 0;
}

这就是全部！架构更简洁，使用更方便！
*/
