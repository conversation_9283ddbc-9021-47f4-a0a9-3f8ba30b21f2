/**
 * 自动注册因子演示程序
 *
 * 演示正确的使用方式：
 * 1. 因子在生成时自动注册到FactorRegistry
 * 2. 使用者只需要通过FactorManager选择和计算因子
 * 3. 无需手动注册，完全自动化
 */

#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <fstream>

using namespace factor_framework;

int main() {
    try {
        std::cout << "=== 自动注册因子演示程序 ===" << std::endl;
        std::cout << "演示正确的因子使用方式：自动注册 + 选择计算" << std::endl;
        std::cout << "框架版本: " << FACTOR_FRAMEWORK_VERSION << std::endl;

        // 1. 初始化框架
        std::cout << "\n1. 初始化因子框架..." << std::endl;
        FrameworkConfig config;
        config.default_num_threads = 4;
        config.enable_performance_stats = true;
        config.verbose_logging = false;

        FACTOR_FRAMEWORK_INIT_WITH_CONFIG(config);

        // 2. 初始化生成的因子（触发自动注册）
        std::cout << "\n2. 初始化生成的因子..." << std::endl;
        generated_factors::initialize_all_factors();

        // 验证因子注册状态
        auto& registry = factor_registry::get_instance();
        auto registered_factors = registry.get_registered_factors();
        std::cout << "✓ 总共注册了 " << registered_factors.size() << " 个因子" << std::endl;

        for (const auto& factor_name : registered_factors) {
            std::cout << "  - " << factor_name << std::endl;
        }

        // 3. 创建测试数据
        std::cout << "\n3. 创建测试数据..." << std::endl;
        auto data_interface = std::make_shared<data_interface>();
        data_interface->createStandardMarketData(120, 80);  // 120天，80只股票
        data_interface->printDataInfo();

        // 4. 创建因子管理器
        std::cout << "\n4. 创建因子管理器..." << std::endl;
        auto factor_manager = std::make_shared<factor_manager>();

        // 添加生成的因子到管理器（使用自动注册的因子）
        auto factor_names = generated_factors::get_all_generated_factor_names();
        auto factor_ids = generated_factors::get_all_generated_factor_ids();

        for (size_t i = 0; i < factor_names.size() && i < factor_ids.size(); ++i) {
            // 这里会使用已注册的因子工厂来创建因子实例
            if (factor_manager->add_factor(factor_ids[i], factor_names[i], "Auto-generated factor")) {
                std::cout << "  ✓ 添加因子: " << factor_names[i] << " (ID: " << factor_ids[i] << ")" << std::endl;
            } else {
                std::cout << "  ❌ 添加因子失败: " << factor_names[i] << std::endl;
            }
        }

        factor_manager->print_factor_info();

        // 5. 创建计算引擎
        std::cout << "\n5. 创建计算引擎..." << std::endl;
        FactorEngine engine(factor_manager, data_interface);
        engine.setNumThreads(4);
        engine.enablePerformanceStats(true);
        engine.enableVerboseLogging(false);

        engine.printEngineStatus();

        // 6. 验证计算环境
        std::cout << "\n6. 验证计算环境..." << std::endl;
        auto [valid, error_msg] = engine.validateEnvironment();
        if (valid) {
            std::cout << "✓ 计算环境验证通过" << std::endl;
        } else {
            std::cout << "❌ 计算环境验证失败: " << error_msg << std::endl;
            return 1;
        }

        // 7. 选择因子进行计算（这是使用者的主要操作）
        std::cout << "\n7. 选择因子进行计算..." << std::endl;

        // 使用者只需要选择想要计算的因子
        std::vector<int> selected_factor_ids = {0, 1, 2};  // 选择前3个因子
        factor_manager->select_factors(selected_factor_ids);

        factor_manager->print_selected_factor_info();

        // 8. 执行计算
        std::cout << "\n8. 执行因子计算..." << std::endl;
        engine.warmUp();

        auto start_time = std::chrono::high_resolution_clock::now();
        auto result = engine.calculate_selected_factors();
        auto end_time = std::chrono::high_resolution_clock::now();

        auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        // 9. 分析结果
        std::cout << "\n9. 分析计算结果..." << std::endl;
        if (result.success) {
            std::cout << "✅ 计算成功完成！" << std::endl;
            std::cout << "总耗时: " << total_duration.count() << " ms" << std::endl;
            std::cout << "计算因子数量: " << result.factor_results.size() << std::endl;
            std::cout << "数据规模: 120天 x 80股票 = 9,600个数据点/因子" << std::endl;

            // 详细分析每个因子的结果
            for (const auto& [factor_name, data] : result.factor_results) {
                std::cout << "\n📊 因子: " << factor_name << std::endl;
                std::cout << "   维度: " << data.rows() << "x" << data.cols() << std::endl;

                // 统计有效值
                int valid_count = 0;
                int nan_count = 0;
                double sum = 0.0;
                double min_val = std::numeric_limits<double>::max();
                double max_val = std::numeric_limits<double>::lowest();

                for (int i = 0; i < data.rows(); ++i) {
                    for (int j = 0; j < data.cols(); ++j) {
                        if (std::isfinite(data(i, j))) {
                            valid_count++;
                            sum += data(i, j);
                            min_val = std::min(min_val, data(i, j));
                            max_val = std::max(max_val, data(i, j));
                        } else {
                            nan_count++;
                        }
                    }
                }

                if (valid_count > 0) {
                    double mean = sum / valid_count;
                    std::cout << "   有效值: " << valid_count << " ("
                             << std::fixed << std::setprecision(1)
                             << (100.0 * valid_count / (data.rows() * data.cols())) << "%)" << std::endl;
                    std::cout << "   NaN值: " << nan_count << std::endl;
                    std::cout << "   均值: " << std::setprecision(6) << mean << std::endl;
                    std::cout << "   范围: [" << min_val << ", " << max_val << "]" << std::endl;
                } else {
                    std::cout << "   ❌ 所有值都是NaN" << std::endl;
                }
            }

            // 显示性能统计
            const auto& stats = engine.getLastPerformanceStats();
            std::cout << "\n⚡ 性能统计" << std::endl;
            std::cout << "总计算时间: " << stats.total_time.count() << " ms" << std::endl;
            std::cout << "计算因子数量: " << stats.factors_calculated << std::endl;
            std::cout << "处理数据点: " << stats.data_points_processed << std::endl;
            std::cout << "平均每因子时间: " << stats.getAverageTimePerFactor() << " ms" << std::endl;
            std::cout << "数据处理速度: " << stats.getDataProcessingRate() << " 点/秒" << std::endl;

        } else {
            std::cout << "❌ 计算失败: " << result.error_message << std::endl;
            return 1;
        }

        // 10. 演示不同的因子选择方式
        std::cout << "\n10. 演示不同的因子选择方式..." << std::endl;

        // 按名称选择
        std::cout << "\n按名称选择因子:" << std::endl;
        std::vector<std::string> selected_names = {"p1_corrs0", "p1_corrs2"};
        factor_manager->select_factors(selected_names);
        auto result2 = engine.calculate_selected_factors();
        std::cout << "✓ 按名称选择计算了 " << result2.factor_results.size() << " 个因子" << std::endl;

        // 选择单个因子
        std::cout << "\n选择单个因子:" << std::endl;
        auto single_result = engine.calculate_single_factor(1);
        std::cout << "✓ 单个因子计算了 " << single_result.factor_results.size() << " 个因子" << std::endl;

        // 11. 保存结果
        std::cout << "\n11. 保存计算结果..." << std::endl;
        std::string output_dir = "auto_registered_results";
        std::string mkdir_cmd = "mkdir -p " + output_dir;
        system(mkdir_cmd.c_str());

        for (const auto& [factor_name, data] : result.factor_results) {
            std::string filename = output_dir + "/" + factor_name + ".csv";
            std::ofstream file(filename);

            if (file.is_open()) {
                for (int i = 0; i < data.rows(); ++i) {
                    for (int j = 0; j < data.cols(); ++j) {
                        if (j > 0) file << ",";
                        if (std::isfinite(data(i, j))) {
                            file << std::fixed << std::setprecision(8) << data(i, j);
                        } else {
                            file << "NaN";
                        }
                    }
                    file << "\n";
                }
                file.close();
                std::cout << "  ✓ " << factor_name << " -> " << filename << std::endl;
            }
        }

        // 12. 运行基准测试
        std::cout << "\n12. 运行基准测试..." << std::endl;
        auto benchmark_result = framework_utils::runBenchmark(engine, {0, 1, 2}, 5);
        std::cout << benchmark_result << std::endl;

        // 13. 清理
        std::cout << "\n13. 清理资源..." << std::endl;
        FACTOR_FRAMEWORK_CLEANUP();

        std::cout << "\n🎉 自动注册因子演示完成！" << std::endl;
        std::cout << "📊 成功演示了正确的因子使用方式" << std::endl;
        std::cout << "⚡ 因子自动注册 + 用户选择计算" << std::endl;
        std::cout << "💾 结果已保存到 " << output_dir << "/ 目录" << std::endl;

        std::cout << "\n✨ 关键特性验证:" << std::endl;
        std::cout << "  ✓ 因子自动注册到FactorRegistry" << std::endl;
        std::cout << "  ✓ 用户通过FactorManager选择因子" << std::endl;
        std::cout << "  ✓ 支持按ID和名称选择" << std::endl;
        std::cout << "  ✓ 高性能并行计算" << std::endl;
        std::cout << "  ✓ 完整的性能监控" << std::endl;

        return 0;

    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
}
