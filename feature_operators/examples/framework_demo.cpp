/**
 * 因子框架演示程序
 *
 * 这个程序演示了因子框架的核心功能，不依赖自动生成的因子代码
 */

#include "factor_framework/factor_framework.hpp"
#include <iostream>
#include <iomanip>
#include <algorithm>

using namespace factor_framework;

// 简单的测试因子类
class TestCorrelationFactor : public FactorBase {
public:
    TestCorrelationFactor(int factor_id, const std::string& factor_name, const std::string& formula)
        : factor_base(factor_id, factor_name, formula) {}

    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override {

        const auto& close = get_field(data_map, "Close");
        const auto& volume = get_field(data_map, "Volume");

        // 使用feature_operators计算相关性
        return feature_operators::ts_Corr(close, volume, 20);
    }

    std::vector<std::string> get_required_fields() const override {
        return {"Close", "Volume"};
    }
};

class TestMeanFactor : public FactorBase {
public:
    TestMeanFactor(int factor_id, const std::string& factor_name, const std::string& formula)
        : factor_base(factor_id, factor_name, formula) {}

    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override {

        const auto& close = get_field(data_map, "Close");

        // 计算20日移动平均
        return feature_operators::ts_Mean(close, 20);
    }

    std::vector<std::string> get_required_fields() const override {
        return {"Close"};
    }
};

class TestStdevFactor : public FactorBase {
public:
    TestStdevFactor(int factor_id, const std::string& factor_name, const std::string& formula)
        : factor_base(factor_id, factor_name, formula) {}

    feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) override {

        const auto& close = get_field(data_map, "Close");

        // 计算20日标准差
        return feature_operators::ts_Stdev(close, 20);
    }

    std::vector<std::string> get_required_fields() const override {
        return {"Close"};
    }
};

// 因子工厂
class TestCorrelationFactorFactory : public FactorFactory {
public:
    std::unique_ptr<factor_base> create_factor(
        int factor_id,
        const std::string& factor_name,
        const std::string& formula) override {
        return std::make_unique<TestCorrelationFactor>(factor_id, factor_name, formula);
    }
};

class TestMeanFactorFactory : public FactorFactory {
public:
    std::unique_ptr<factor_base> create_factor(
        int factor_id,
        const std::string& factor_name,
        const std::string& formula) override {
        return std::make_unique<TestMeanFactor>(factor_id, factor_name, formula);
    }
};

class TestStdevFactorFactory : public FactorFactory {
public:
    std::unique_ptr<factor_base> create_factor(
        int factor_id,
        const std::string& factor_name,
        const std::string& formula) override {
        return std::make_unique<TestStdevFactor>(factor_id, factor_name, formula);
    }
};

// 注册测试因子
void registerTestFactors() {
    auto& registry = factor_registry::get_instance();

    registry.register_factory("test_corr", std::make_unique<TestCorrelationFactorFactory>());
    registry.register_factory("test_mean", std::make_unique<TestMeanFactorFactory>());
    registry.register_factory("test_stdev", std::make_unique<TestStdevFactorFactory>());

    std::cout << "✓ Test factors registered" << std::endl;
}

// 打印结果统计
void printResultStats(const std::unordered_map<std::string, feature_operators::DataFrame>& results) {
    std::cout << "\n=== 计算结果统计 ===" << std::endl;

    for (const auto& [factor_name, data] : results) {
        std::cout << "\n因子: " << factor_name << std::endl;
        std::cout << "  维度: " << data.rows() << "x" << data.cols() << std::endl;

        // 统计有效值
        int valid_count = 0;
        int nan_count = 0;
        double sum = 0.0;
        double min_val = std::numeric_limits<double>::max();
        double max_val = std::numeric_limits<double>::lowest();

        for (int i = 0; i < data.rows(); ++i) {
            for (int j = 0; j < data.cols(); ++j) {
                if (std::isfinite(data(i, j))) {
                    valid_count++;
                    sum += data(i, j);
                    min_val = std::min(min_val, data(i, j));
                    max_val = std::max(max_val, data(i, j));
                } else {
                    nan_count++;
                }
            }
        }

        std::cout << "  有效值: " << valid_count << std::endl;
        std::cout << "  NaN值: " << nan_count << std::endl;

        if (valid_count > 0) {
            double mean = sum / valid_count;
            std::cout << "  均值: " << std::fixed << std::setprecision(6) << mean << std::endl;
            std::cout << "  最小值: " << min_val << std::endl;
            std::cout << "  最大值: " << max_val << std::endl;

            // 显示最后几行的示例数据
            std::cout << "  最后5行示例:" << std::endl;
            for (int i = std::max(0, (int)data.rows() - 5); i < data.rows(); ++i) {
                std::cout << "    行" << i << ": ";
                for (int j = 0; j < std::min(5, (int)data.cols()); ++j) {
                    if (std::isfinite(data(i, j))) {
                        std::cout << std::setw(8) << std::fixed << std::setprecision(4) << data(i, j) << " ";
                    } else {
                        std::cout << "     NaN ";
                    }
                }
                if (data.cols() > 5) std::cout << "...";
                std::cout << std::endl;
            }
        }
    }
}

int main() {
    try {
        std::cout << "=== 因子框架演示程序 ===" << std::endl;
        std::cout << "框架版本: " << FACTOR_FRAMEWORK_VERSION << std::endl;

        // 1. 初始化框架
        std::cout << "\n1. 初始化框架..." << std::endl;
        FrameworkConfig config;
        config.default_num_threads = 4;
        config.enable_performance_stats = true;
        config.verbose_logging = true;

        FACTOR_FRAMEWORK_INIT_WITH_CONFIG(config);

        // 注册测试因子
        registerTestFactors();

        // 2. 创建数据接口并生成测试数据
        std::cout << "\n2. 创建测试数据..." << std::endl;
        auto data_interface = std::make_shared<data_interface>();
        data_interface->createStandardMarketData(100, 50);  // 100天，50只股票
        data_interface->printDataInfo();

        // 3. 创建因子管理器并添加测试因子
        std::cout << "\n3. 创建因子管理器..." << std::endl;
        auto factor_manager = std::make_shared<factor_manager>();

        // 手动添加测试因子
        factor_manager->add_factor(0, "test_corr", "ts_Corr(Close,Volume,20)");
        factor_manager->add_factor(1, "test_mean", "ts_Mean(Close,20)");
        factor_manager->add_factor(2, "test_stdev", "ts_Stdev(Close,20)");

        factor_manager->print_factor_info();

        // 4. 创建计算引擎
        std::cout << "\n4. 创建计算引擎..." << std::endl;
        FactorEngine engine(factor_manager, data_interface);
        engine.setNumThreads(4);
        engine.enablePerformanceStats(true);
        engine.enableVerboseLogging(true);

        engine.printEngineStatus();

        // 5. 验证环境
        std::cout << "\n5. 验证计算环境..." << std::endl;
        auto [valid, error_msg] = engine.validateEnvironment();
        if (valid) {
            std::cout << "✓ 计算环境验证通过" << std::endl;
        } else {
            std::cout << "❌ 计算环境验证失败: " << error_msg << std::endl;
            return 1;
        }

        // 6. 选择因子并计算
        std::cout << "\n6. 选择因子并执行计算..." << std::endl;
        factor_manager->select_factors({0, 1, 2});  // 选择所有测试因子
        factor_manager->print_selected_factor_info();

        // 预热引擎
        engine.warmUp();

        // 执行计算
        std::cout << "\n开始计算..." << std::endl;
        auto start_time = std::chrono::high_resolution_clock::now();

        auto result = engine.calculate_selected_factors();

        auto end_time = std::chrono::high_resolution_clock::now();
        auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        // 7. 处理结果
        std::cout << "\n7. 处理计算结果..." << std::endl;
        if (result.success) {
            std::cout << "✅ 计算成功完成！" << std::endl;
            std::cout << "总耗时: " << total_duration.count() << " ms" << std::endl;
            std::cout << "计算因子数量: " << result.factor_results.size() << std::endl;

            // 打印详细结果
            printResultStats(result.factor_results);

            // 显示性能统计
            const auto& stats = engine.getLastPerformanceStats();
            std::cout << "\n=== 性能统计 ===" << std::endl;
            std::cout << "总计算时间: " << stats.total_time.count() << " ms" << std::endl;
            std::cout << "计算因子数量: " << stats.factors_calculated << std::endl;
            std::cout << "处理数据点: " << stats.data_points_processed << std::endl;
            std::cout << "平均每因子时间: " << stats.getAverageTimePerFactor() << " ms" << std::endl;
            std::cout << "数据处理速度: " << stats.getDataProcessingRate() << " 点/秒" << std::endl;

            // 各因子计算时间
            std::cout << "\n各因子计算时间:" << std::endl;
            for (const auto& [factor_name, duration] : stats.factor_times) {
                std::cout << "  " << factor_name << ": " << duration.count() << " ms" << std::endl;
            }

        } else {
            std::cout << "❌ 计算失败: " << result.error_message << std::endl;
            return 1;
        }

        // 8. 运行基准测试
        std::cout << "\n8. 运行基准测试..." << std::endl;
        auto benchmark_result = framework_utils::runBenchmark(engine, {0, 1, 2}, 5);
        std::cout << benchmark_result << std::endl;

        // 9. 验证结果
        std::cout << "\n9. 验证计算结果..." << std::endl;
        bool validation_passed = framework_utils::validateFactorResults(engine, {0, 1, 2});
        std::cout << "结果验证: " << (validation_passed ? "✓ 通过" : "❌ 失败") << std::endl;

        // 10. 清理
        std::cout << "\n10. 清理资源..." << std::endl;
        FACTOR_FRAMEWORK_CLEANUP();

        std::cout << "\n🎉 演示程序执行完成！" << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
}
