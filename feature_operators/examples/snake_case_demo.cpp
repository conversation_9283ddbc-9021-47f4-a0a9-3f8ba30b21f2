/**
 * Snake Case 命名演示程序
 * 
 * 展示重构后的snake_case命名规范：
 * 1. 类名使用snake_case: factor_base, factor_manager, data_interface
 * 2. 函数名使用snake_case: get_name(), calculate(), register_factor()
 * 3. 变量名使用snake_case: factor_id, factor_name
 */

#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"
#include <iostream>
#include <iomanip>

using namespace factor_framework;

int main() {
    try {
        std::cout << "=== Snake Case 命名演示程序 ===" << std::endl;
        std::cout << "展示重构后的snake_case命名规范" << std::endl;
        std::cout << "框架版本: " << FACTOR_FRAMEWORK_VERSION << std::endl;
        
        // 1. 初始化框架
        std::cout << "\n1. 初始化因子框架..." << std::endl;
        FACTOR_FRAMEWORK_INIT();
        
        // 2. 初始化生成的因子库
        std::cout << "\n2. 初始化生成的因子库..." << std::endl;
        generated_factors::initialize_all_factors();
        
        // 3. 创建测试数据 - 使用snake_case类名
        std::cout << "\n3. 创建测试数据..." << std::endl;
        auto data_interface = std::make_shared<factor_framework::data_interface>();
        data_interface->createStandardMarketData(60, 30);  // 60天，30只股票
        
        // 4. 创建因子管理器 - 使用snake_case类名
        std::cout << "\n4. 创建因子管理器..." << std::endl;
        auto factor_manager = std::make_shared<factor_framework::factor_manager>();
        
        // 5. 批量注册生成的因子 - 使用snake_case函数名
        std::cout << "\n5. 批量注册生成的因子..." << std::endl;
        int registered_count = generated_factors::register_all_factors_to_manager(factor_manager);
        
        if (registered_count == 0) {
            std::cout << "❌ 没有注册任何因子" << std::endl;
            return 1;
        }
        
        // 6. 展示snake_case方法的使用
        std::cout << "\n6. 展示snake_case方法的使用..." << std::endl;
        
        // 获取所有因子并展示其属性
        auto all_factors = factor_manager->get_all_factors();
        std::cout << "注册的因子列表:" << std::endl;
        
        for (const auto& [factor_id, factor] : all_factors) {
            std::cout << "  因子ID: " << factor->get_id() << std::endl;
            std::cout << "  因子名称: " << factor->get_name() << std::endl;
            std::cout << "  因子公式: " << factor->get_formula() << std::endl;
            
            // 使用snake_case方法名
            auto required_fields = factor->get_required_fields();
            std::cout << "  所需字段: ";
            for (size_t i = 0; i < required_fields.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << required_fields[i];
            }
            std::cout << std::endl;
            std::cout << "  ---" << std::endl;
        }
        
        // 7. 演示因子选择 - 使用snake_case方法名
        std::cout << "\n7. 演示因子选择..." << std::endl;
        
        // 按ID选择因子
        std::vector<int> selected_ids = {0, 1};
        factor_manager->select_factors(selected_ids);
        std::cout << "✓ 按ID选择了因子: ";
        for (size_t i = 0; i < selected_ids.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << selected_ids[i];
        }
        std::cout << std::endl;
        
        // 获取选中的因子信息
        auto selected_factors = factor_manager->get_selected_factors();
        std::cout << "选中的因子详情:" << std::endl;
        for (const auto& factor : selected_factors) {
            std::cout << "  - " << factor->get_name() << " (ID: " << factor->get_id() << ")" << std::endl;
        }
        
        // 8. 演示数据接口的snake_case方法
        std::cout << "\n8. 演示数据接口的snake_case方法..." << std::endl;
        
        // 获取数据信息
        auto data_info = data_interface->get_data_info();
        std::cout << "数据信息:" << std::endl;
        for (const auto& [field_name, info] : data_info) {
            std::cout << "  字段: " << field_name << std::endl;
            std::cout << "    维度: " << info.rows << "x" << info.cols << std::endl;
            std::cout << "    有效值: " << info.valid_count << std::endl;
            std::cout << "    NaN值: " << info.nan_count << std::endl;
        }
        
        // 验证数据维度
        if (data_interface->validate_data_dimensions()) {
            std::cout << "✓ 数据维度验证通过" << std::endl;
        } else {
            std::cout << "❌ 数据维度验证失败" << std::endl;
        }
        
        // 9. 演示因子计算（如果可能）
        std::cout << "\n9. 演示因子计算..." << std::endl;
        
        try {
            // 获取数据映射
            const auto& data_map = data_interface->getAllData();
            
            // 计算第一个选中的因子
            if (!selected_factors.empty()) {
                auto first_factor = selected_factors[0];
                std::cout << "计算因子: " << first_factor->get_name() << std::endl;
                
                auto start_time = std::chrono::high_resolution_clock::now();
                auto result = first_factor->calculate(data_map);
                auto end_time = std::chrono::high_resolution_clock::now();
                
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
                
                std::cout << "✓ 计算完成，耗时: " << duration.count() << " ms" << std::endl;
                std::cout << "  结果维度: " << result.rows() << "x" << result.cols() << std::endl;
                
                // 统计结果
                int valid_count = 0;
                int nan_count = 0;
                for (int i = 0; i < result.rows(); ++i) {
                    for (int j = 0; j < result.cols(); ++j) {
                        if (std::isfinite(result(i, j))) {
                            valid_count++;
                        } else {
                            nan_count++;
                        }
                    }
                }
                
                std::cout << "  有效值: " << valid_count << std::endl;
                std::cout << "  NaN值: " << nan_count << std::endl;
            }
            
        } catch (const std::exception& e) {
            std::cout << "计算过程中出现错误: " << e.what() << std::endl;
        }
        
        // 10. 展示命名规范总结
        std::cout << "\n10. Snake Case 命名规范总结..." << std::endl;
        std::cout << "🎯 类名 (snake_case):" << std::endl;
        std::cout << "  ✓ factor_base (原 FactorBase)" << std::endl;
        std::cout << "  ✓ factor_manager (原 FactorManager)" << std::endl;
        std::cout << "  ✓ data_interface (原 DataInterface)" << std::endl;
        std::cout << "  ✓ factor_registry (原 FactorRegistry)" << std::endl;
        
        std::cout << "\n🎯 方法名 (snake_case):" << std::endl;
        std::cout << "  ✓ get_id() (原 getId())" << std::endl;
        std::cout << "  ✓ get_name() (原 getName())" << std::endl;
        std::cout << "  ✓ get_formula() (原 getFormula())" << std::endl;
        std::cout << "  ✓ get_required_fields() (原 getRequiredFields())" << std::endl;
        std::cout << "  ✓ register_factor() (原 registerFactor())" << std::endl;
        std::cout << "  ✓ select_factors() (原 selectFactors())" << std::endl;
        std::cout << "  ✓ validate_data_dimensions() (原 validateDataDimensions())" << std::endl;
        
        std::cout << "\n🎯 函数名 (snake_case):" << std::endl;
        std::cout << "  ✓ initialize_all_factors() (原 initializeAllFactors())" << std::endl;
        std::cout << "  ✓ register_all_factors_to_manager() (原 registerAllFactorsToManager())" << std::endl;
        std::cout << "  ✓ get_all_generated_factor_names() (原 getAllGeneratedFactorNames())" << std::endl;
        
        std::cout << "\n🎯 优势:" << std::endl;
        std::cout << "  ✅ 符合C++标准库命名风格" << std::endl;
        std::cout << "  ✅ 提高代码可读性" << std::endl;
        std::cout << "  ✅ 统一的命名规范" << std::endl;
        std::cout << "  ✅ 更好的代码一致性" << std::endl;
        
        // 11. 清理
        std::cout << "\n11. 清理资源..." << std::endl;
        FACTOR_FRAMEWORK_CLEANUP();
        
        std::cout << "\n🎉 Snake Case 命名演示完成！" << std::endl;
        std::cout << "📊 成功演示了重构后的命名规范" << std::endl;
        std::cout << "⚡ 代码更加规范和一致" << std::endl;
        std::cout << "🚀 符合现代C++最佳实践" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
}

/*
=== Snake Case 命名规范示例 ===

// 类名使用snake_case
class factor_base {
public:
    // 方法名使用snake_case
    int get_id() const;
    const std::string& get_name() const;
    std::vector<std::string> get_required_fields() const;
    
    // 虚函数也使用snake_case
    virtual feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) = 0;
};

class factor_manager {
public:
    // 方法名使用snake_case
    bool register_factor(std::unique_ptr<factor_base> factor);
    void select_factors(const std::vector<int>& factor_ids);
    std::vector<std::shared_ptr<factor_base>> get_selected_factors() const;
};

// 函数名使用snake_case
void initialize_all_factors();
int register_all_factors_to_manager(std::shared_ptr<factor_manager> manager);
std::vector<std::string> get_all_generated_factor_names();

这种命名风格更加一致和现代化！
*/
