/**
 * 生成因子库演示程序
 *
 * 这个程序演示如何使用自动生成的因子库进行实际的因子计算
 * 使用 generated_factors_full 中的10个真实因子
 */

#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <fstream>

using namespace factor_framework;

// 创建更大规模的测试数据
std::shared_ptr<data_interface> createLargeTestData(int days = 252, int stocks = 100) {
    auto data_interface = std::make_shared<data_interface>();

    std::cout << "创建大规模测试数据: " << days << "天 x " << stocks << "只股票" << std::endl;

    // 创建标准市场数据
    data_interface->createStandardMarketData(days, stocks, 1.0);

    std::cout << "✓ 大规模测试数据创建完成" << std::endl;
    return data_interface;
}

// 分析因子计算结果
void analyzeFactorResults(const std::unordered_map<std::string, feature_operators::DataFrame>& results) {
    std::cout << "\n=== 详细因子分析 ===" << std::endl;

    for (const auto& [factor_name, data] : results) {
        std::cout << "\n📊 因子: " << factor_name << std::endl;
        std::cout << "   维度: " << data.rows() << "x" << data.cols() << std::endl;

        // 统计有效值和NaN值
        int valid_count = 0;
        int nan_count = 0;
        double sum = 0.0;
        double sum_sq = 0.0;
        double min_val = std::numeric_limits<double>::max();
        double max_val = std::numeric_limits<double>::lowest();

        for (int i = 0; i < data.rows(); ++i) {
            for (int j = 0; j < data.cols(); ++j) {
                if (std::isfinite(data(i, j))) {
                    valid_count++;
                    double val = data(i, j);
                    sum += val;
                    sum_sq += val * val;
                    min_val = std::min(min_val, val);
                    max_val = std::max(max_val, val);
                } else {
                    nan_count++;
                }
            }
        }

        if (valid_count > 0) {
            double mean = sum / valid_count;
            double variance = (sum_sq / valid_count) - (mean * mean);
            double std_dev = std::sqrt(std::max(0.0, variance));

            std::cout << "   有效值: " << valid_count << " ("
                     << std::fixed << std::setprecision(1)
                     << (100.0 * valid_count / (data.rows() * data.cols())) << "%)" << std::endl;
            std::cout << "   NaN值: " << nan_count << std::endl;
            std::cout << "   统计信息:" << std::endl;
            std::cout << "     均值: " << std::setprecision(6) << mean << std::endl;
            std::cout << "     标准差: " << std_dev << std::endl;
            std::cout << "     最小值: " << min_val << std::endl;
            std::cout << "     最大值: " << max_val << std::endl;

            // 计算分位数（简化版本）
            std::vector<double> valid_values;
            valid_values.reserve(valid_count);
            for (int i = 0; i < data.rows(); ++i) {
                for (int j = 0; j < data.cols(); ++j) {
                    if (std::isfinite(data(i, j))) {
                        valid_values.push_back(data(i, j));
                    }
                }
            }

            if (valid_values.size() > 10) {
                std::sort(valid_values.begin(), valid_values.end());
                size_t p25_idx = valid_values.size() / 4;
                size_t p50_idx = valid_values.size() / 2;
                size_t p75_idx = valid_values.size() * 3 / 4;

                std::cout << "     25%分位: " << valid_values[p25_idx] << std::endl;
                std::cout << "     50%分位: " << valid_values[p50_idx] << std::endl;
                std::cout << "     75%分位: " << valid_values[p75_idx] << std::endl;
            }

            // 显示最后几行的示例数据
            std::cout << "   最后3行示例:" << std::endl;
            for (int i = std::max(0, (int)data.rows() - 3); i < data.rows(); ++i) {
                std::cout << "     行" << i << ": ";
                for (int j = 0; j < std::min(5, (int)data.cols()); ++j) {
                    if (std::isfinite(data(i, j))) {
                        std::cout << std::setw(8) << std::fixed << std::setprecision(4) << data(i, j) << " ";
                    } else {
                        std::cout << "     NaN ";
                    }
                }
                if (data.cols() > 5) std::cout << "...";
                std::cout << std::endl;
            }
        } else {
            std::cout << "   ❌ 所有值都是NaN" << std::endl;
        }
    }
}

// 保存结果到CSV文件
bool saveResultsToCSV(const std::unordered_map<std::string, feature_operators::DataFrame>& results,
                     const std::string& output_dir) {
    std::cout << "\n💾 保存计算结果到: " << output_dir << std::endl;

    // 创建输出目录
    std::string mkdir_cmd = "mkdir -p " + output_dir;
    system(mkdir_cmd.c_str());

    for (const auto& [factor_name, data] : results) {
        std::string filename = output_dir + "/" + factor_name + ".csv";
        std::ofstream file(filename);

        if (!file.is_open()) {
            std::cerr << "❌ 无法创建文件: " << filename << std::endl;
            continue;
        }

        // 写入CSV数据
        for (int i = 0; i < data.rows(); ++i) {
            for (int j = 0; j < data.cols(); ++j) {
                if (j > 0) file << ",";
                if (std::isfinite(data(i, j))) {
                    file << std::fixed << std::setprecision(8) << data(i, j);
                } else {
                    file << "NaN";
                }
            }
            file << "\n";
        }

        file.close();
        std::cout << "   ✓ " << factor_name << " -> " << filename << std::endl;
    }

    return true;
}

// 因子相关性分析
void analyzeFactorCorrelations(const std::unordered_map<std::string, feature_operators::DataFrame>& results) {
    std::cout << "\n🔗 因子相关性分析" << std::endl;

    std::vector<std::string> factor_names;
    std::vector<std::vector<double>> factor_data;

    // 收集所有因子的最后一行数据（作为截面数据）
    for (const auto& [name, data] : results) {
        if (data.rows() > 0 && data.cols() > 0) {
            factor_names.push_back(name);
            std::vector<double> last_row_data;

            for (int j = 0; j < data.cols(); ++j) {
                if (std::isfinite(data(data.rows()-1, j))) {
                    last_row_data.push_back(data(data.rows()-1, j));
                }
            }

            if (!last_row_data.empty()) {
                factor_data.push_back(last_row_data);
            }
        }
    }

    if (factor_data.size() < 2) {
        std::cout << "   数据不足，无法进行相关性分析" << std::endl;
        return;
    }

    std::cout << "   分析 " << factor_names.size() << " 个因子的相关性:" << std::endl;

    // 计算相关性矩阵（简化版本）
    for (size_t i = 0; i < factor_names.size(); ++i) {
        for (size_t j = i + 1; j < factor_names.size(); ++j) {
            const auto& data1 = factor_data[i];
            const auto& data2 = factor_data[j];

            size_t min_size = std::min(data1.size(), data2.size());
            if (min_size < 10) continue;

            // 计算皮尔逊相关系数
            double sum1 = 0, sum2 = 0, sum1_sq = 0, sum2_sq = 0, sum12 = 0;
            for (size_t k = 0; k < min_size; ++k) {
                sum1 += data1[k];
                sum2 += data2[k];
                sum1_sq += data1[k] * data1[k];
                sum2_sq += data2[k] * data2[k];
                sum12 += data1[k] * data2[k];
            }

            double mean1 = sum1 / min_size;
            double mean2 = sum2 / min_size;
            double var1 = sum1_sq / min_size - mean1 * mean1;
            double var2 = sum2_sq / min_size - mean2 * mean2;
            double cov = sum12 / min_size - mean1 * mean2;

            if (var1 > 1e-10 && var2 > 1e-10) {
                double corr = cov / std::sqrt(var1 * var2);
                if (std::abs(corr) > 0.1) {  // 只显示相关性较强的
                    std::cout << "     " << factor_names[i] << " vs " << factor_names[j]
                             << ": " << std::fixed << std::setprecision(3) << corr << std::endl;
                }
            }
        }
    }
}

int main() {
    try {
        std::cout << "=== 生成因子库演示程序 ===" << std::endl;
        std::cout << "使用自动生成的10个真实因子进行计算" << std::endl;
        std::cout << "框架版本: " << FACTOR_FRAMEWORK_VERSION << std::endl;

        // 1. 初始化框架
        std::cout << "\n1. 初始化因子框架..." << std::endl;
        FrameworkConfig config;
        config.default_num_threads = 8;  // 使用更多线程
        config.enable_performance_stats = true;
        config.verbose_logging = false;  // 减少日志输出

        FACTOR_FRAMEWORK_INIT_WITH_CONFIG(config);

        // 手动注册生成的因子（避免复杂的代码生成问题）
        auto& registry = factor_framework::factor_registry::get_instance();

        // 创建简单的因子工厂
        class SimpleFactorFactory : public factor_framework::FactorFactory {
        public:
            std::unique_ptr<factor_framework::factor_base> create_factor(
                int factor_id,
                const std::string& factor_name,
                const std::string& formula) override {

                // 根据因子名称创建对应的因子实例
                if (factor_name == "p1_corrs0") {
                    return std::make_unique<generated_factors::FactorP1corrs0>(factor_id, factor_name, formula);
                } else if (factor_name == "p1_corrs1") {
                    return std::make_unique<generated_factors::FactorP1corrs1>(factor_id, factor_name, formula);
                } else if (factor_name == "p1_corrs2") {
                    return std::make_unique<generated_factors::FactorP1corrs2>(factor_id, factor_name, formula);
                } else if (factor_name == "p1_corrs3") {
                    return std::make_unique<generated_factors::FactorP1corrs3>(factor_id, factor_name, formula);
                } else if (factor_name == "p1_corrs4") {
                    return std::make_unique<generated_factors::FactorP1corrs4>(factor_id, factor_name, formula);
                }

                throw std::runtime_error("Unknown factor: " + factor_name);
            }
        };

        // 注册因子工厂
        auto factory = std::make_unique<SimpleFactorFactory>();
        registry.register_factory("p1_corrs0", std::make_unique<SimpleFactorFactory>());
        registry.register_factory("p1_corrs1", std::make_unique<SimpleFactorFactory>());
        registry.register_factory("p1_corrs2", std::make_unique<SimpleFactorFactory>());
        registry.register_factory("p1_corrs3", std::make_unique<SimpleFactorFactory>());
        registry.register_factory("p1_corrs4", std::make_unique<SimpleFactorFactory>());

        std::cout << "✓ 生成因子库初始化完成" << std::endl;

        // 2. 创建大规模测试数据
        std::cout << "\n2. 创建测试数据..." << std::endl;
        auto data_interface = createLargeTestData(252, 100);  // 一年数据，100只股票
        data_interface->printDataInfo();

        // 3. 创建因子管理器并加载生成的因子
        std::cout << "\n3. 加载生成的因子..." << std::endl;
        auto factor_manager = std::make_shared<factor_manager>();

        // 从CSV加载因子配置
        if (factor_manager->load_factors_from_csv("../feature.csv")) {
            std::cout << "✓ 从CSV成功加载因子配置" << std::endl;
        } else {
            std::cout << "⚠ CSV加载失败，手动添加生成的因子" << std::endl;

            // 手动添加生成的因子
            std::vector<std::string> factor_names = {"p1_corrs0", "p1_corrs1", "p1_corrs2", "p1_corrs3", "p1_corrs4"};
            std::vector<int> factor_ids = {0, 1, 2, 3, 4};

            for (size_t i = 0; i < factor_names.size() && i < factor_ids.size(); ++i) {
                std::string formula = "Generated factor: " + factor_names[i];
                factor_manager->add_factor(factor_ids[i], factor_names[i], formula);
            }
        }

        factor_manager->print_factor_info();

        // 4. 创建高性能计算引擎
        std::cout << "\n4. 创建计算引擎..." << std::endl;
        FactorEngine engine(factor_manager, data_interface);
        engine.setNumThreads(8);
        engine.enablePerformanceStats(true);
        engine.enableVerboseLogging(false);

        engine.printEngineStatus();

        // 5. 验证计算环境
        std::cout << "\n5. 验证计算环境..." << std::endl;
        auto [valid, error_msg] = engine.validateEnvironment();
        if (valid) {
            std::cout << "✓ 计算环境验证通过" << std::endl;
        } else {
            std::cout << "❌ 计算环境验证失败: " << error_msg << std::endl;
            return 1;
        }

        // 6. 选择并计算生成的因子
        std::cout << "\n6. 计算生成的因子..." << std::endl;

        // 获取所有生成的因子ID
        std::vector<int> generated_ids = {0, 1, 2, 3, 4};
        std::cout << "发现 " << generated_ids.size() << " 个生成的因子" << std::endl;

        // 选择前8个因子进行计算
        std::vector<int> selected_ids;
        for (size_t i = 0; i < std::min(size_t(8), generated_ids.size()); ++i) {
            selected_ids.push_back(generated_ids[i]);
        }

        factor_manager->select_factors(selected_ids);
        factor_manager->print_selected_factor_info();

        // 预热引擎
        engine.warmUp();

        // 执行计算
        std::cout << "\n🚀 开始大规模因子计算..." << std::endl;
        auto start_time = std::chrono::high_resolution_clock::now();

        auto result = engine.calculate_selected_factors();

        auto end_time = std::chrono::high_resolution_clock::now();
        auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        // 7. 分析计算结果
        std::cout << "\n7. 分析计算结果..." << std::endl;
        if (result.success) {
            std::cout << "✅ 大规模因子计算成功完成！" << std::endl;
            std::cout << "总耗时: " << total_duration.count() << " ms" << std::endl;
            std::cout << "计算因子数量: " << result.factor_results.size() << std::endl;
            std::cout << "数据规模: 252天 x 100股票 = 25,200个数据点/因子" << std::endl;

            // 详细分析结果
            analyzeFactorResults(result.factor_results);

            // 显示性能统计
            const auto& stats = engine.getLastPerformanceStats();
            std::cout << "\n⚡ 性能统计" << std::endl;
            std::cout << "总计算时间: " << stats.total_time.count() << " ms" << std::endl;
            std::cout << "计算因子数量: " << stats.factors_calculated << std::endl;
            std::cout << "处理数据点: " << stats.data_points_processed << std::endl;
            std::cout << "平均每因子时间: " << stats.getAverageTimePerFactor() << " ms" << std::endl;
            std::cout << "数据处理速度: " << stats.getDataProcessingRate() << " 点/秒" << std::endl;

            // 各因子计算时间
            std::cout << "\n各因子计算时间:" << std::endl;
            for (const auto& [factor_name, duration] : stats.factor_times) {
                std::cout << "  " << factor_name << ": " << duration.count() << " ms" << std::endl;
            }

            // 因子相关性分析
            analyzeFactorCorrelations(result.factor_results);

            // 保存结果
            saveResultsToCSV(result.factor_results, "factor_results");

        } else {
            std::cout << "❌ 计算失败: " << result.error_message << std::endl;
            return 1;
        }

        // 8. 运行基准测试
        std::cout << "\n8. 运行基准测试..." << std::endl;
        auto benchmark_result = framework_utils::runBenchmark(engine, selected_ids, 3);
        std::cout << benchmark_result << std::endl;

        // 9. 验证结果质量
        std::cout << "\n9. 验证结果质量..." << std::endl;
        bool validation_passed = framework_utils::validateFactorResults(engine, selected_ids);
        std::cout << "结果验证: " << (validation_passed ? "✓ 通过" : "❌ 失败") << std::endl;

        // 10. 清理
        std::cout << "\n10. 清理资源..." << std::endl;
        FACTOR_FRAMEWORK_CLEANUP();

        std::cout << "\n🎉 生成因子库演示完成！" << std::endl;
        std::cout << "📊 成功计算了 " << result.factor_results.size() << " 个真实因子" << std::endl;
        std::cout << "⚡ 处理了 " << (252 * 100 * result.factor_results.size()) << " 个数据点" << std::endl;
        std::cout << "💾 结果已保存到 factor_results/ 目录" << std::endl;

        return 0;

    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
}
