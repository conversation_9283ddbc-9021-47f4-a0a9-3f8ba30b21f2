/**
 * 正确使用方式演示程序
 * 
 * 展示最简洁的因子使用方式：
 * 1. 因子自动注册到FactorRegistry
 * 2. FactorManager自动从Registry加载因子
 * 3. 用户只需选择和计算
 */

#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"
#include <iostream>
#include <iomanip>

using namespace factor_framework;

int main() {
    try {
        std::cout << "=== 正确使用方式演示程序 ===" << std::endl;
        std::cout << "展示最简洁的因子使用方式" << std::endl;
        std::cout << "框架版本: " << FACTOR_FRAMEWORK_VERSION << std::endl;
        
        // 1. 初始化框架
        std::cout << "\n1. 初始化因子框架..." << std::endl;
        FACTOR_FRAMEWORK_INIT();
        
        // 2. 初始化生成的因子（触发自动注册）
        std::cout << "\n2. 初始化生成的因子..." << std::endl;
        generated_factors::initializeAllFactors();
        
        // 3. 创建测试数据
        std::cout << "\n3. 创建测试数据..." << std::endl;
        auto data_interface = std::make_shared<DataInterface>();
        data_interface->createStandardMarketData(100, 60);  // 100天，60只股票
        
        // 4. 创建因子管理器并自动加载已注册的因子
        std::cout << "\n4. 自动加载已注册的因子..." << std::endl;
        auto factor_manager = std::make_shared<FactorManager>();
        
        // 🎯 关键改进：直接从Registry自动加载，无需手动添加！
        int loaded_count = factor_manager->loadAllRegisteredFactors();
        
        if (loaded_count == 0) {
            std::cout << "❌ 没有加载到任何因子" << std::endl;
            return 1;
        }
        
        factor_manager->printFactorInfo();
        
        // 5. 创建计算引擎
        std::cout << "\n5. 创建计算引擎..." << std::endl;
        FactorEngine engine(factor_manager, data_interface);
        engine.setNumThreads(4);
        engine.enablePerformanceStats(true);
        
        // 6. 用户的主要操作：选择要计算的因子
        std::cout << "\n6. 选择要计算的因子..." << std::endl;
        
        // 方式1：按ID选择
        std::cout << "\n方式1：按ID选择因子" << std::endl;
        factor_manager->selectFactors({0, 1, 2});
        auto result1 = engine.calculateSelectedFactors();
        std::cout << "✓ 按ID选择计算了 " << result1.factor_results.size() << " 个因子" << std::endl;
        
        // 方式2：按名称选择
        std::cout << "\n方式2：按名称选择因子" << std::endl;
        std::vector<std::string> selected_names = {"p1_corrs0", "p1_corrs1"};
        factor_manager->selectFactors(selected_names);
        auto result2 = engine.calculateSelectedFactors();
        std::cout << "✓ 按名称选择计算了 " << result2.factor_results.size() << " 个因子" << std::endl;
        
        // 方式3：计算单个因子
        std::cout << "\n方式3：计算单个因子" << std::endl;
        auto result3 = engine.calculateSingleFactor(0);
        std::cout << "✓ 单个因子计算了 " << result3.factor_results.size() << " 个因子" << std::endl;
        
        // 7. 分析最终结果
        std::cout << "\n7. 分析计算结果..." << std::endl;
        
        if (result1.success) {
            std::cout << "✅ 计算成功！" << std::endl;
            std::cout << "数据规模: 100天 x 60股票 = 6,000个数据点/因子" << std::endl;
            
            for (const auto& [factor_name, data] : result1.factor_results) {
                std::cout << "\n📊 因子: " << factor_name << std::endl;
                std::cout << "   维度: " << data.rows() << "x" << data.cols() << std::endl;
                
                // 统计有效值
                int valid_count = 0;
                int nan_count = 0;
                double sum = 0.0;
                
                for (int i = 0; i < data.rows(); ++i) {
                    for (int j = 0; j < data.cols(); ++j) {
                        if (std::isfinite(data(i, j))) {
                            valid_count++;
                            sum += data(i, j);
                        } else {
                            nan_count++;
                        }
                    }
                }
                
                if (valid_count > 0) {
                    double mean = sum / valid_count;
                    std::cout << "   有效值: " << valid_count << " (" 
                             << std::fixed << std::setprecision(1) 
                             << (100.0 * valid_count / (data.rows() * data.cols())) << "%)" << std::endl;
                    std::cout << "   NaN值: " << nan_count << std::endl;
                    std::cout << "   均值: " << std::setprecision(6) << mean << std::endl;
                }
            }
        } else {
            std::cout << "❌ 计算失败: " << result1.error_message << std::endl;
        }
        
        // 8. 展示使用的简洁性
        std::cout << "\n8. 使用方式总结..." << std::endl;
        std::cout << "✨ 用户只需要做的事情：" << std::endl;
        std::cout << "  1. 初始化框架和生成的因子" << std::endl;
        std::cout << "  2. 创建数据和管理器" << std::endl;
        std::cout << "  3. 自动加载已注册的因子" << std::endl;
        std::cout << "  4. 选择要计算的因子" << std::endl;
        std::cout << "  5. 执行计算" << std::endl;
        std::cout << "\n🚫 用户不需要做的事情：" << std::endl;
        std::cout << "  ❌ 手动注册因子" << std::endl;
        std::cout << "  ❌ 手动添加因子到管理器" << std::endl;
        std::cout << "  ❌ 管理因子工厂" << std::endl;
        std::cout << "  ❌ 处理因子实例化" << std::endl;
        
        // 9. 性能基准测试
        std::cout << "\n9. 性能基准测试..." << std::endl;
        auto benchmark_result = FrameworkUtils::runBenchmark(engine, {0, 1, 2}, 10);
        std::cout << benchmark_result << std::endl;
        
        // 10. 清理
        std::cout << "\n10. 清理资源..." << std::endl;
        FACTOR_FRAMEWORK_CLEANUP();
        
        std::cout << "\n🎉 正确使用方式演示完成！" << std::endl;
        std::cout << "📝 关键要点：" << std::endl;
        std::cout << "  ✅ 因子自动注册，无需手动操作" << std::endl;
        std::cout << "  ✅ 管理器自动加载，一行代码搞定" << std::endl;
        std::cout << "  ✅ 用户只需选择和计算" << std::endl;
        std::cout << "  ✅ 支持多种选择方式" << std::endl;
        std::cout << "  ✅ 高性能并行计算" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
}

/*
=== 完整的用户代码示例 ===

#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"

int main() {
    // 1. 初始化
    FACTOR_FRAMEWORK_INIT();
    generated_factors::initializeAllFactors();
    
    // 2. 创建组件
    auto data_interface = std::make_shared<DataInterface>();
    auto factor_manager = std::make_shared<FactorManager>();
    
    // 3. 加载数据和因子
    data_interface->createStandardMarketData(100, 50);
    factor_manager->loadAllRegisteredFactors();  // 🎯 一行代码自动加载所有因子
    
    // 4. 创建引擎
    FactorEngine engine(factor_manager, data_interface);
    
    // 5. 选择和计算（用户的主要操作）
    factor_manager->selectFactors({0, 1, 2});     // 按ID选择
    auto result = engine.calculateSelectedFactors();
    
    // 6. 使用结果
    if (result.success) {
        for (const auto& [name, data] : result.factor_results) {
            // 处理计算结果...
        }
    }
    
    return 0;
}

这就是全部！用户代码非常简洁，无需手动注册或添加因子。
*/
