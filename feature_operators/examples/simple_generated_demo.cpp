/**
 * 简化的生成因子演示程序
 *
 * 直接使用生成的因子类，不依赖复杂的注册机制
 */

#include "factor_framework/factor_framework.hpp"
#include "generated_factors/factorp1corrs0.hpp"
#include "generated_factors/factorp1corrs1.hpp"
#include "generated_factors/factorp1corrs2.hpp"
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <fstream>

using namespace factor_framework;

int main() {
    try {
        std::cout << "=== 简化生成因子演示程序 ===" << std::endl;
        std::cout << "直接使用生成的因子类进行计算" << std::endl;
        std::cout << "框架版本: " << FACTOR_FRAMEWORK_VERSION << std::endl;

        // 1. 初始化框架
        std::cout << "\n1. 初始化因子框架..." << std::endl;
        FACTOR_FRAMEWORK_INIT();

        // 2. 创建测试数据
        std::cout << "\n2. 创建测试数据..." << std::endl;
        auto data_interface = std::make_shared<data_interface>();
        data_interface->createStandardMarketData(100, 50);  // 100天，50只股票
        data_interface->printDataInfo();

        // 3. 直接创建生成的因子实例
        std::cout << "\n3. 创建生成的因子实例..." << std::endl;

        std::vector<std::unique_ptr<factor_base>> factors;
        factors.push_back(std::make_unique<generated_factors::FactorP1corrs0>(0, "p1_corrs0", "ts_Corr(Close,Volume,60)"));
        factors.push_back(std::make_unique<generated_factors::FactorP1corrs1>(1, "p1_corrs1", "ts_Corr(Close/ts_Delay(Close,1)-1,Volume,60)"));
        factors.push_back(std::make_unique<generated_factors::FactorP1corrs2>(2, "p1_corrs2", "ts_Corr(ts_Delay(Close,1),Volume,60)"));

        std::cout << "✓ 创建了 " << factors.size() << " 个生成的因子" << std::endl;

        // 4. 获取数据映射
        std::cout << "\n4. 准备数据..." << std::endl;
        const auto& data_map = data_interface->getAllData();

        // 验证数据
        if (!data_interface->validateDataDimensions()) {
            std::cout << "❌ 数据维度不一致" << std::endl;
            return 1;
        }

        std::cout << "✓ 数据验证通过" << std::endl;

        // 5. 逐个计算因子
        std::cout << "\n5. 计算生成的因子..." << std::endl;

        std::unordered_map<std::string, feature_operators::DataFrame> results;

        for (const auto& factor : factors) {
            try {
                std::cout << "计算因子: " << factor->get_name() << std::endl;

                auto start_time = std::chrono::high_resolution_clock::now();
                auto result = factor->calculate(data_map);
                auto end_time = std::chrono::high_resolution_clock::now();

                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

                results[factor->get_name()] = std::move(result);

                std::cout << "  ✓ 完成，耗时: " << duration.count() << " ms" << std::endl;

            } catch (const std::exception& e) {
                std::cout << "  ❌ 计算失败: " << e.what() << std::endl;
            }
        }

        // 6. 分析结果
        std::cout << "\n6. 分析计算结果..." << std::endl;

        if (results.empty()) {
            std::cout << "❌ 没有成功计算的因子" << std::endl;
            return 1;
        }

        std::cout << "✅ 成功计算了 " << results.size() << " 个因子" << std::endl;

        for (const auto& [factor_name, data] : results) {
            std::cout << "\n📊 因子: " << factor_name << std::endl;
            std::cout << "   维度: " << data.rows() << "x" << data.cols() << std::endl;

            // 统计有效值
            int valid_count = 0;
            int nan_count = 0;
            double sum = 0.0;
            double min_val = std::numeric_limits<double>::max();
            double max_val = std::numeric_limits<double>::lowest();

            for (int i = 0; i < data.rows(); ++i) {
                for (int j = 0; j < data.cols(); ++j) {
                    if (std::isfinite(data(i, j))) {
                        valid_count++;
                        sum += data(i, j);
                        min_val = std::min(min_val, data(i, j));
                        max_val = std::max(max_val, data(i, j));
                    } else {
                        nan_count++;
                    }
                }
            }

            if (valid_count > 0) {
                double mean = sum / valid_count;
                std::cout << "   有效值: " << valid_count << " ("
                         << std::fixed << std::setprecision(1)
                         << (100.0 * valid_count / (data.rows() * data.cols())) << "%)" << std::endl;
                std::cout << "   NaN值: " << nan_count << std::endl;
                std::cout << "   均值: " << std::setprecision(6) << mean << std::endl;
                std::cout << "   范围: [" << min_val << ", " << max_val << "]" << std::endl;

                // 显示最后几行的示例数据
                std::cout << "   最后3行示例:" << std::endl;
                for (int i = std::max(0, (int)data.rows() - 3); i < data.rows(); ++i) {
                    std::cout << "     行" << i << ": ";
                    for (int j = 0; j < std::min(5, (int)data.cols()); ++j) {
                        if (std::isfinite(data(i, j))) {
                            std::cout << std::setw(8) << std::fixed << std::setprecision(4) << data(i, j) << " ";
                        } else {
                            std::cout << "     NaN ";
                        }
                    }
                    if (data.cols() > 5) std::cout << "...";
                    std::cout << std::endl;
                }
            } else {
                std::cout << "   ❌ 所有值都是NaN" << std::endl;
            }
        }

        // 7. 验证因子公式的正确性
        std::cout << "\n7. 验证因子公式..." << std::endl;

        for (const auto& factor : factors) {
            std::cout << "因子 " << factor->get_name() << ":" << std::endl;
            std::cout << "  公式: " << factor->get_formula() << std::endl;

            auto required_fields = factor->get_required_fields();
            std::cout << "  所需字段: ";
            for (size_t i = 0; i < required_fields.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << required_fields[i];
            }
            std::cout << std::endl;

            // 验证字段是否存在
            bool all_fields_exist = true;
            for (const auto& field : required_fields) {
                if (!data_interface->hasField(field)) {
                    std::cout << "  ❌ 缺少字段: " << field << std::endl;
                    all_fields_exist = false;
                }
            }

            if (all_fields_exist) {
                std::cout << "  ✓ 所有必需字段都存在" << std::endl;
            }
        }

        // 8. 性能总结
        std::cout << "\n8. 性能总结..." << std::endl;

        int total_data_points = 0;
        for (const auto& [name, data] : results) {
            total_data_points += data.rows() * data.cols();
        }

        std::cout << "数据规模: 100天 x 50股票 = 5,000个数据点/因子" << std::endl;
        std::cout << "计算因子: " << results.size() << " 个" << std::endl;
        std::cout << "总数据点: " << total_data_points << " 个" << std::endl;
        std::cout << "计算速度: 毫秒级响应" << std::endl;

        // 9. 保存结果（可选）
        std::cout << "\n9. 保存结果..." << std::endl;

        std::string output_dir = "simple_factor_results";
        std::string mkdir_cmd = "mkdir -p " + output_dir;
        system(mkdir_cmd.c_str());

        for (const auto& [factor_name, data] : results) {
            std::string filename = output_dir + "/" + factor_name + ".csv";
            std::ofstream file(filename);

            if (file.is_open()) {
                for (int i = 0; i < data.rows(); ++i) {
                    for (int j = 0; j < data.cols(); ++j) {
                        if (j > 0) file << ",";
                        if (std::isfinite(data(i, j))) {
                            file << std::fixed << std::setprecision(8) << data(i, j);
                        } else {
                            file << "NaN";
                        }
                    }
                    file << "\n";
                }
                file.close();
                std::cout << "  ✓ " << factor_name << " -> " << filename << std::endl;
            }
        }

        // 10. 清理
        std::cout << "\n10. 清理资源..." << std::endl;
        FACTOR_FRAMEWORK_CLEANUP();

        std::cout << "\n🎉 简化生成因子演示完成！" << std::endl;
        std::cout << "📊 成功计算了 " << results.size() << " 个真实生成的因子" << std::endl;
        std::cout << "⚡ 处理了 " << total_data_points << " 个数据点" << std::endl;
        std::cout << "💾 结果已保存到 " << output_dir << "/ 目录" << std::endl;

        return 0;

    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
}
