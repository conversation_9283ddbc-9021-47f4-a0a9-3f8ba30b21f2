/**
 * 简单的因子测试程序
 * 演示如何使用自动生成的因子代码
 */

#include <iostream>
#include <memory>
#include <vector>
#include <random>
#include <Eigen/Dense>

// 包含feature_operators库
#include "feature_operators.hpp"

// 模拟生成的因子类（简化版本）
class SimpleFactorP1corrs0 {
public:
    SimpleFactorP1corrs0(int id, const std::string& name) 
        : factor_id_(id), factor_name_(name) {}
    
    feature_operators::DataFrame calculate(
        const feature_operators::DataFrame& close,
        const feature_operators::DataFrame& volume) {
        
        std::cout << "计算因子: " << factor_name_ << std::endl;
        std::cout << "公式: ts_Corr(Close, Volume, 60)" << std::endl;
        
        // 使用feature_operators库计算相关性
        return feature_operators::ts_Corr(close, volume, 60);
    }
    
    std::vector<std::string> getRequiredFields() const {
        return {"Close", "Volume"};
    }
    
    int getId() const { return factor_id_; }
    const std::string& getName() const { return factor_name_; }

private:
    int factor_id_;
    std::string factor_name_;
};

// 创建测试数据
std::pair<feature_operators::DataFrame, feature_operators::DataFrame> createTestData(int rows, int cols) {
    std::cout << "创建测试数据: " << rows << "x" << cols << std::endl;
    
    // 创建随机数生成器
    std::random_device rd;
    std::mt19937 gen(rd());
    std::normal_distribution<double> price_dist(100.0, 10.0);  // 价格分布
    std::normal_distribution<double> volume_dist(1000000, 200000);  // 成交量分布
    
    feature_operators::DataFrame close_data(rows, cols);
    feature_operators::DataFrame volume_data(rows, cols);
    
    // 生成初始价格和成交量
    for (int j = 0; j < cols; ++j) {
        close_data(0, j) = price_dist(gen);
        volume_data(0, j) = std::abs(volume_dist(gen));
    }
    
    // 生成时间序列数据（模拟价格随机游走）
    std::normal_distribution<double> return_dist(0.0, 0.02);  // 日收益率分布
    
    for (int i = 1; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            // 价格随机游走
            double return_rate = return_dist(gen);
            close_data(i, j) = close_data(i-1, j) * (1.0 + return_rate);
            
            // 成交量有一定的序列相关性
            double volume_change = return_dist(gen) * 0.5;
            volume_data(i, j) = volume_data(i-1, j) * (1.0 + volume_change);
            volume_data(i, j) = std::max(volume_data(i, j), 1000.0);  // 最小成交量
        }
    }
    
    return {close_data, volume_data};
}

// 打印数据统计信息
void printDataStats(const feature_operators::DataFrame& data, const std::string& name) {
    double sum = data.sum();
    double mean = sum / (data.rows() * data.cols());
    
    // 计算标准差
    double variance = 0.0;
    for (int i = 0; i < data.rows(); ++i) {
        for (int j = 0; j < data.cols(); ++j) {
            double diff = data(i, j) - mean;
            variance += diff * diff;
        }
    }
    variance /= (data.rows() * data.cols());
    double std_dev = std::sqrt(variance);
    
    std::cout << name << " 统计信息:" << std::endl;
    std::cout << "  维度: " << data.rows() << "x" << data.cols() << std::endl;
    std::cout << "  均值: " << mean << std::endl;
    std::cout << "  标准差: " << std_dev << std::endl;
    std::cout << "  最小值: " << data.minCoeff() << std::endl;
    std::cout << "  最大值: " << data.maxCoeff() << std::endl;
}

// 打印结果摘要
void printResultSummary(const feature_operators::DataFrame& result, const std::string& factor_name) {
    std::cout << "\n=== " << factor_name << " 计算结果 ===" << std::endl;
    
    // 统计有效值数量
    int valid_count = 0;
    int nan_count = 0;
    
    for (int i = 0; i < result.rows(); ++i) {
        for (int j = 0; j < result.cols(); ++j) {
            if (std::isfinite(result(i, j))) {
                valid_count++;
            } else {
                nan_count++;
            }
        }
    }
    
    std::cout << "有效值数量: " << valid_count << std::endl;
    std::cout << "NaN数量: " << nan_count << std::endl;
    
    if (valid_count > 0) {
        // 计算有效值的统计信息
        double sum = 0.0;
        double min_val = std::numeric_limits<double>::max();
        double max_val = std::numeric_limits<double>::lowest();
        
        for (int i = 0; i < result.rows(); ++i) {
            for (int j = 0; j < result.cols(); ++j) {
                if (std::isfinite(result(i, j))) {
                    sum += result(i, j);
                    min_val = std::min(min_val, result(i, j));
                    max_val = std::max(max_val, result(i, j));
                }
            }
        }
        
        double mean = sum / valid_count;
        std::cout << "有效值均值: " << mean << std::endl;
        std::cout << "有效值范围: [" << min_val << ", " << max_val << "]" << std::endl;
        
        // 显示前几行的结果（用于验证）
        std::cout << "\n前5行结果示例:" << std::endl;
        for (int i = std::max(0, result.rows() - 5); i < result.rows() && i < result.rows(); ++i) {
            std::cout << "行 " << i << ": ";
            for (int j = 0; j < std::min(5, (int)result.cols()); ++j) {
                if (std::isfinite(result(i, j))) {
                    std::cout << std::fixed << std::setprecision(4) << result(i, j) << " ";
                } else {
                    std::cout << "NaN ";
                }
            }
            if (result.cols() > 5) std::cout << "...";
            std::cout << std::endl;
        }
    }
}

int main() {
    try {
        std::cout << "=== 简单因子测试程序 ===" << std::endl;
        
        // 1. 创建测试数据
        std::cout << "\n1. 创建测试数据..." << std::endl;
        auto [close_data, volume_data] = createTestData(100, 50);  // 100天，50只股票
        
        printDataStats(close_data, "Close");
        printDataStats(volume_data, "Volume");
        
        // 2. 创建因子实例
        std::cout << "\n2. 创建因子实例..." << std::endl;
        SimpleFactorP1corrs0 factor(0, "p1_corrs0");
        
        std::cout << "因子ID: " << factor.getId() << std::endl;
        std::cout << "因子名称: " << factor.getName() << std::endl;
        std::cout << "所需字段: ";
        auto required_fields = factor.getRequiredFields();
        for (size_t i = 0; i < required_fields.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << required_fields[i];
        }
        std::cout << std::endl;
        
        // 3. 执行因子计算
        std::cout << "\n3. 执行因子计算..." << std::endl;
        auto start_time = std::chrono::high_resolution_clock::now();
        
        auto result = factor.calculate(close_data, volume_data);
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        std::cout << "计算完成，耗时: " << duration.count() << " ms" << std::endl;
        
        // 4. 分析结果
        printResultSummary(result, factor.getName());
        
        std::cout << "\n✅ 测试完成！" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 错误: " << e.what() << std::endl;
        return 1;
    }
}
