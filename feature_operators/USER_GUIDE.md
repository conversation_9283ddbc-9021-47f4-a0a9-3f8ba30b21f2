# 📖 因子框架用户指南

## 🚀 快速开始

### 第一步：准备因子配置
创建或编辑 `feature.csv` 文件：
```csv
factor_id,factor_name,formula,description
0,p1_corrs0,ts_Corr(Close,Volume,60),价格与成交量60日相关性
1,p1_corrs1,ts_Corr(Close/ts_Delay(Close,1)-1,Volume,60),收益率与成交量相关性
2,p1_corrs2,ts_Corr(ts_Delay(Close,1),Volume,60),滞后价格与成交量相关性
```

### 第二步：生成因子代码
```bash
cd scripts
python3 factor_code_generator.py \
    --input ../feature.csv \
    --output_dir ../generated_factors \
    --max_factors 10 \
    --verbose
```

### 第三步：编译项目
```bash
cd ../build
cmake ..
make
```

### 第四步：使用因子框架
```cpp
#include "factor_framework/factor_framework.hpp"
#include "generated_factors/all_factors.hpp"

int main() {
    // 1. 初始化
    FACTOR_FRAMEWORK_INIT();
    generated_factors::initialize_all_factors();
    
    // 2. 创建组件
    auto data_interface = std::make_shared<factor_framework::data_interface>();
    auto factor_manager = std::make_shared<factor_framework::factor_manager>();
    
    // 3. 加载数据
    data_interface->create_standard_market_data(100, 50);
    
    // 4. 注册因子
    generated_factors::register_all_factors_to_manager(factor_manager);
    
    // 5. 选择因子
    factor_manager->select_factors({0, 1, 2});
    
    // 6. 获取结果
    auto factors = factor_manager->get_selected_factors();
    for (const auto& factor : factors) {
        std::cout << "因子: " << factor->get_name() << std::endl;
    }
    
    // 7. 清理
    FACTOR_FRAMEWORK_CLEANUP();
    return 0;
}
```

## 📋 核心API参考

### factor_manager (因子管理器)
```cpp
class factor_manager {
public:
    // 因子注册
    bool register_factor(std::unique_ptr<factor_base> factor);
    
    // 因子选择
    void select_factors(const std::vector<int>& factor_ids);
    void select_factors(const std::vector<std::string>& factor_names);
    void clear_selection();
    
    // 因子查询
    std::shared_ptr<factor_base> get_factor(int factor_id) const;
    std::vector<std::shared_ptr<factor_base>> get_selected_factors() const;
    size_t get_factor_count() const;
    
    // 依赖分析
    std::vector<std::string> get_required_fields() const;
};
```

### data_interface (数据接口)
```cpp
class data_interface {
public:
    // 数据加载
    bool load_data_from_csv(const std::string& field_name, const std::string& file_path);
    void create_standard_market_data(int rows, int cols, double base_price = 100.0);
    
    // 数据访问
    const std::unordered_map<std::string, feature_operators::DataFrame>& get_all_data() const;
    bool has_field(const std::string& field_name) const;
    std::vector<std::string> get_field_names() const;
    
    // 数据验证
    bool validate_data_dimensions() const;
};
```

### factor_base (因子基类)
```cpp
class factor_base {
public:
    // 属性访问
    int get_id() const;
    const std::string& get_name() const;
    const std::string& get_formula() const;
    virtual std::vector<std::string> get_required_fields() const = 0;
    
    // 核心计算
    virtual feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) = 0;
};
```

## 🔧 代码生成器使用

### 基本用法
```bash
python3 factor_code_generator.py --input feature.csv --output_dir generated_factors
```

### 高级选项
```bash
python3 factor_code_generator.py \
    --input feature.csv \
    --output_dir generated_factors \
    --filter "p1_*" \           # 只生成匹配模式的因子
    --max_factors 20 \          # 限制生成数量
    --verbose \                 # 详细输出
    --dry_run                   # 只解析不生成
```

### 生成的文件结构
```
generated_factors/
├── include/generated_factors/
│   ├── factorp1corrs0.hpp
│   ├── factorp1corrs1.hpp
│   └── all_factors.hpp
├── src/
│   ├── factorp1corrs0.cpp
│   ├── factorp1corrs1.cpp
│   └── all_factors.cpp
├── CMakeLists.txt
└── factor_info.json
```

## 📊 数据格式要求

### CSV配置文件格式
- **factor_id**: 唯一的因子ID (整数)
- **factor_name**: 因子名称 (字符串，用作标识符)
- **formula**: 因子公式 (使用feature_operators语法)
- **description**: 因子描述 (可选)

### 支持的操作符
- **时间序列**: `ts_Corr`, `ts_Mean`, `ts_Std`, `ts_Delay`, `ts_Delta`
- **截面运算**: `cs_Rank`, `cs_Zscore`, `cs_Demean`
- **数学函数**: `Abs`, `Log`, `Sqrt`, `Sign`
- **逻辑运算**: `Greater`, `Less`, `Equal`

### 数据字段
标准市场数据字段：
- **Open**: 开盘价
- **High**: 最高价  
- **Low**: 最低价
- **Close**: 收盘价
- **Volume**: 成交量
- **Amount**: 成交额
- **VWAP**: 成交量加权平均价

## ⚡ 性能优化建议

### 数据准备
```cpp
// 预先加载所有需要的数据
data_interface->load_data_from_csv("Close", "close.csv");
data_interface->load_data_from_csv("Volume", "volume.csv");

// 验证数据完整性
if (!data_interface->validate_data_dimensions()) {
    std::cerr << "数据维度不一致" << std::endl;
}
```

### 批量操作
```cpp
// 批量注册因子
std::vector<std::unique_ptr<factor_base>> factors;
// ... 创建因子实例
factor_manager->register_factors(std::move(factors));

// 批量选择因子
factor_manager->select_factors({0, 1, 2, 3, 4});
```

### 内存管理
```cpp
// 使用智能指针管理生命周期
auto factor_manager = std::make_shared<factor_framework::factor_manager>();
auto data_interface = std::make_shared<factor_framework::data_interface>();

// 及时清理不需要的数据
factor_manager->clear_selection();
```

## 🐛 常见问题解决

### 编译错误
```bash
# 确保包含路径正确
target_include_directories(your_target PRIVATE 
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/generated_factors/include
)

# 链接必要的库
target_link_libraries(your_target 
    feature_ops_lib
    generated_factors
)
```

### 运行时错误
```cpp
// 检查因子是否注册成功
if (factor_manager->get_factor_count() == 0) {
    std::cerr << "没有注册任何因子" << std::endl;
}

// 检查数据是否加载
if (data_interface->get_field_names().empty()) {
    std::cerr << "没有加载任何数据" << std::endl;
}

// 检查数据依赖
auto required_fields = factor_manager->get_required_fields();
for (const auto& field : required_fields) {
    if (!data_interface->has_field(field)) {
        std::cerr << "缺少必需的数据字段: " << field << std::endl;
    }
}
```

### 性能问题
```cpp
// 启用编译器优化
// 在CMakeLists.txt中添加:
// set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

// 使用Release模式编译
// cmake -DCMAKE_BUILD_TYPE=Release ..
```

## 📚 示例程序

### 完整的因子计算示例
参考 `examples/architecture_demo.cpp`

### Snake Case命名演示
参考 `examples/snake_case_demo.cpp`

### 简化使用示例
参考 `examples/simplified_demo.cpp`

## 🔗 相关文档

- **架构指南**: `ARCHITECTURE_GUIDE.md` - 详细的架构设计文档
- **重构报告**: `SNAKE_CASE_REFACTOR_REPORT.md` - 命名规范重构说明
- **API文档**: 头文件中的详细注释
- **示例代码**: `examples/` 目录下的演示程序

## 💡 最佳实践

1. **配置管理**: 使用版本控制管理因子配置文件
2. **代码生成**: 将生成的代码纳入构建流程
3. **测试验证**: 编写单元测试验证因子正确性
4. **性能监控**: 定期进行性能基准测试
5. **文档维护**: 保持因子描述和文档的更新

---

通过这个用户指南，您可以快速上手使用因子框架，从配置到计算的完整流程都有详细说明。如有问题，请参考相关文档或示例程序。
