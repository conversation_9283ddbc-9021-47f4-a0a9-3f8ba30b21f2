#!/usr/bin/env python3
"""
Results Analysis Tool
结果分析工具，用于分析测试结果并生成报告

用法:
python analyze_results.py --mode summary --category all
python analyze_results.py --mode detailed --category core_math
python analyze_results.py --mode report --output-dir custom_output
"""

import os
import sys
import argparse
import json
import pandas as pd
import numpy as np
import glob
from pathlib import Path
import time

def analyze_comparison_results(results_dir, category="all"):
    """分析比较结果"""
    print("=" * 80)
    print(f"Comparison Results Analysis - Category: {category}")
    print("=" * 80)

    # 查找比较结果文件
    if category == "all":
        # 查找所有比较结果文件，包括simple_compare和v2_vs_python等
        patterns = [
            os.path.join(results_dir, "*_comparison_*.json"),
            os.path.join(results_dir, "simple_compare_*_results.json"),
            os.path.join(results_dir, "v2_vs_python_*.json")
        ]
        result_files = []
        for pattern in patterns:
            result_files.extend(glob.glob(pattern))
    else:
        # 查找指定分类的文件
        patterns = [
            os.path.join(results_dir, f"*{category}*_comparison_*.json"),
            os.path.join(results_dir, f"simple_compare_{category}_results.json"),
            os.path.join(results_dir, f"*{category}*_vs_python_*.json")
        ]
        result_files = []
        for pattern in patterns:
            result_files.extend(glob.glob(pattern))

    if not result_files:
        print(f"No comparison result files found for category: {category}")
        return None

    print(f"Found {len(result_files)} comparison result files:")
    for f in result_files:
        print(f"  - {os.path.basename(f)}")

    # 分析每个结果文件
    all_results = {}

    for result_file in result_files:
        try:
            with open(result_file, 'r') as f:
                data = json.load(f)

            file_name = os.path.basename(result_file)
            all_results[file_name] = data

            print(f"\n--- {file_name} ---")
            if 'summary' in data:
                summary = data['summary']
                print(f"Total factors: {summary.get('total_factors', 'N/A')}")
                print(f"Matches: {summary.get('matches', 'N/A')}")
                print(f"Total compared: {summary.get('total_compared', 'N/A')}")
                print(f"Match rate: {summary.get('match_rate', 'N/A'):.1f}%")

        except Exception as e:
            print(f"Error loading {result_file}: {e}")

    return all_results

def generate_summary_report(results_dir, output_dir):
    """生成总结报告"""
    print("=" * 80)
    print("Generating Summary Report")
    print("=" * 80)

    # 分析所有比较结果
    all_results = analyze_comparison_results(results_dir, "all")

    if not all_results:
        print("No results to analyze")
        return False

    # 生成时间戳
    timestamp = time.strftime("%Y%m%d_%H%M%S")

    # 创建总结报告
    report_file = os.path.join(output_dir, f"summary_report_{timestamp}.txt")

    with open(report_file, 'w') as f:
        f.write("Feature Operators Test Results Summary Report\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Generated: {timestamp}\n\n")

        f.write("Comparison Results Summary:\n")
        f.write("-" * 40 + "\n")

        total_matches = 0
        total_compared = 0

        for file_name, data in all_results.items():
            f.write(f"\n{file_name}:\n")

            if 'summary' in data:
                summary = data['summary']
                matches = summary.get('matches', 0)
                compared = summary.get('total_compared', 0)
                match_rate = summary.get('match_rate', 0)

                f.write(f"  Matches: {matches}/{compared} ({match_rate:.1f}%)\n")

                total_matches += matches
                total_compared += compared
            else:
                f.write("  No summary data available\n")

        f.write(f"\nOverall Summary:\n")
        f.write("-" * 20 + "\n")
        if total_compared > 0:
            overall_rate = (total_matches / total_compared) * 100
            f.write(f"Total Matches: {total_matches}/{total_compared} ({overall_rate:.1f}%)\n")
        else:
            f.write("No comparison data available\n")

    print(f"Summary report saved to: {report_file}")
    return True

def generate_detailed_report(results_dir, category, output_dir):
    """生成详细报告"""
    print("=" * 80)
    print(f"Generating Detailed Report - Category: {category}")
    print("=" * 80)

    # 分析指定分类的结果
    results = analyze_comparison_results(results_dir, category)

    if not results:
        print(f"No results found for category: {category}")
        return False

    # 生成时间戳
    timestamp = time.strftime("%Y%m%d_%H%M%S")

    # 创建详细报告
    report_file = os.path.join(output_dir, f"detailed_report_{category}_{timestamp}.txt")

    with open(report_file, 'w') as f:
        f.write(f"Feature Operators Detailed Report - {category}\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Generated: {timestamp}\n\n")

        for file_name, data in results.items():
            f.write(f"=== {file_name} ===\n\n")

            if 'summary' in data:
                summary = data['summary']
                f.write("Summary:\n")
                for key, value in summary.items():
                    f.write(f"  {key}: {value}\n")
                f.write("\n")

            if 'detailed_results' in data:
                f.write("Detailed Results:\n")
                f.write("-" * 40 + "\n")

                detailed = data['detailed_results']
                if isinstance(detailed, list):
                    # 处理列表格式的详细结果
                    for item in detailed:
                        if isinstance(item, dict) and 'factor' in item:
                            factor = item['factor']
                            has_cpp = item.get('has_cpp', False)
                            has_py = item.get('has_py', False)
                            cpp_vs_py = item.get('cpp_vs_py', 'N/A')

                            status_cpp = "✓" if has_cpp else "✗"
                            status_py = "✓" if has_py else "✗"

                            f.write(f"{factor:<20} C++:{status_cpp} Python:{status_py} Compare:{cpp_vs_py}\n")
                elif isinstance(detailed, dict):
                    # 处理字典格式的详细结果
                    for factor, result in detailed.items():
                        if isinstance(result, dict):
                            match = result.get('match', False)
                            details = result.get('details', 'N/A')
                            status = "✓" if match else "✗"
                            f.write(f"{factor:<20} {status} {details}\n")
                        else:
                            f.write(f"{factor:<20} {result}\n")

                f.write("\n")

    print(f"Detailed report saved to: {report_file}")
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Results Analysis Tool')
    parser.add_argument('--mode',
                       choices=['summary', 'detailed', 'analyze'],
                       default='analyze',
                       help='分析模式')
    parser.add_argument('--category',
                       choices=['core_math', 'logical_ops', 'comparison_ops', 'data_utils',
                               'reduction_ops', 'timeseries_ops', 'panel_ops', 'group_ops', 'all'],
                       default='all',
                       help='测试分类')
    parser.add_argument('--results-dir',
                       default="/home/<USER>/git/feature_operators/tests/test_right/results/comparison",
                       help='结果目录')
    parser.add_argument('--output-dir',
                       default="/home/<USER>/git/feature_operators/tests/test_right/results/reports",
                       help='输出目录')

    args = parser.parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    print("Feature Operators Results Analysis Tool")
    print("=" * 80)
    print(f"模式: {args.mode}")
    print(f"分类: {args.category}")
    print(f"结果目录: {args.results_dir}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 80)

    success = False

    if args.mode == 'analyze':
        # 只分析，不生成报告
        results = analyze_comparison_results(args.results_dir, args.category)
        success = results is not None

    elif args.mode == 'summary':
        # 生成总结报告
        success = generate_summary_report(args.results_dir, args.output_dir)

    elif args.mode == 'detailed':
        # 生成详细报告
        success = generate_detailed_report(args.results_dir, args.category, args.output_dir)

    if success:
        print("\n🎉 结果分析完成!")
        return 0
    else:
        print("\n❌ 结果分析失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
