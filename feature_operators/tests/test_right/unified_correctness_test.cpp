#include "../../include/feature_operators/core_math.hpp"
#include "../../include/feature_operators/logical_ops.hpp"
#include "../../include/feature_operators/comparison_ops.hpp"
#include "../../include/feature_operators/data_utils.hpp"
#include "../../include/feature_operators/reduction_ops.hpp"
#include "../../include/feature_operators/timeseries_ops.hpp"
#include "../../include/feature_operators/timeseries_ops_v2.hpp"
#include "../../include/feature_operators/panel_ops.hpp"
#include "../../include/feature_operators/group_ops.hpp"
#include <iostream>
#include <iomanip>
#include <vector>
#include <string>
#include <cmath>
#include <fstream>
#include <sstream>
#include <map>

using namespace feature_operators;
using namespace feature_operators::v2;

// 从CSV文件加载数据
DataFrame loadCsv(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filepath << std::endl;
        return DataFrame();
    }

    std::vector<std::vector<double>> data;
    std::string line;

    // 跳过标题行
    std::getline(file, line);

    // 读取数据行
    while (std::getline(file, line)) {
        std::vector<double> row;
        std::stringstream ss(line);
        std::string cell;

        // 跳过第一列（日期）
        std::getline(ss, cell, ',');

        // 读取数据列
        while (std::getline(ss, cell, ',')) {
            try {
                double value = std::stod(cell);
                row.push_back(value);
            } catch (const std::exception& e) {
                row.push_back(std::numeric_limits<double>::quiet_NaN());
            }
        }

        if (!row.empty()) {
            data.push_back(row);
        }
    }

    // 创建Eigen矩阵
    int rows = data.size();
    int cols = rows > 0 ? data[0].size() : 0;

    DataFrame matrix(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            matrix(i, j) = data[i][j];
        }
    }

    return matrix;
}

// 将DataFrame保存为CSV文件
void saveCsv(const DataFrame& data, const std::string& filepath) {
    // 创建目录
    std::string dir = filepath.substr(0, filepath.find_last_of('/'));
    std::string cmd = "mkdir -p " + dir;
    system(cmd.c_str());

    std::ofstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filepath << std::endl;
        return;
    }

    // 使用科学计数法，保留10位小数
    file << std::scientific << std::setprecision(10);

    for (int i = 0; i < data.rows(); ++i) {
        for (int j = 0; j < data.cols(); ++j) {
            if (j > 0) file << ",";
            file << data(i, j);
        }
        file << "\n";
    }

    file.close();
    std::cout << "已保存到 " << filepath << std::endl;
}

// 运行核心数学算子测试
void testCoreMath(const DataFrame& close, const DataFrame& open, const DataFrame& volume,
                  const std::string& outputDir) {
    std::cout << "\n=== Core Math Operations Test ===" << std::endl;

    try {
        std::cout << "  测试 Add..." << std::endl;
        saveCsv(Add(close, open), outputDir + "/Add.csv");

        std::cout << "  测试 Minus..." << std::endl;
        saveCsv(Minus(close, open), outputDir + "/Minus.csv");

        std::cout << "  测试 Multiply..." << std::endl;
        saveCsv(Multiply(close, open), outputDir + "/Multiply.csv");

        std::cout << "  测试 Divide..." << std::endl;
        saveCsv(Divide(close, open), outputDir + "/Divide.csv");

        std::cout << "  测试 Sqrt..." << std::endl;
        saveCsv(Sqrt(close), outputDir + "/Sqrt.csv");

        std::cout << "  测试 Log..." << std::endl;
        saveCsv(Log(close), outputDir + "/Log.csv");

        std::cout << "  测试 inv..." << std::endl;
        saveCsv(inv(close), outputDir + "/inv.csv");

        std::cout << "  测试 Power..." << std::endl;
        saveCsv(Power(close, 2), outputDir + "/Power.csv");

        std::cout << "  测试 Abs..." << std::endl;
        saveCsv(Abs(Minus(close, open)), outputDir + "/Abs.csv");

        std::cout << "  测试 Sign..." << std::endl;
        saveCsv(Sign(Minus(close, open)), outputDir + "/Sign.csv");

        std::cout << "  测试 Exp..." << std::endl;
        saveCsv(Exp(Log(close)), outputDir + "/Exp.csv");

        std::cout << "  测试 Reverse..." << std::endl;
        saveCsv(Reverse(close), outputDir + "/Reverse.csv");

        std::cout << "  测试 Ceil..." << std::endl;
        saveCsv(Ceil(close), outputDir + "/Ceil.csv");

        std::cout << "  测试 Floor..." << std::endl;
        saveCsv(Floor(close), outputDir + "/Floor.csv");

        std::cout << "  测试 Round..." << std::endl;
        saveCsv(Round(close), outputDir + "/Round.csv");

        std::cout << "  测试 SignedPower..." << std::endl;
        saveCsv(SignedPower(close, 2.0), outputDir + "/SignedPower.csv");

        std::cout << "  测试 Softsign..." << std::endl;
        saveCsv(Softsign(close), outputDir + "/Softsign.csv");

    } catch (const std::exception& e) {
        std::cerr << "Core Math 测试错误: " << e.what() << std::endl;
    }
}

// 运行逻辑算子测试
void testLogicalOps(const DataFrame& close, const DataFrame& open, const std::string& outputDir) {
    std::cout << "\n=== Logical Operations Test ===" << std::endl;

    try {
        std::cout << "  测试 And..." << std::endl;
        saveCsv(And(close, open), outputDir + "/And.csv");

        std::cout << "  测试 Or..." << std::endl;
        saveCsv(Or(close, open), outputDir + "/Or.csv");

        std::cout << "  测试 Not..." << std::endl;
        saveCsv(Not(close), outputDir + "/Not.csv");

        std::cout << "  测试 Xor..." << std::endl;
        saveCsv(Xor(close, open), outputDir + "/Xor.csv");

    } catch (const std::exception& e) {
        std::cerr << "Logical Ops 测试错误: " << e.what() << std::endl;
    }
}

// 运行比较算子测试
void testComparisonOps(const DataFrame& close, const DataFrame& open, const std::string& outputDir) {
    std::cout << "\n=== Comparison Operations Test ===" << std::endl;

    try {
        std::cout << "  测试 Equal..." << std::endl;
        saveCsv(Equal(close, open), outputDir + "/Equal.csv");

        std::cout << "  测试 UnEqual..." << std::endl;
        saveCsv(UnEqual(close, open), outputDir + "/UnEqual.csv");

        std::cout << "  测试 Mthan..." << std::endl;
        saveCsv(Mthan(close, open), outputDir + "/Mthan.csv");

        std::cout << "  测试 MEthan..." << std::endl;
        saveCsv(MEthan(close, open), outputDir + "/MEthan.csv");

        std::cout << "  测试 Lthan..." << std::endl;
        saveCsv(Lthan(close, open), outputDir + "/Lthan.csv");

        std::cout << "  测试 LEthan..." << std::endl;
        saveCsv(LEthan(close, open), outputDir + "/LEthan.csv");

    } catch (const std::exception& e) {
        std::cerr << "Comparison Ops 测试错误: " << e.what() << std::endl;
    }
}

// 运行数据工具测试
void testDataUtils(const DataFrame& close, const std::string& outputDir) {
    std::cout << "\n=== Data Utils Test ===" << std::endl;

    try {
        std::cout << "  测试 FilterInf..." << std::endl;
        saveCsv(FilterInf(close), outputDir + "/FilterInf.csv");

        std::cout << "  测试 FillNan..." << std::endl;
        saveCsv(FillNan(close, 0.0), outputDir + "/FillNan.csv");

        std::cout << "  测试 getNan..." << std::endl;
        saveCsv(getNan(close), outputDir + "/getNan.csv");

        std::cout << "  测试 getInf..." << std::endl;
        saveCsv(getInf(close), outputDir + "/getInf.csv");

    } catch (const std::exception& e) {
        std::cerr << "Data Utils 测试错误: " << e.what() << std::endl;
    }
}

// 运行归约算子测试
void testReductionOps(const DataFrame& close, const DataFrame& open, const std::string& outputDir) {
    std::cout << "\n=== Reduction Operations Test ===" << std::endl;

    try {
        std::cout << "  测试 Min..." << std::endl;
        saveCsv(Min(close, open), outputDir + "/Min.csv");

        std::cout << "  测试 Max..." << std::endl;
        saveCsv(Max(close, open), outputDir + "/Max.csv");

    } catch (const std::exception& e) {
        std::cerr << "Reduction Ops 测试错误: " << e.what() << std::endl;
    }
}

// 运行时间序列算子测试
void testTimeseriesOps(const DataFrame& close, const DataFrame& open, const DataFrame& high,
                      const DataFrame& volume, const std::string& outputDir) {
    std::cout << "\n=== Time Series Operations Test ===" << std::endl;

    try {
        // 基础时间序列算子
        std::cout << "  测试 ts_Delay..." << std::endl;
        saveCsv(ts_Delay(close, 5), outputDir + "/ts_Delay.csv");

        std::cout << "  测试 ts_Mean..." << std::endl;
        saveCsv(ts_Mean(close, 10), outputDir + "/ts_Mean.csv");

        std::cout << "  测试 ts_Sum..." << std::endl;
        saveCsv(ts_Sum(close, 10), outputDir + "/ts_Sum.csv");

        std::cout << "  测试 ts_Stdev..." << std::endl;
        saveCsv(ts_Stdev(close, 10), outputDir + "/ts_Stdev.csv");

        std::cout << "  测试 ts_Min..." << std::endl;
        saveCsv(ts_Min(close, 10), outputDir + "/ts_Min.csv");

        std::cout << "  测试 ts_Max..." << std::endl;
        saveCsv(ts_Max(close, 10), outputDir + "/ts_Max.csv");

        std::cout << "  测试 ts_Delta..." << std::endl;
        saveCsv(ts_Delta(close, 5), outputDir + "/ts_Delta.csv");

        std::cout << "  测试 ts_Divide..." << std::endl;
        saveCsv(ts_Divide(close, 5), outputDir + "/ts_Divide.csv");

        std::cout << "  测试 ts_ChgRate..." << std::endl;
        saveCsv(ts_ChgRate(close, 5), outputDir + "/ts_ChgRate.csv");

        std::cout << "  测试 ts_Argmax..." << std::endl;
        saveCsv(ts_Argmax(close, 10), outputDir + "/ts_Argmax.csv");

        std::cout << "  测试 ts_Argmin..." << std::endl;
        saveCsv(ts_Argmin(close, 10), outputDir + "/ts_Argmin.csv");

        std::cout << "  测试 ts_Rank..." << std::endl;
        saveCsv(ts_Rank(close, 10), outputDir + "/ts_Rank.csv");

        std::cout << "  测试 ts_Median..." << std::endl;
        saveCsv(ts_Median(close, 10), outputDir + "/ts_Median.csv");

        // 启用已实现的算子
        std::cout << "  测试 ts_Corr..." << std::endl;
        saveCsv(ts_Corr(close, volume, 10), outputDir + "/ts_Corr.csv");

        std::cout << "  测试 ts_Cov..." << std::endl;
        saveCsv(ts_Cov(close, volume, 10), outputDir + "/ts_Cov.csv");

        std::cout << "  测试 ts_Skewness..." << std::endl;
        saveCsv(ts_Skewness(close, 10), outputDir + "/ts_Skewness.csv");

        std::cout << "  测试 ts_Kurtosis..." << std::endl;
        saveCsv(ts_Kurtosis(close, 10), outputDir + "/ts_Kurtosis.csv");

        std::cout << "  测试 ts_Scale..." << std::endl;
        saveCsv(ts_Scale(close, 10), outputDir + "/ts_Scale.csv");

        std::cout << "  测试 ts_Product..." << std::endl;
        saveCsv(ts_Product(close, 10), outputDir + "/ts_Product.csv");

        // 启用已实现的复杂算子
        std::cout << "  测试 ts_TransNorm..." << std::endl;
        saveCsv(ts_TransNorm(close, 10), outputDir + "/ts_TransNorm.csv");

        std::cout << "  测试 ts_Decay..." << std::endl;
        saveCsv(ts_Decay(close, 10), outputDir + "/ts_Decay.csv");

        std::cout << "  测试 ts_Decay2..." << std::endl;
        saveCsv(ts_Decay2(close, 10), outputDir + "/ts_Decay2.csv");

        // 启用复杂的多变量算子
        std::cout << "  测试 ts_Partial_corr..." << std::endl;
        saveCsv(ts_Partial_corr(close, open, high, 10), outputDir + "/ts_Partial_corr.csv");

        std::cout << "  测试 ts_Regression_A..." << std::endl;
        saveCsv(ts_Regression(close, open, 10, 'A'), outputDir + "/ts_Regression_A.csv");

        std::cout << "  测试 ts_Regression_B..." << std::endl;
        saveCsv(ts_Regression(close, open, 10, 'B'), outputDir + "/ts_Regression_B.csv");

        std::cout << "  测试 ts_Regression_C..." << std::endl;
        saveCsv(ts_Regression(close, open, 10, 'C'), outputDir + "/ts_Regression_C.csv");

        std::cout << "  测试 ts_Regression_D..." << std::endl;
        saveCsv(ts_Regression(close, open, 10, 'D'), outputDir + "/ts_Regression_D.csv");

        std::cout << "  测试 ts_Entropy..." << std::endl;
        saveCsv(ts_Entropy(close, 10), outputDir + "/ts_Entropy.csv");

        std::cout << "  测试 ts_MaxDD..." << std::endl;
        saveCsv(ts_MaxDD(close, 10), outputDir + "/ts_MaxDD.csv");

        std::cout << "  测试 ts_MeanChg..." << std::endl;
        saveCsv(ts_MeanChg(close, 10), outputDir + "/ts_MeanChg.csv");

        std::cout << "  测试 ts_Quantile_A..." << std::endl;
        saveCsv(ts_Quantile(close, 10, 'A'), outputDir + "/ts_Quantile_A.csv");

        std::cout << "  测试 ts_Quantile_B..." << std::endl;
        saveCsv(ts_Quantile(close, 10, 'B'), outputDir + "/ts_Quantile_B.csv");

        std::cout << "  测试 ts_Quantile_C..." << std::endl;
        saveCsv(ts_Quantile(close, 10, 'C'), outputDir + "/ts_Quantile_C.csv");

        std::cout << "  测试 ts_Quantile_D..." << std::endl;
        saveCsv(ts_Quantile(close, 10, 'D'), outputDir + "/ts_Quantile_D.csv");

        // V2版本的时间序列算子测试
        std::cout << "  测试 ts_Delay_v2..." << std::endl;
        saveCsv(ts_Delay_v2(close, 5), outputDir + "/ts_Delay_v2.csv");

        std::cout << "  测试 ts_Mean_v2..." << std::endl;
        saveCsv(ts_Mean_v2(close, 10), outputDir + "/ts_Mean_v2.csv");

        std::cout << "  测试 ts_Sum_v2..." << std::endl;
        saveCsv(ts_Sum_v2(close, 10), outputDir + "/ts_Sum_v2.csv");

        std::cout << "  测试 ts_Stdev_v2..." << std::endl;
        saveCsv(ts_Stdev_v2(close, 10), outputDir + "/ts_Stdev_v2.csv");

        std::cout << "  测试 ts_Min_v2..." << std::endl;
        saveCsv(ts_Min_v2(close, 10), outputDir + "/ts_Min_v2.csv");

        std::cout << "  测试 ts_Max_v2..." << std::endl;
        saveCsv(ts_Max_v2(close, 10), outputDir + "/ts_Max_v2.csv");

        std::cout << "  测试 ts_Delta_v2..." << std::endl;
        saveCsv(ts_Delta_v2(close, 5), outputDir + "/ts_Delta_v2.csv");

        std::cout << "  测试 ts_Divide_v2..." << std::endl;
        saveCsv(ts_Divide_v2(close, 5), outputDir + "/ts_Divide_v2.csv");

        std::cout << "  测试 ts_ChgRate_v2..." << std::endl;
        saveCsv(ts_ChgRate_v2(close, 5), outputDir + "/ts_ChgRate_v2.csv");

        std::cout << "  测试 ts_Argmax_v2..." << std::endl;
        saveCsv(ts_Argmax_v2(close, 10), outputDir + "/ts_Argmax_v2.csv");

        std::cout << "  测试 ts_Argmin_v2..." << std::endl;
        saveCsv(ts_Argmin_v2(close, 10), outputDir + "/ts_Argmin_v2.csv");

        std::cout << "  测试 ts_Rank_v2..." << std::endl;
        saveCsv(ts_Rank_v2(close, 10), outputDir + "/ts_Rank_v2.csv");

        std::cout << "  测试 ts_Median_v2..." << std::endl;
        saveCsv(ts_Median_v2(close, 10), outputDir + "/ts_Median_v2.csv");

        std::cout << "  测试 ts_Corr_v2..." << std::endl;
        saveCsv(ts_Corr_v2(close, volume, 10), outputDir + "/ts_Corr_v2.csv");

        std::cout << "  测试 ts_Cov_v2..." << std::endl;
        saveCsv(ts_Cov_v2(close, volume, 10), outputDir + "/ts_Cov_v2.csv");

        std::cout << "  测试 ts_Skewness_v2..." << std::endl;
        saveCsv(ts_Skewness_v2(close, 10), outputDir + "/ts_Skewness_v2.csv");

        std::cout << "  测试 ts_Kurtosis_v2..." << std::endl;
        saveCsv(ts_Kurtosis_v2(close, 10), outputDir + "/ts_Kurtosis_v2.csv");

        std::cout << "  测试 ts_Scale_v2..." << std::endl;
        saveCsv(ts_Scale_v2(close, 10), outputDir + "/ts_Scale_v2.csv");

        std::cout << "  测试 ts_Product_v2..." << std::endl;
        saveCsv(ts_Product_v2(close, 10), outputDir + "/ts_Product_v2.csv");

        std::cout << "  测试 ts_Decay_v2..." << std::endl;
        saveCsv(ts_Decay_v2(close, 10), outputDir + "/ts_Decay_v2.csv");

        std::cout << "  测试 ts_Decay2_v2..." << std::endl;
        saveCsv(ts_Decay2_v2(close, 10), outputDir + "/ts_Decay2_v2.csv");

        std::cout << "  测试 ts_MaxDD_v2..." << std::endl;
        saveCsv(ts_MaxDD_v2(close, 10), outputDir + "/ts_MaxDD_v2.csv");

        std::cout << "  测试 ts_MeanChg_v2..." << std::endl;
        saveCsv(ts_MeanChg_v2(close, 10), outputDir + "/ts_MeanChg_v2.csv");

        std::cout << "  测试 ts_Quantile_A_v2..." << std::endl;
        saveCsv(ts_Quantile_v2(close, 10, 'A'), outputDir + "/ts_Quantile_A_v2.csv");

        std::cout << "  测试 ts_Quantile_B_v2..." << std::endl;
        saveCsv(ts_Quantile_v2(close, 10, 'B'), outputDir + "/ts_Quantile_B_v2.csv");

        std::cout << "  测试 ts_Quantile_C_v2..." << std::endl;
        saveCsv(ts_Quantile_v2(close, 10, 'C'), outputDir + "/ts_Quantile_C_v2.csv");

        std::cout << "  测试 ts_Quantile_D_v2..." << std::endl;
        saveCsv(ts_Quantile_v2(close, 10, 'D'), outputDir + "/ts_Quantile_D_v2.csv");

    } catch (const std::exception& e) {
        std::cerr << "Time Series Ops 测试错误: " << e.what() << std::endl;
    }
}

// 运行面板算子测试
void testPanelOps(const DataFrame& close, const DataFrame& open, const std::string& outputDir) {
    std::cout << "\n=== Panel Operations Test ===" << std::endl;

    try {
        std::cout << "  测试 pn_Mean..." << std::endl;
        saveCsv(pn_Mean(close), outputDir + "/pn_Mean.csv");

        std::cout << "  测试 pn_Rank..." << std::endl;
        saveCsv(pn_Rank(close), outputDir + "/pn_Rank.csv");

        std::cout << "  测试 pn_Stand..." << std::endl;
        saveCsv(pn_Stand(close), outputDir + "/pn_Stand.csv");

        std::cout << "  测试 pn_TransNorm..." << std::endl;
        saveCsv(pn_TransNorm(close), outputDir + "/pn_TransNorm.csv");

        std::cout << "  测试 pn_Rank2..." << std::endl;
        saveCsv(pn_Rank2(close), outputDir + "/pn_Rank2.csv");

        std::cout << "  测试 pn_RankCentered..." << std::endl;
        saveCsv(pn_RankCentered(close), outputDir + "/pn_RankCentered.csv");

        std::cout << "  测试 pn_FillMax..." << std::endl;
        saveCsv(pn_FillMax(close), outputDir + "/pn_FillMax.csv");

        std::cout << "  测试 pn_FillMin..." << std::endl;
        saveCsv(pn_FillMin(close), outputDir + "/pn_FillMin.csv");

        std::cout << "  测试 pn_TransStd..." << std::endl;
        saveCsv(pn_TransStd(close), outputDir + "/pn_TransStd.csv");

        std::cout << "  测试 pn_Winsor..." << std::endl;
        saveCsv(pn_Winsor(close, 3.0), outputDir + "/pn_Winsor.csv");

        std::cout << "  测试 pn_Cut..." << std::endl;
        saveCsv(pn_Cut(close), outputDir + "/pn_Cut.csv");

        std::cout << "  测试 pn_CrossFit..." << std::endl;
        saveCsv(pn_CrossFit(close, open), outputDir + "/pn_CrossFit.csv");

        std::cout << "  测试 Tot_Mean..." << std::endl;
        saveCsv(Tot_Mean(close), outputDir + "/Tot_Mean.csv");

        std::cout << "  测试 Tot_Sum..." << std::endl;
        saveCsv(Tot_Sum(close), outputDir + "/Tot_Sum.csv");

        std::cout << "  测试 Tot_Stdev..." << std::endl;
        saveCsv(Tot_Stdev(close), outputDir + "/Tot_Stdev.csv");

        std::cout << "  测试 Tot_Delta..." << std::endl;
        saveCsv(Tot_Delta(close), outputDir + "/Tot_Delta.csv");

        std::cout << "  测试 Tot_Divide..." << std::endl;
        saveCsv(Tot_Divide(close), outputDir + "/Tot_Divide.csv");

        std::cout << "  测试 Tot_ChgRate..." << std::endl;
        saveCsv(Tot_ChgRate(close), outputDir + "/Tot_ChgRate.csv");

        std::cout << "  测试 Tot_Rank..." << std::endl;
        saveCsv(Tot_Rank(close), outputDir + "/Tot_Rank.csv");

        std::cout << "  测试 Tot_ArgMax..." << std::endl;
        saveCsv(Tot_ArgMax(close), outputDir + "/Tot_ArgMax.csv");

        std::cout << "  测试 Tot_ArgMin..." << std::endl;
        saveCsv(Tot_ArgMin(close), outputDir + "/Tot_ArgMin.csv");

        std::cout << "  测试 Tot_Max..." << std::endl;
        saveCsv(Tot_Max(close), outputDir + "/Tot_Max.csv");

        std::cout << "  测试 Tot_Min..." << std::endl;
        saveCsv(Tot_Min(close), outputDir + "/Tot_Min.csv");

    } catch (const std::exception& e) {
        std::cerr << "Panel Ops 测试错误: " << e.what() << std::endl;
    }
}

// 运行分组算子测试
void testGroupOps(const DataFrame& close, const DataFrame& group, const std::string& outputDir) {
    std::cout << "\n=== Group Operations Test ===" << std::endl;

    try {
        std::cout << "  测试 pn_GroupRank..." << std::endl;
        saveCsv(pn_GroupRank(close, group), outputDir + "/pn_GroupRank.csv");

        std::cout << "  测试 pn_GroupNorm..." << std::endl;
        saveCsv(pn_GroupNorm(close, group), outputDir + "/pn_GroupNorm.csv");

        std::cout << "  测试 pn_GroupNeutral..." << std::endl;
        saveCsv(pn_GroupNeutral(close, group), outputDir + "/pn_GroupNeutral.csv");

    } catch (const std::exception& e) {
        std::cerr << "Group Ops 测试错误: " << e.what() << std::endl;
    }
}

int main(int argc, char* argv[]) {
    std::string category = "all";
    std::string dataDir = "../../test_right/test_data";
    std::string outputBaseDir = "../test_right/results/cpp";

    // 解析命令行参数
    if (argc > 1) {
        category = argv[1];
    }

    std::cout << "Feature Operators Correctness Test Suite (C++)" << std::endl;
    std::cout << "===============================================" << std::endl;
    std::cout << "分类: " << category << std::endl;
    std::cout << "数据目录: " << dataDir << std::endl;

    // 加载数据
    DataFrame open = loadCsv(dataDir + "/open.csv");
    DataFrame high = loadCsv(dataDir + "/high.csv");
    DataFrame low = loadCsv(dataDir + "/low.csv");
    DataFrame close = loadCsv(dataDir + "/close.csv");
    DataFrame volume = loadCsv(dataDir + "/volume.csv");

    if (open.rows() == 0 || high.rows() == 0 || low.rows() == 0 ||
        close.rows() == 0 || volume.rows() == 0) {
        std::cerr << "加载数据失败!" << std::endl;
        return 1;
    }

    std::cout << "数据加载成功: " << open.rows() << " 行, " << open.cols() << " 列" << std::endl;

    // 创建分组标签
    DataFrame group = DataFrame::Zero(close.rows(), close.cols());
    for (int j = 0; j < close.cols(); ++j) {
        group.col(j).setConstant(j % 5); // 将列分为5组
    }

    // 根据分类运行不同的测试
    if (category == "core_math" || category == "all") {
        testCoreMath(close, open, volume, outputBaseDir + "/core_math");
    }

    if (category == "logical_ops" || category == "all") {
        testLogicalOps(close, open, outputBaseDir + "/logical_ops");
    }

    if (category == "comparison_ops" || category == "all") {
        testComparisonOps(close, open, outputBaseDir + "/comparison_ops");
    }

    if (category == "data_utils" || category == "all") {
        testDataUtils(close, outputBaseDir + "/data_utils");
    }

    if (category == "reduction_ops" || category == "all") {
        testReductionOps(close, open, outputBaseDir + "/reduction_ops");
    }

    if (category == "timeseries_ops" || category == "all") {
        testTimeseriesOps(close, open, high, volume, outputBaseDir + "/timeseries_ops");
    }

    if (category == "panel_ops" || category == "all") {
        testPanelOps(close, open, outputBaseDir + "/panel_ops");
    }

    if (category == "group_ops" || category == "all") {
        testGroupOps(close, group, outputBaseDir + "/group_ops");
    }

    std::cout << "\nC++ 正确性测试完成!" << std::endl;
    return 0;
}
