#include "../../include/feature_operators/core_math.hpp"
#include "../../include/feature_operators/logical_ops.hpp"
#include "../../include/feature_operators/comparison_ops.hpp"
#include "../../include/feature_operators/data_utils.hpp"
#include "../../include/feature_operators/reduction_ops.hpp"
#include "../../include/feature_operators/timeseries_ops.hpp"
#include "../../include/feature_operators/timeseries_ops_v2.hpp"
#include "../../include/feature_operators/panel_ops.hpp"
#include "../../include/feature_operators/group_ops.hpp"
#include <iostream>
#include <iomanip>
#include <vector>
#include <string>
#include <cmath>
#include <fstream>
#include <sstream>
#include <chrono>
#include <map>

using namespace feature_operators;

// 从CSV文件加载数据
DataFrame loadCsv(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filepath << std::endl;
        return DataFrame();
    }

    std::vector<std::vector<double>> data;
    std::string line;

    // 跳过标题行
    std::getline(file, line);

    // 读取数据行
    while (std::getline(file, line)) {
        std::vector<double> row;
        std::stringstream ss(line);
        std::string cell;

        // 跳过第一列（日期）
        std::getline(ss, cell, ',');

        // 读取数据列
        while (std::getline(ss, cell, ',')) {
            try {
                double value = std::stod(cell);
                row.push_back(value);
            } catch (const std::exception& e) {
                row.push_back(std::numeric_limits<double>::quiet_NaN());
            }
        }

        if (!row.empty()) {
            data.push_back(row);
        }
    }

    // 创建Eigen矩阵
    int rows = data.size();
    int cols = rows > 0 ? data[0].size() : 0;

    DataFrame matrix(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            matrix(i, j) = data[i][j];
        }
    }

    return matrix;
}

// 创建目录的辅助函数
void createDirectoryIfNotExists(const std::string& dirPath) {
    std::string command = "mkdir -p " + dirPath;
    int result = system(command.c_str());
    if (result != 0) {
        std::cerr << "警告: 无法创建目录 " << dirPath << std::endl;
    }
}

// 将结果保存为JSON文件
void saveResultsToJson(const std::map<std::string, double>& timings, const std::string& filepath) {
    // 提取目录路径并创建目录
    size_t lastSlash = filepath.find_last_of('/');
    if (lastSlash != std::string::npos) {
        std::string dirPath = filepath.substr(0, lastSlash);
        createDirectoryIfNotExists(dirPath);
    }

    std::ofstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filepath << std::endl;
        return;
    }

    file << "{\n";
    bool first = true;
    for (const auto& pair : timings) {
        if (!first) {
            file << ",\n";
        }
        first = false;
        file << "  \"" << pair.first << "\": " << std::fixed << std::setprecision(6) << pair.second;
    }
    file << "\n}";
    file.close();

    std::cout << "结果已保存到: " << filepath << std::endl;
}

// 测量函数执行时间的辅助函数（微秒）
template<typename Func>
double measureExecutionTime(Func func, int iterations) {
    // 预热
    func();

    auto start = std::chrono::high_resolution_clock::now();

    // 多次执行以获得更准确的时间
    for (int i = 0; i < iterations; ++i) {
        func();
    }

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::micro> duration = end - start;

    // 返回平均执行时间（微秒）
    return duration.count() / iterations;
}
// 运行核心数学算子性能测试
void benchmarkCoreMath(const DataFrame& close, const DataFrame& open, const DataFrame& volume,
                      std::map<std::string, double>& timings, int iterations) {
    std::cout << "\n=== Core Math Operations Benchmark ===" << std::endl;

    timings["Add"] = measureExecutionTime([&]() {
        DataFrame result = Add(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Add" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Add"] << " μs" << std::endl;

    timings["Minus"] = measureExecutionTime([&]() {
        DataFrame result = Minus(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Minus" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Minus"] << " μs" << std::endl;

    timings["Multiply"] = measureExecutionTime([&]() {
        DataFrame result = Multiply(close, volume);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Multiply" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Multiply"] << " μs" << std::endl;

    timings["Divide"] = measureExecutionTime([&]() {
        DataFrame result = Divide(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Divide" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Divide"] << " μs" << std::endl;

    timings["Sqrt"] = measureExecutionTime([&]() {
        DataFrame result = Sqrt(volume);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Sqrt" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Sqrt"] << " μs" << std::endl;

    timings["Log"] = measureExecutionTime([&]() {
        DataFrame result = Log(volume);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Log" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Log"] << " μs" << std::endl;

    timings["Abs"] = measureExecutionTime([&]() {
        DataFrame result = Abs(Minus(close, open));
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Abs" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Abs"] << " μs" << std::endl;

    timings["Power"] = measureExecutionTime([&]() {
        DataFrame result = Power(close, 2);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Power" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Power"] << " μs" << std::endl;

    timings["Sign"] = measureExecutionTime([&]() {
        DataFrame result = Sign(Minus(close, open));
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Sign" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Sign"] << " μs" << std::endl;

    timings["Exp"] = measureExecutionTime([&]() {
        DataFrame result = Exp(Log(close));
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Exp" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Exp"] << " μs" << std::endl;

    timings["inv"] = measureExecutionTime([&]() {
        DataFrame result = inv(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "inv" << std::setw(15) << std::fixed << std::setprecision(3) << timings["inv"] << " μs" << std::endl;

    timings["Reverse"] = measureExecutionTime([&]() {
        DataFrame result = Reverse(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Reverse" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Reverse"] << " μs" << std::endl;

    timings["Ceil"] = measureExecutionTime([&]() {
        DataFrame result = Ceil(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Ceil" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Ceil"] << " μs" << std::endl;

    timings["Floor"] = measureExecutionTime([&]() {
        DataFrame result = Floor(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Floor" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Floor"] << " μs" << std::endl;

    timings["Round"] = measureExecutionTime([&]() {
        DataFrame result = Round(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Round" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Round"] << " μs" << std::endl;

    timings["SignedPower"] = measureExecutionTime([&]() {
        DataFrame result = SignedPower(close, 2.0);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "SignedPower" << std::setw(15) << std::fixed << std::setprecision(3) << timings["SignedPower"] << " μs" << std::endl;

    timings["Softsign"] = measureExecutionTime([&]() {
        DataFrame result = Softsign(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Softsign" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Softsign"] << " μs" << std::endl;
}

// 运行逻辑算子性能测试
void benchmarkLogicalOps(const DataFrame& close, const DataFrame& open,
                        std::map<std::string, double>& timings, int iterations) {
    std::cout << "\n=== Logical Operations Benchmark ===" << std::endl;

    timings["And"] = measureExecutionTime([&]() {
        DataFrame result = And(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "And" << std::setw(15) << std::fixed << std::setprecision(3) << timings["And"] << " μs" << std::endl;

    timings["Or"] = measureExecutionTime([&]() {
        DataFrame result = Or(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Or" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Or"] << " μs" << std::endl;

    timings["Not"] = measureExecutionTime([&]() {
        DataFrame result = Not(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Not" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Not"] << " μs" << std::endl;

    timings["Xor"] = measureExecutionTime([&]() {
        DataFrame result = Xor(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Xor" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Xor"] << " μs" << std::endl;
}

// 运行比较算子性能测试
void benchmarkComparisonOps(const DataFrame& close, const DataFrame& open,
                           std::map<std::string, double>& timings, int iterations) {
    std::cout << "\n=== Comparison Operations Benchmark ===" << std::endl;

    timings["Equal"] = measureExecutionTime([&]() {
        DataFrame result = Equal(close, open);
        // volatile double dummy = result(0, 0);
        if (result.rows() > 0 && result.cols() > 0) { // 或者更简单的 result.size() > 0
        volatile double dummy = result(0, 0);
    } else {
        // 在这里处理 result 为空的情况，例如打印一条警告或跳过 dummy 赋值
        std::cerr << "Warning: 'result' DataFrame is empty or too small." << std::endl;
    }
    }, iterations);
    std::cout << std::setw(20) << "Equal" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Equal"] << " μs" << std::endl;

    timings["Mthan"] = measureExecutionTime([&]() {
        DataFrame result = Mthan(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Mthan" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Mthan"] << " μs" << std::endl;

    timings["Lthan"] = measureExecutionTime([&]() {
        DataFrame result = Lthan(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Lthan" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Lthan"] << " μs" << std::endl;

    timings["UnEqual"] = measureExecutionTime([&]() {
        DataFrame result = UnEqual(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "UnEqual" << std::setw(15) << std::fixed << std::setprecision(3) << timings["UnEqual"] << " μs" << std::endl;

    timings["MEthan"] = measureExecutionTime([&]() {
        DataFrame result = MEthan(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "MEthan" << std::setw(15) << std::fixed << std::setprecision(3) << timings["MEthan"] << " μs" << std::endl;

    timings["LEthan"] = measureExecutionTime([&]() {
        DataFrame result = LEthan(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "LEthan" << std::setw(15) << std::fixed << std::setprecision(3) << timings["LEthan"] << " μs" << std::endl;
}

// 运行数据工具性能测试
void benchmarkDataUtils(const DataFrame& close, std::map<std::string, double>& timings, int iterations) {
    std::cout << "\n=== Data Utils Benchmark ===" << std::endl;

    timings["FilterInf"] = measureExecutionTime([&]() {
        DataFrame result = FilterInf(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "FilterInf" << std::setw(15) << std::fixed << std::setprecision(3) << timings["FilterInf"] << " μs" << std::endl;

    timings["FillNan"] = measureExecutionTime([&]() {
        DataFrame result = FillNan(close, 0.0);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "FillNan" << std::setw(15) << std::fixed << std::setprecision(3) << timings["FillNan"] << " μs" << std::endl;

    timings["getNan"] = measureExecutionTime([&]() {
        DataFrame result = getNan(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "getNan" << std::setw(15) << std::fixed << std::setprecision(3) << timings["getNan"] << " μs" << std::endl;

    timings["getInf"] = measureExecutionTime([&]() {
        DataFrame result = getInf(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "getInf" << std::setw(15) << std::fixed << std::setprecision(3) << timings["getInf"] << " μs" << std::endl;
}

// 运行归约算子性能测试
void benchmarkReductionOps(const DataFrame& close, const DataFrame& open,
                          std::map<std::string, double>& timings, int iterations) {
    std::cout << "\n=== Reduction Operations Benchmark ===" << std::endl;

    timings["Min"] = measureExecutionTime([&]() {
        DataFrame result = Min(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Min" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Min"] << " μs" << std::endl;

    timings["Max"] = measureExecutionTime([&]() {
        DataFrame result = Max(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Max" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Max"] << " μs" << std::endl;
}

// 运行时间序列算子性能测试
void benchmarkTimeSeriesOps(const DataFrame& close, const DataFrame& open, const DataFrame& volume,
                           std::map<std::string, double>& timings, int iterations) {
    std::cout << "\n=== Time Series Operations Benchmark ===" << std::endl;

    timings["ts_Delay"] = measureExecutionTime([&]() {
        DataFrame result = ts_Delay(close, 5);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Delay" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Delay"] << " μs" << std::endl;

    timings["ts_Mean"] = measureExecutionTime([&]() {
        DataFrame result = ts_Mean(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Mean" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Mean"] << " μs" << std::endl;

    timings["ts_Sum"] = measureExecutionTime([&]() {
        DataFrame result = ts_Sum(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Sum" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Sum"] << " μs" << std::endl;

    timings["ts_Stdev"] = measureExecutionTime([&]() {
        DataFrame result = ts_Stdev(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Stdev" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Stdev"] << " μs" << std::endl;

    timings["ts_Corr"] = measureExecutionTime([&]() {
        DataFrame result = ts_Corr(close, volume, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Corr" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Corr"] << " μs" << std::endl;

    timings["ts_Min"] = measureExecutionTime([&]() {
        DataFrame result = ts_Min(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Min" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Min"] << " μs" << std::endl;

    timings["ts_Max"] = measureExecutionTime([&]() {
        DataFrame result = ts_Max(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Max" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Max"] << " μs" << std::endl;

    timings["ts_Delta"] = measureExecutionTime([&]() {
        DataFrame result = ts_Delta(close, 5);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Delta" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Delta"] << " μs" << std::endl;

    timings["ts_Divide"] = measureExecutionTime([&]() {
        DataFrame result = ts_Divide(close, 5);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Divide" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Divide"] << " μs" << std::endl;

    timings["ts_ChgRate"] = measureExecutionTime([&]() {
        DataFrame result = ts_ChgRate(close, 5);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_ChgRate" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_ChgRate"] << " μs" << std::endl;

    timings["ts_Argmax"] = measureExecutionTime([&]() {
        DataFrame result = ts_Argmax(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Argmax" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Argmax"] << " μs" << std::endl;

    timings["ts_Argmin"] = measureExecutionTime([&]() {
        DataFrame result = ts_Argmin(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Argmin" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Argmin"] << " μs" << std::endl;

    timings["ts_Rank"] = measureExecutionTime([&]() {
        DataFrame result = ts_Rank(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Rank" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Rank"] << " μs" << std::endl;

    timings["ts_Median"] = measureExecutionTime([&]() {
        DataFrame result = ts_Median(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Median" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Median"] << " μs" << std::endl;

    timings["ts_Cov"] = measureExecutionTime([&]() {
        DataFrame result = ts_Cov(close, volume, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Cov" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Cov"] << " μs" << std::endl;

    timings["ts_Skewness"] = measureExecutionTime([&]() {
        DataFrame result = ts_Skewness(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Skewness" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Skewness"] << " μs" << std::endl;

    timings["ts_Kurtosis"] = measureExecutionTime([&]() {
        DataFrame result = ts_Kurtosis(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Kurtosis" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Kurtosis"] << " μs" << std::endl;

    timings["ts_Scale"] = measureExecutionTime([&]() {
        DataFrame result = ts_Scale(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Scale" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Scale"] << " μs" << std::endl;

    timings["ts_Product"] = measureExecutionTime([&]() {
        DataFrame result = ts_Product(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Product" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Product"] << " μs" << std::endl;

    timings["ts_TransNorm"] = measureExecutionTime([&]() {
        DataFrame result = ts_TransNorm(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_TransNorm" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_TransNorm"] << " μs" << std::endl;

    timings["ts_Decay"] = measureExecutionTime([&]() {
        DataFrame result = ts_Decay(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Decay" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Decay"] << " μs" << std::endl;

    timings["ts_Decay2"] = measureExecutionTime([&]() {
        DataFrame result = ts_Decay2(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Decay2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Decay2"] << " μs" << std::endl;

    timings["ts_Partial_corr"] = measureExecutionTime([&]() {
        DataFrame result = ts_Partial_corr(close, open, volume, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Partial_corr" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Partial_corr"] << " μs" << std::endl;

    timings["ts_Regression_A"] = measureExecutionTime([&]() {
        DataFrame result = ts_Regression(close, open, 10, 'A');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Regression_A" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Regression_A"] << " μs" << std::endl;

    timings["ts_Regression_B"] = measureExecutionTime([&]() {
        DataFrame result = ts_Regression(close, open, 10, 'B');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Regression_B" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Regression_B"] << " μs" << std::endl;

    timings["ts_Regression_C"] = measureExecutionTime([&]() {
        DataFrame result = ts_Regression(close, open, 10, 'C');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Regression_C" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Regression_C"] << " μs" << std::endl;

    timings["ts_Regression_D"] = measureExecutionTime([&]() {
        DataFrame result = ts_Regression(close, open, 10, 'D');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Regression_D" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Regression_D"] << " μs" << std::endl;

    timings["ts_Entropy"] = measureExecutionTime([&]() {
        DataFrame result = ts_Entropy(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Entropy" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Entropy"] << " μs" << std::endl;

    timings["ts_MaxDD"] = measureExecutionTime([&]() {
        DataFrame result = ts_MaxDD(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_MaxDD" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_MaxDD"] << " μs" << std::endl;

    timings["ts_MeanChg"] = measureExecutionTime([&]() {
        DataFrame result = ts_MeanChg(close, 10);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_MeanChg" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_MeanChg"] << " μs" << std::endl;

    timings["ts_Quantile_A"] = measureExecutionTime([&]() {
        DataFrame result = ts_Quantile(close, 10, 'A');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Quantile_A" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Quantile_A"] << " μs" << std::endl;

    timings["ts_Quantile_B"] = measureExecutionTime([&]() {
        DataFrame result = ts_Quantile(close, 10, 'B');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Quantile_B" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Quantile_B"] << " μs" << std::endl;

    timings["ts_Quantile_C"] = measureExecutionTime([&]() {
        DataFrame result = ts_Quantile(close, 10, 'C');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Quantile_C" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Quantile_C"] << " μs" << std::endl;

    timings["ts_Quantile_D"] = measureExecutionTime([&]() {
        DataFrame result = ts_Quantile(close, 10, 'D');
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "ts_Quantile_D" << std::setw(15) << std::fixed << std::setprecision(3) << timings["ts_Quantile_D"] << " μs" << std::endl;
}

// 运行面板算子性能测试
void benchmarkPanelOps(const DataFrame& close, const DataFrame& open, const DataFrame& volume,
                      std::map<std::string, double>& timings, int iterations) {
    std::cout << "\n=== Panel Operations Benchmark ===" << std::endl;

    timings["pn_Mean"] = measureExecutionTime([&]() {
        DataFrame result = pn_Mean(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_Mean" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_Mean"] << " μs" << std::endl;

    timings["pn_Rank"] = measureExecutionTime([&]() {
        DataFrame result = pn_Rank(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_Rank" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_Rank"] << " μs" << std::endl;

    timings["Tot_Mean"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Mean(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Mean" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Mean"] << " μs" << std::endl;

    timings["Tot_Sum"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Sum(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Sum" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Sum"] << " μs" << std::endl;

    timings["Tot_Rank"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Rank(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Rank" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Rank"] << " μs" << std::endl;

    timings["pn_Stand"] = measureExecutionTime([&]() {
        DataFrame result = pn_Stand(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_Stand" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_Stand"] << " μs" << std::endl;

    timings["pn_TransNorm"] = measureExecutionTime([&]() {
        DataFrame result = pn_TransNorm(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_TransNorm" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_TransNorm"] << " μs" << std::endl;

    timings["pn_Rank2"] = measureExecutionTime([&]() {
        DataFrame result = pn_Rank2(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_Rank2" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_Rank2"] << " μs" << std::endl;

    timings["pn_RankCentered"] = measureExecutionTime([&]() {
        DataFrame result = pn_RankCentered(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_RankCentered" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_RankCentered"] << " μs" << std::endl;

    timings["pn_FillMax"] = measureExecutionTime([&]() {
        DataFrame result = pn_FillMax(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_FillMax" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_FillMax"] << " μs" << std::endl;

    timings["pn_FillMin"] = measureExecutionTime([&]() {
        DataFrame result = pn_FillMin(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_FillMin" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_FillMin"] << " μs" << std::endl;

    timings["pn_TransStd"] = measureExecutionTime([&]() {
        DataFrame result = pn_TransStd(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_TransStd" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_TransStd"] << " μs" << std::endl;

    timings["pn_Winsor"] = measureExecutionTime([&]() {
        DataFrame result = pn_Winsor(close, 3.0);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_Winsor" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_Winsor"] << " μs" << std::endl;

    timings["pn_Cut"] = measureExecutionTime([&]() {
        DataFrame result = pn_Cut(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_Cut" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_Cut"] << " μs" << std::endl;

    timings["pn_CrossFit"] = measureExecutionTime([&]() {
        DataFrame result = pn_CrossFit(close, open);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_CrossFit" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_CrossFit"] << " μs" << std::endl;

    timings["Tot_Stdev"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Stdev(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Stdev" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Stdev"] << " μs" << std::endl;

    timings["Tot_Delta"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Delta(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Delta" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Delta"] << " μs" << std::endl;

    timings["Tot_Divide"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Divide(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Divide" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Divide"] << " μs" << std::endl;

    timings["Tot_ChgRate"] = measureExecutionTime([&]() {
        DataFrame result = Tot_ChgRate(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_ChgRate" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_ChgRate"] << " μs" << std::endl;

    timings["Tot_ArgMax"] = measureExecutionTime([&]() {
        DataFrame result = Tot_ArgMax(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_ArgMax" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_ArgMax"] << " μs" << std::endl;

    timings["Tot_ArgMin"] = measureExecutionTime([&]() {
        DataFrame result = Tot_ArgMin(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_ArgMin" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_ArgMin"] << " μs" << std::endl;

    timings["Tot_Max"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Max(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Max" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Max"] << " μs" << std::endl;

    timings["Tot_Min"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Min(close);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "Tot_Min" << std::setw(15) << std::fixed << std::setprecision(3) << timings["Tot_Min"] << " μs" << std::endl;
     timings["complex"] = measureExecutionTime([&]() {
        DataFrame result = Tot_Mean(IfThen(ts_Sum(IfThen(Tot_Rank(volume-ts_Delay(volume,1))-0.9,1,0),10),close/ts_Delay(close,1)-1,getNan(close/ts_Delay(close,1)-1)));
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "complex" << std::setw(15) << std::fixed << std::setprecision(3) << timings["complex"] << " μs" << std::endl;
}

// 运行分组算子性能测试
void benchmarkGroupOps(const DataFrame& close, const DataFrame& group,
                      std::map<std::string, double>& timings, int iterations) {
    std::cout << "\n=== Group Operations Benchmark ===" << std::endl;

    timings["pn_GroupRank"] = measureExecutionTime([&]() {
        DataFrame result = pn_GroupRank(close, group);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_GroupRank" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_GroupRank"] << " μs" << std::endl;

    timings["pn_GroupNorm"] = measureExecutionTime([&]() {
        DataFrame result = pn_GroupNorm(close, group);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_GroupNorm" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_GroupNorm"] << " μs" << std::endl;

    timings["pn_GroupNeutral"] = measureExecutionTime([&]() {
        DataFrame result = pn_GroupNeutral(close, group);
        volatile double dummy = result(0, 0);
    }, iterations);
    std::cout << std::setw(20) << "pn_GroupNeutral" << std::setw(15) << std::fixed << std::setprecision(3) << timings["pn_GroupNeutral"] << " μs" << std::endl;
}

// 运行单个分类的性能测试
bool runBenchmarkTest(const std::string& category, const DataFrame& close, const DataFrame& open,
                     const DataFrame& volume, const DataFrame& group,
                     const std::string& outputDir, int iterations) {
    std::cout << std::string(80, '=') << std::endl;
    std::cout << category << " Operations Performance Benchmark (C++)" << std::endl;
    std::cout << std::string(80, '=') << std::endl;

    std::map<std::string, double> timings;

    if (category == "core_math") {
        benchmarkCoreMath(close, open, volume, timings, iterations);
    } else if (category == "logical_ops") {
        benchmarkLogicalOps(close, open, timings, iterations);
    } else if (category == "comparison_ops") {
        benchmarkComparisonOps(close, open, timings, iterations);
    } else if (category == "data_utils") {
        benchmarkDataUtils(close, timings, iterations);
    } else if (category == "reduction_ops") {
        benchmarkReductionOps(close, open, timings, iterations);
    } else if (category == "timeseries_ops") {
        benchmarkTimeSeriesOps(close, open, volume, timings, iterations);
    } else if (category == "panel_ops") {
        benchmarkPanelOps(close, open, volume,timings, iterations);
    } else if (category == "group_ops") {
        benchmarkGroupOps(close, group, timings, iterations);
    } else {
        std::cerr << "未知分类: " << category << std::endl;
        return false;
    }

    // 保存结果
    std::string outputFile = outputDir + "/" + category + "_benchmark.json";
    saveResultsToJson(timings, outputFile);

    std::cout << "\n" << category << " 性能测试完成!" << std::endl;
    return true;
}

int main(int argc, char* argv[]) {
    std::string category = "all";
    std::string dataDir = "../../test_right_old/test_data";
    std::string outputDir = "../test_benchmark/results/cpp";
    int iterations = 100;

    // 解析命令行参数
    if (argc > 1) {
        category = argv[1];
    }
    if (argc > 2) {
        iterations = std::stoi(argv[2]);
    }

    std::cout << "Feature Operators Performance Benchmark Suite (C++)" << std::endl;
    std::cout << "====================================================" << std::endl;
    std::cout << "分类: " << category << std::endl;
    std::cout << "迭代次数: " << iterations << std::endl;
    std::cout << "数据目录: " << dataDir << std::endl;

    // 加载数据
    DataFrame open = loadCsv(dataDir + "/open.csv");
    DataFrame high = loadCsv(dataDir + "/high.csv");
    DataFrame low = loadCsv(dataDir + "/low.csv");
    DataFrame close = loadCsv(dataDir + "/close.csv");
    DataFrame volume = loadCsv(dataDir + "/volume.csv");

    if (open.rows() == 0 || high.rows() == 0 || low.rows() == 0 ||
        close.rows() == 0 || volume.rows() == 0) {
        std::cerr << "加载数据失败!" << std::endl;
        return 1;
    }

    std::cout << "数据加载成功: " << open.rows() << " 行, " << open.cols() << " 列" << std::endl;

    // 创建分组标签
    DataFrame group = DataFrame::Zero(close.rows(), close.cols());
    for (int j = 0; j < close.cols(); ++j) {
        group.col(j).setConstant(j % 5); // 将列分为5组
    }

    std::cout << "\n开始性能测试..." << std::endl;

    bool success = true;

    if (category == "all") {
        // 运行所有分类的性能测试
        std::vector<std::string> categories = {
            "core_math", "logical_ops", "comparison_ops", "data_utils",
            "reduction_ops", "timeseries_ops", "panel_ops", "group_ops"
        };

        for (const std::string& cat : categories) {
            std::cout << "\n" << std::string(20, '=') << " " << cat << " " << std::string(20, '=') << std::endl;
            bool catSuccess = runBenchmarkTest(cat, close, open, volume, group, outputDir, iterations);
            success = success && catSuccess;
        }
    } else {
        // 运行指定分类的性能测试
        success = runBenchmarkTest(category, close, open, volume, group, outputDir, iterations);
    }

    if (success) {
        std::cout << "\n🎉 " << category << " 性能测试完成!" << std::endl;
        return 0;
    } else {
        std::cout << "\n❌ " << category << " 性能测试失败" << std::endl;
        return 1;
    }
}
