C++ 性能比较报告 (原始版本 vs 重构版本v2)
============================================================

测试时间: 20250528_185235
基准测试文件: /home/<USER>/git/feature_operators/test_right/test_results/benchmark_cpp_results.json

性能统计:
  平均性能提升: 24.8%
  中位数性能提升: 21.3%
  最大性能提升: 70.7%
  最小性能提升: -15.7%

性能分布:
  性能提升 (>5%): 28 个算子
  性能下降 (<-5%): 1 个算子
  性能相近 (-5%~5%): 9 个算子

详细结果:
------------------------------------------------------------
算子名称                 原版本(μs)         v2版本(μs)        提升率       
------------------------------------------------------------
ts_Delay             270.650         313.218         -15.7     %
ts_Mean              4161.889        3226.362        22.5      %
ts_Sum               3002.059        2010.246        33.0      %
ts_Stdev             6498.957        5052.455        22.3      %
ts_Min               13128.375       4443.028        66.2      %
ts_Max               15242.228       4468.533        70.7      %
ts_Delta             279.615         277.128         0.9       %
ts_Divide            745.000         604.874         18.8      %
ts_ChgRate           744.913         607.329         18.5      %
ts_Argmax            9070.556        4239.762        53.3      %
ts_Argmin            9042.228        4191.185        53.6      %
ts_Rank              51921.061       41652.026       19.8      %
ts_Median            63563.075       63536.884       0.0       %
ts_Corr              5014.065        5039.629        -0.5      %
ts_Cov               2760.686        2764.404        -0.1      %
ts_Skewness          8591.728        5886.840        31.5      %
ts_Kurtosis          11484.224       7656.490        33.3      %
ts_Scale             9495.169        9287.743        2.2       %
ts_Product           8822.813        5326.352        39.6      %
ts_Decay             8481.277        5952.289        29.8      %
ts_Decay2            8150.058        7580.884        7.0       %
ts_MaxDD             9858.534        7163.694        27.3      %
ts_MeanChg           14701.754       12310.859       16.3      %
ts_Quantile_A        72157.076       63860.435       11.5      %
ts_Quantile_B        65723.109       63799.417       2.9       %
ts_Quantile_C        65651.511       63763.435       2.9       %
ts_Quantile_D        65825.247       63812.606       3.1       %
Tot_Mean             4148.738        3216.580        22.5      %
Tot_Sum              3000.941        2015.199        32.8      %
Tot_Stdev            6481.539        5024.814        22.5      %
Tot_Delta            277.238         280.978         -1.3      %
Tot_Divide           738.477         596.353         19.2      %
Tot_ChgRate          738.277         595.424         19.3      %
Tot_Rank             58611.622       46637.608       20.4      %
Tot_ArgMax           10736.047       5465.076        49.1      %
Tot_ArgMin           10736.066       5402.699        49.7      %
Tot_Max              21202.605       6221.375        70.7      %
Tot_Min              18787.304       6207.957        67.0      %
