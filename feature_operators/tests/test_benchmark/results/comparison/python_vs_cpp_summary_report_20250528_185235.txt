Feature Operators 性能比较汇总报告 (Python vs C++)
================================================================================

测试时间: 20250528_185235
总测试分类数: 8
总测试算子数: 91

整体性能统计:
--------------------------------------------------
C++总平均执行时间: 11400.106 μs
Python总平均执行时间: 48520.696 μs
总平均加速比: 8.62x
最大加速比: 58.76x (ts_Regression_A)
最小加速比: 0.29x (Round)

各分类性能汇总:
--------------------------------------------------------------------------------
分类              算子数      C++平均(μs)    Python平均(μs)    平均加速比     
--------------------------------------------------------------------------------
core_math       17       1327.655     1975.873        2.03      
logical_ops     4        460.582      10623.552       23.40     
comparison_ops  6        301.405      1716.803        5.70      
data_utils      3        136.215      573.790         4.34      
reduction_ops   2        299.071      11265.558       37.67     
timeseries_ops  33       21916.546    98578.961       12.67     
panel_ops       23       10400.549    27293.217       3.47      
group_ops       3        15907.762    141298.750      12.02     

前10名最快的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>      <GROUP>  2547.863     149715.305      58.76     
2    ts_Regression_B      timeseries_ops  2639.194     150808.659      57.14     
3    ts_Regression_D      timeseries_ops  3191.165     155007.977      48.57     
4    ts_Regression_C      timeseries_ops  3195.830     151630.622      47.45     
5    ts_Cov               timeseries_ops  2627.829     110376.843      42.00     
6    Max                  reduction_ops   298.244      11251.392       37.73     
7    Min                  reduction_ops   299.898      11279.723       37.61     
8    ts_Corr              timeseries_ops  5782.164     210191.447      36.35     
9    Xor                  logical_ops     504.947      14657.679       29.03     
10   ts_Partial_corr      timeseries_ops  23199.434    634271.801      27.34     

后10名最慢的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>                <GROUP>       997.713      288.673         0.29      
2    pn_Stand             panel_ops       9264.145     4902.830        0.53      
3    Floor                core_math       443.361      301.105         0.68      
4    Ceil                 core_math       441.288      304.994         0.69      
5    pn_FillMin           panel_ops       1151.087     1120.358        0.97      
6    pn_FillMax           panel_ops       1152.035     1137.444        0.99      
7    Exp                  core_math       9011.203     10913.224       1.21      
8    ts_Decay2            timeseries_ops  7730.501     9703.230        1.26      
9    ts_Decay             timeseries_ops  7513.897     9673.118        1.29      
10   pn_TransNorm         panel_ops       27182.325    35471.610       1.30      

================================================================================
按分类详细性能列表:
================================================================================

COMPARISON_OPS (6 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
MEthan               292.811      1734.824        5.92      
LEthan               293.662      1701.534        5.79      
Mthan                296.706      1707.159        5.75      
Lthan                297.504      1701.423        5.72      
UnEqual              311.936      1723.006        5.52      
Equal                315.815      1732.875        5.49      

CORE_MATH (17 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Abs                  219.515      978.599         4.46      
Minus                218.352      716.044         3.28      
Multiply             218.698      704.506         3.22      
Add                  236.426      722.262         3.05      
Sign                 537.991      1498.234        2.78      
Power                163.017      357.907         2.20      
Reverse              163.669      357.129         2.18      
SignedPower          2645.752     5656.874        2.14      
Divide               362.238      762.422         2.10      
Softsign             1008.364     1976.580        1.96      
inv                  362.061      562.293         1.55      
Log                  4911.643     6659.781        1.36      
Sqrt                 628.849      829.219         1.32      
Exp                  9011.203     10913.224       1.21      
Ceil                 441.288      304.994         0.69      
Floor                443.361      301.105         0.68      
Round                997.713      288.673         0.29      

DATA_UTILS (3 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
getNan               105.904      499.027         4.71      
getInf               105.938      481.695         4.55      
FilterInf            196.802      740.650         3.76      

GROUP_OPS (3 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
pn_GroupNeutral      4460.534     84991.961       19.05     
pn_GroupRank         13622.347    140539.696      10.32     
pn_GroupNorm         29640.405    198364.592      6.69      

LOGICAL_OPS (4 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Xor                  504.947      14657.679       29.03     
Not                  331.627      9008.126        27.16     
And                  505.417      10189.889       20.16     
Or                   500.337      8638.515        17.27     

PANEL_OPS (23 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Tot_Sum              2913.341     34843.596       11.96     
Tot_Mean             4344.303     35626.133       8.20      
pn_CrossFit          3613.722     28534.279       7.90      
Tot_Stdev            6699.221     47639.059       7.11      
Tot_ArgMin           11252.406    56199.289       4.99      
Tot_Delta            279.992      1388.788        4.96      
Tot_ArgMax           11266.120    55711.113       4.95      
Tot_Divide           733.718      2641.072        3.60      
pn_Winsor            2751.229     9426.057        3.43      
Tot_ChgRate          738.542      2148.395        2.91      
Tot_Rank             57071.087    147075.396      2.58      
pn_Mean              1249.671     2667.337        2.13      
Tot_Max              18388.754    35553.151       1.93      
pn_Cut               15858.453    26932.114       1.70      
Tot_Min              21467.102    35490.392       1.65      
pn_Rank              12588.481    20575.546       1.63      
pn_Rank2             11591.947    17437.780       1.50      
pn_TransStd          3358.032     4875.282        1.45      
pn_RankCentered      14296.917    20346.980       1.42      
pn_TransNorm         27182.325    35471.610       1.30      
pn_FillMax           1152.035     1137.444        0.99      
pn_FillMin           1151.087     1120.358        0.97      
pn_Stand             9264.145     4902.830        0.53      

REDUCTION_OPS (2 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Max                  298.244      11251.392       37.73     
Min                  299.898      11279.723       37.61     

TIMESERIES_OPS (33 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
ts_Regression_A      2547.863     149715.305      58.76     
ts_Regression_B      2639.194     150808.659      57.14     
ts_Regression_D      3191.165     155007.977      48.57     
ts_Regression_C      3195.830     151630.622      47.45     
ts_Cov               2627.829     110376.843      42.00     
ts_Corr              5782.164     210191.447      36.35     
ts_Partial_corr      23199.434    634271.801      27.34     
ts_Sum               2937.064     34309.354       11.68     
ts_Mean              4403.945     34894.154       7.92      
ts_Scale             9484.741     72651.432       7.66      
ts_Stdev             6817.609     46304.934       6.79      
ts_Argmax            9193.613     50626.608       5.51      
ts_Argmin            9359.152     50628.726       5.41      
ts_Skewness          9576.619     47621.269       4.97      
ts_Product           7511.407     35490.452       4.72      
ts_Delta             290.595      1348.317        4.64      
ts_Kurtosis          12035.450    49175.946       4.09      
ts_Delay             280.348      964.022         3.44      
ts_MeanChg           14245.461    47965.541       3.37      
ts_Divide            754.815      2419.170        3.20      
ts_ChgRate           772.307      2190.315        2.84      
ts_Max               13159.029    34727.625       2.64      
ts_Rank              51340.947    131408.308      2.56      
ts_Quantile_D        64604.744    143513.266      2.22      
ts_Min               15543.423    34466.019       2.22      
ts_TransNorm         67291.501    147814.134      2.20      
ts_Quantile_C        63467.752    139234.544      2.19      
ts_Quantile_B        63608.490    135340.496      2.13      
ts_Median            63501.581    130671.525      2.06      
ts_Quantile_A        63540.425    129340.418      2.04      
ts_Entropy           111097.108   168620.146      1.52      
ts_Decay             7513.897     9673.118        1.29      
ts_Decay2            7730.501     9703.230        1.26      
