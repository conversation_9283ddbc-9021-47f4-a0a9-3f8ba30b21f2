{"timestamp": "20250528_185235", "summary": {"total_categories": 8, "total_operators": 91, "overall_cpp_avg_time": 11400.105527428572, "overall_python_avg_time": 48520.69598407699, "overall_avg_speedup": 8.623063578765088, "max_speedup": 58.76112939800891, "min_speedup": 0.28933442412233906}, "category_summaries": {"core_math": {"total_operators": 17, "cpp_avg_time": 1327.655243, "python_avg_time": 1975.8732021585402, "avg_speedup": 2.0280781322851658, "max_speedup": 4.458014894922334, "min_speedup": 0.28933442412233906}, "logical_ops": {"total_operators": 4, "cpp_avg_time": 460.58202500000004, "python_avg_time": 10623.552122463783, "avg_speedup": 23.4045802066031, "max_speedup": 29.02813543182584, "min_speedup": 17.265403813432172}, "comparison_ops": {"total_operators": 6, "cpp_avg_time": 301.40534983333333, "python_avg_time": 1716.803355763356, "avg_speedup": 5.700371265983172, "max_speedup": 5.924723210159304, "min_speedup": 5.486997142428099}, "data_utils": {"total_operators": 3, "cpp_avg_time": 136.21474433333333, "python_avg_time": 573.7904873159197, "avg_speedup": 4.340814990534725, "max_speedup": 4.712075727325177, "min_speedup": 3.7634181982606165}, "reduction_ops": {"total_operators": 2, "cpp_avg_time": 299.07063300000004, "python_avg_time": 11265.557600806156, "avg_speedup": 37.66870870220984, "max_speedup": 37.72549314573353, "min_speedup": 37.611924258686145}, "timeseries_ops": {"total_operators": 33, "cpp_avg_time": 21916.545533303026, "python_avg_time": 98578.96137041877, "avg_speedup": 12.671833097783429, "max_speedup": 58.76112939800891, "min_speedup": 1.2551877478794964}, "panel_ops": {"total_operators": 23, "cpp_avg_time": 10400.549145, "python_avg_time": 27293.217403517247, "avg_speedup": 3.4698748779119732, "max_speedup": 11.960012301292283, "min_speedup": 0.5292263876721295}, "group_ops": {"total_operators": 3, "cpp_avg_time": 15907.761744333335, "python_avg_time": 141298.7497738666, "avg_speedup": 12.021144067203814, "max_speedup": 19.054211664790383, "min_speedup": 6.692371281347519}}, "detailed_results": [{"Operator": "Abs", "C++_Time_μs": 219.514567, "Python_Time_μs": 978.5992093384266, "Speedup": 4.458014894922334, "Performance_Ratio": "4.46x", "Category": "core_math"}, {"Operator": "Minus", "C++_Time_μs": 218.351533, "Python_Time_μs": 716.0435741146406, "Speedup": 3.2793155343436067, "Performance_Ratio": "3.28x", "Category": "core_math"}, {"Operator": "Multiply", "C++_Time_μs": 218.6977, "Python_Time_μs": 704.5058843990167, "Speedup": 3.221368511872858, "Performance_Ratio": "3.22x", "Category": "core_math"}, {"Operator": "Add", "C++_Time_μs": 236.425533, "Python_Time_μs": 722.2616113722324, "Speedup": 3.054922208305786, "Performance_Ratio": "3.05x", "Category": "core_math"}, {"Operator": "Sign", "C++_Time_μs": 537.990833, "Python_Time_μs": 1498.2335579891999, "Speedup": 2.7848681912191617, "Performance_Ratio": "2.78x", "Category": "core_math"}, {"Operator": "Power", "C++_Time_μs": 163.017233, "Python_Time_μs": 357.9072654247284, "Speedup": 2.19551797584939, "Performance_Ratio": "2.20x", "Category": "core_math"}, {"Operator": "Reverse", "C++_Time_μs": 163.669433, "Python_Time_μs": 357.1285555760066, "Speedup": 2.182011320195669, "Performance_Ratio": "2.18x", "Category": "core_math"}, {"Operator": "SignedPower", "C++_Time_μs": 2645.752133, "Python_Time_μs": 5656.874335060517, "Speedup": 2.1380968627043027, "Performance_Ratio": "2.14x", "Category": "core_math"}, {"Operator": "Divide", "C++_Time_μs": 362.238333, "Python_Time_μs": 762.4215135971705, "Speedup": 2.1047510551490154, "Performance_Ratio": "2.10x", "Category": "core_math"}, {"Operator": "Softsign", "C++_Time_μs": 1008.364167, "Python_Time_μs": 1976.5798933804035, "Speedup": 1.96018458218419, "Performance_Ratio": "1.96x", "Category": "core_math"}, {"Operator": "inv", "C++_Time_μs": 362.060833, "Python_Time_μs": 562.2932376960913, "Speedup": 1.5530352538742882, "Performance_Ratio": "1.55x", "Category": "core_math"}, {"Operator": "Log", "C++_Time_μs": 4911.6434, "Python_Time_μs": 6659.78134299318, "Speedup": 1.3559171138102535, "Performance_Ratio": "1.36x", "Category": "core_math"}, {"Operator": "Sqrt", "C++_Time_μs": 628.848633, "Python_Time_μs": 829.2187936604023, "Speedup": 1.3186301919819907, "Performance_Ratio": "1.32x", "Category": "core_math"}, {"Operator": "Exp", "C++_Time_μs": 9011.2029, "Python_Time_μs": 10913.223897417387, "Speedup": 1.2110729298324185, "Performance_Ratio": "1.21x", "Category": "core_math"}, {"Operator": "Ceil", "C++_Time_μs": 441.287733, "Python_Time_μs": 304.9939249952634, "Speedup": 0.6911452600819598, "Performance_Ratio": "0.69x", "Category": "core_math"}, {"Operator": "Floor", "C++_Time_μs": 443.361067, "Python_Time_μs": 301.105094452699, "Speedup": 0.6791419383982559, "Performance_Ratio": "0.68x", "Category": "core_math"}, {"Operator": "Round", "C++_Time_μs": 997.7131, "Python_Time_μs": 288.6727452278137, "Speedup": 0.28933442412233906, "Performance_Ratio": "0.29x", "Category": "core_math"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 504.9473, "Python_Time_μs": 14657.678610334793, "Speedup": 29.02813543182584, "Performance_Ratio": "29.03x", "Category": "logical_ops"}, {"Operator": "Not", "C++_Time_μs": 331.6268, "Python_Time_μs": 9008.12620917956, "Speedup": 27.163444598505187, "Performance_Ratio": "27.16x", "Category": "logical_ops"}, {"Operator": "And", "C++_Time_μs": 505.4173, "Python_Time_μs": 10189.888502160708, "Speedup": 20.161336982649203, "Performance_Ratio": "20.16x", "Category": "logical_ops"}, {"Operator": "Or", "C++_Time_μs": 500.3367, "Python_Time_μs": 8638.515168180069, "Speedup": 17.265403813432172, "Performance_Ratio": "17.27x", "Category": "logical_ops"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 292.811033, "Python_Time_μs": 1734.824323405822, "Speedup": 5.924723210159304, "Performance_Ratio": "5.92x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON><PERSON>", "C++_Time_μs": 293.661533, "Python_Time_μs": 1701.5340117116768, "Speedup": 5.794201216376803, "Performance_Ratio": "5.79x", "Category": "comparison_ops"}, {"Operator": "Mthan", "C++_Time_μs": 296.705533, "Python_Time_μs": 1707.158548136552, "Speedup": 5.753713221574982, "Performance_Ratio": "5.75x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 297.5036, "Python_Time_μs": 1701.4226876199245, "Speedup": 5.718998652856384, "Performance_Ratio": "5.72x", "Category": "comparison_ops"}, {"Operator": "UnEqual", "C++_Time_μs": 311.935667, "Python_Time_μs": 1723.0060261984665, "Speedup": 5.52359415250346, "Performance_Ratio": "5.52x", "Category": "comparison_ops"}, {"Operator": "Equal", "C++_Time_μs": 315.814733, "Python_Time_μs": 1732.874537507693, "Speedup": 5.486997142428099, "Performance_Ratio": "5.49x", "Category": "comparison_ops"}, {"Operator": "get<PERSON><PERSON>", "C++_Time_μs": 105.903833, "Python_Time_μs": 499.0268809099992, "Speedup": 4.712075727325177, "Performance_Ratio": "4.71x", "Category": "data_utils"}, {"Operator": "getInf", "C++_Time_μs": 105.937933, "Python_Time_μs": 481.6945952673753, "Speedup": 4.546951046018382, "Performance_Ratio": "4.55x", "Category": "data_utils"}, {"Operator": "FilterInf", "C++_Time_μs": 196.802467, "Python_Time_μs": 740.6499857703844, "Speedup": 3.7634181982606165, "Performance_Ratio": "3.76x", "Category": "data_utils"}, {"Operator": "Max", "C++_Time_μs": 298.243733, "Python_Time_μs": 11251.391905049482, "Speedup": 37.72549314573353, "Performance_Ratio": "37.73x", "Category": "reduction_ops"}, {"Operator": "Min", "C++_Time_μs": 299.897533, "Python_Time_μs": 11279.72329656283, "Speedup": 37.611924258686145, "Performance_Ratio": "37.61x", "Category": "reduction_ops"}, {"Operator": "ts_Regression_A", "C++_Time_μs": 2547.862967, "Python_Time_μs": 149715.3054922819, "Speedup": 58.76112939800891, "Performance_Ratio": "58.76x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_B", "C++_Time_μs": 2639.193867, "Python_Time_μs": 150808.65919589996, "Speedup": 57.1419406060252, "Performance_Ratio": "57.14x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_D", "C++_Time_μs": 3191.165233, "Python_Time_μs": 155007.97679026923, "Speedup": 48.574099262339644, "Performance_Ratio": "48.57x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_C", "C++_Time_μs": 3195.830067, "Python_Time_μs": 151630.62242791057, "Speedup": 47.44639710153606, "Performance_Ratio": "47.45x", "Category": "timeseries_ops"}, {"Operator": "ts_Cov", "C++_Time_μs": 2627.828933, "Python_Time_μs": 110376.84278562665, "Speedup": 42.00305484102327, "Performance_Ratio": "42.00x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>rr", "C++_Time_μs": 5782.1642, "Python_Time_μs": 210191.4471325775, "Speedup": 36.3516911423196, "Performance_Ratio": "36.35x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>_corr", "C++_Time_μs": 23199.4337, "Python_Time_μs": 634271.8014804026, "Speedup": 27.33996914245379, "Performance_Ratio": "27.34x", "Category": "timeseries_ops"}, {"Operator": "ts_Sum", "C++_Time_μs": 2937.0641, "Python_Time_μs": 34309.35374150673, "Speedup": 11.681513434285188, "Performance_Ratio": "11.68x", "Category": "timeseries_ops"}, {"Operator": "ts_Mean", "C++_Time_μs": 4403.945333, "Python_Time_μs": 34894.1543760399, "Speedup": 7.923384996305063, "Performance_Ratio": "7.92x", "Category": "timeseries_ops"}, {"Operator": "ts_Scale", "C++_Time_μs": 9484.740567, "Python_Time_μs": 72651.43155430754, "Speedup": 7.659822748034003, "Performance_Ratio": "7.66x", "Category": "timeseries_ops"}, {"Operator": "ts_Stdev", "C++_Time_μs": 6817.6085, "Python_Time_μs": 46304.93440975746, "Speedup": 6.791961493499877, "Performance_Ratio": "6.79x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON><PERSON>", "C++_Time_μs": 9193.612733, "Python_Time_μs": 50626.608387877546, "Speedup": 5.506715353166437, "Performance_Ratio": "5.51x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON><PERSON>", "C++_Time_μs": 9359.152433, "Python_Time_μs": 50628.72643272082, "Speedup": 5.409541814299972, "Performance_Ratio": "5.41x", "Category": "timeseries_ops"}, {"Operator": "ts_Skewness", "C++_Time_μs": 9576.619167, "Python_Time_μs": 47621.269493053354, "Speedup": 4.972659835649633, "Performance_Ratio": "4.97x", "Category": "timeseries_ops"}, {"Operator": "ts_Product", "C++_Time_μs": 7511.406833, "Python_Time_μs": 35490.45184627175, "Speedup": 4.724874132812365, "Performance_Ratio": "4.72x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 290.595133, "Python_Time_μs": 1348.3166384200256, "Speedup": 4.6398459069169125, "Performance_Ratio": "4.64x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 12035.4499, "Python_Time_μs": 49175.94604194164, "Speedup": 4.085925034006551, "Performance_Ratio": "4.09x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 280.348, "Python_Time_μs": 964.0222415328026, "Speedup": 3.4386628102672483, "Performance_Ratio": "3.44x", "Category": "timeseries_ops"}, {"Operator": "ts_MeanChg", "C++_Time_μs": 14245.461267, "Python_Time_μs": 47965.541326751314, "Speedup": 3.367075339137301, "Performance_Ratio": "3.37x", "Category": "timeseries_ops"}, {"Operator": "ts_Divide", "C++_Time_μs": 754.814733, "Python_Time_μs": 2419.1696817676225, "Speedup": 3.204984714795733, "Performance_Ratio": "3.20x", "Category": "timeseries_ops"}, {"Operator": "ts_ChgRate", "C++_Time_μs": 772.306667, "Python_Time_μs": 2190.3153819342456, "Speedup": 2.8360694987166877, "Performance_Ratio": "2.84x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 13159.029067, "Python_Time_μs": 34727.62471064925, "Speedup": 2.639071966011431, "Performance_Ratio": "2.64x", "Category": "timeseries_ops"}, {"Operator": "ts_Rank", "C++_Time_μs": 51340.947133, "Python_Time_μs": 131408.30785656968, "Speedup": 2.559522470751332, "Performance_Ratio": "2.56x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_D", "C++_Time_μs": 64604.743933, "Python_Time_μs": 143513.26559359828, "Speedup": 2.2214044489121787, "Performance_Ratio": "2.22x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 15543.422767, "Python_Time_μs": 34466.01942802469, "Speedup": 2.2174021735546536, "Performance_Ratio": "2.22x", "Category": "timeseries_ops"}, {"Operator": "ts_TransNorm", "C++_Time_μs": 67291.501, "Python_Time_μs": 147814.1344152391, "Speedup": 2.1966241236800332, "Performance_Ratio": "2.20x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_C", "C++_Time_μs": 63467.7524, "Python_Time_μs": 139234.54424366355, "Speedup": 2.19378407110039, "Performance_Ratio": "2.19x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_B", "C++_Time_μs": 63608.4905, "Python_Time_μs": 135340.49646308026, "Speedup": 2.1277111813096754, "Performance_Ratio": "2.13x", "Category": "timeseries_ops"}, {"Operator": "ts_Median", "C++_Time_μs": 63501.5808, "Python_Time_μs": 130671.52490839362, "Speedup": 2.057768062813227, "Performance_Ratio": "2.06x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_A", "C++_Time_μs": 63540.4255, "Python_Time_μs": 129340.41771416862, "Speedup": 2.035561088809023, "Performance_Ratio": "2.04x", "Category": "timeseries_ops"}, {"Operator": "ts_Entropy", "C++_Time_μs": 111097.1078, "Python_Time_μs": 168620.14550715685, "Speedup": 1.5177725941408968, "Performance_Ratio": "1.52x", "Category": "timeseries_ops"}, {"Operator": "ts_Decay", "C++_Time_μs": 7513.896833, "Python_Time_μs": 9673.117970426878, "Speedup": 1.2873636922913123, "Performance_Ratio": "1.29x", "Category": "timeseries_ops"}, {"Operator": "ts_Decay2", "C++_Time_μs": 7730.500533, "Python_Time_μs": 9703.229553997517, "Speedup": 1.2551877478794964, "Performance_Ratio": "1.26x", "Category": "timeseries_ops"}, {"Operator": "Tot_Sum", "C++_Time_μs": 2913.341167, "Python_Time_μs": 34843.59619518121, "Speedup": 11.960012301292283, "Performance_Ratio": "11.96x", "Category": "panel_ops"}, {"Operator": "Tot_Mean", "C++_Time_μs": 4344.302667, "Python_Time_μs": 35626.13272418579, "Speedup": 8.200656228399426, "Performance_Ratio": "8.20x", "Category": "panel_ops"}, {"Operator": "pn_CrossFit", "C++_Time_μs": 3613.721967, "Python_Time_μs": 28534.278832376003, "Speedup": 7.896091368662841, "Performance_Ratio": "7.90x", "Category": "panel_ops"}, {"Operator": "Tot_Stdev", "C++_Time_μs": 6699.2208, "Python_Time_μs": 47639.058685551085, "Speedup": 7.111134280803386, "Performance_Ratio": "7.11x", "Category": "panel_ops"}, {"Operator": "Tot_Arg<PERSON>in", "C++_Time_μs": 11252.406133, "Python_Time_μs": 56199.28864762187, "Speedup": 4.994424124348469, "Performance_Ratio": "4.99x", "Category": "panel_ops"}, {"Operator": "Tot_Delta", "C++_Time_μs": 279.991967, "Python_Time_μs": 1388.7878507375717, "Speedup": 4.960098911471884, "Performance_Ratio": "4.96x", "Category": "panel_ops"}, {"Operator": "Tot_ArgMax", "C++_Time_μs": 11266.119933, "Python_Time_μs": 55711.113257954516, "Speedup": 4.945013331055448, "Performance_Ratio": "4.95x", "Category": "panel_ops"}, {"Operator": "Tot_Divide", "C++_Time_μs": 733.718267, "Python_Time_μs": 2641.0721242427826, "Speedup": 3.5995725376192422, "Performance_Ratio": "3.60x", "Category": "panel_ops"}, {"Operator": "pn_Winsor", "C++_Time_μs": 2751.228967, "Python_Time_μs": 9426.056655744711, "Speedup": 3.4261258400543406, "Performance_Ratio": "3.43x", "Category": "panel_ops"}, {"Operator": "Tot_ChgRate", "C++_Time_μs": 738.541933, "Python_Time_μs": 2148.395280043284, "Speedup": 2.908968582618428, "Performance_Ratio": "2.91x", "Category": "panel_ops"}, {"Operator": "Tot_Rank", "C++_Time_μs": 57071.086833, "Python_Time_μs": 147075.39631053805, "Speedup": 2.5770561675285144, "Performance_Ratio": "2.58x", "Category": "panel_ops"}, {"Operator": "pn_Mean", "C++_Time_μs": 1249.670533, "Python_Time_μs": 2667.3365694781146, "Speedup": 2.13443183546532, "Performance_Ratio": "2.13x", "Category": "panel_ops"}, {"Operator": "Tot_Max", "C++_Time_μs": 18388.754467, "Python_Time_μs": 35553.15140013894, "Speedup": 1.9334181368260552, "Performance_Ratio": "1.93x", "Category": "panel_ops"}, {"Operator": "pn_Cut", "C++_Time_μs": 15858.453167, "Python_Time_μs": 26932.1136487027, "Speedup": 1.6982812488134706, "Performance_Ratio": "1.70x", "Category": "panel_ops"}, {"Operator": "To<PERSON>_<PERSON>", "C++_Time_μs": 21467.1023, "Python_Time_μs": 35490.39248997966, "Speedup": 1.6532456031562148, "Performance_Ratio": "1.65x", "Category": "panel_ops"}, {"Operator": "pn_Rank", "C++_Time_μs": 12588.4808, "Python_Time_μs": 20575.54613177975, "Speedup": 1.634474124294629, "Performance_Ratio": "1.63x", "Category": "panel_ops"}, {"Operator": "pn_Rank2", "C++_Time_μs": 11591.946867, "Python_Time_μs": 17437.779903411865, "Speedup": 1.5043012276957382, "Performance_Ratio": "1.50x", "Category": "panel_ops"}, {"Operator": "pn_TransStd", "C++_Time_μs": 3358.032267, "Python_Time_μs": 4875.2823534111185, "Speedup": 1.4518271314190199, "Performance_Ratio": "1.45x", "Category": "panel_ops"}, {"Operator": "pn_RankCentered", "C++_Time_μs": 14296.9175, "Python_Time_μs": 20346.979858974617, "Speedup": 1.423172502672315, "Performance_Ratio": "1.42x", "Category": "panel_ops"}, {"Operator": "pn_TransNorm", "C++_Time_μs": 27182.325233, "Python_Time_μs": 35471.60976255933, "Speedup": 1.3049512673586858, "Performance_Ratio": "1.30x", "Category": "panel_ops"}, {"Operator": "pn_FillMax", "C++_Time_μs": 1152.034767, "Python_Time_μs": 1137.4438802401226, "Speedup": 0.9873346819229479, "Performance_Ratio": "0.99x", "Category": "panel_ops"}, {"Operator": "pn_FillMin", "C++_Time_μs": 1151.0866, "Python_Time_μs": 1120.357618977626, "Speedup": 0.9733043708245983, "Performance_Ratio": "0.97x", "Category": "panel_ops"}, {"Operator": "pn_Stand", "C++_Time_μs": 9264.1452, "Python_Time_μs": 4902.830099066098, "Speedup": 0.5292263876721295, "Performance_Ratio": "0.53x", "Category": "panel_ops"}, {"Operator": "pn_GroupNeutral", "C++_Time_μs": 4460.5341, "Python_Time_μs": 84991.96087941527, "Speedup": 19.054211664790383, "Performance_Ratio": "19.05x", "Category": "group_ops"}, {"Operator": "pn_GroupRank", "C++_Time_μs": 13622.3466, "Python_Time_μs": 140539.6963780125, "Speedup": 10.316849255473539, "Performance_Ratio": "10.32x", "Category": "group_ops"}, {"Operator": "pn_GroupNorm", "C++_Time_μs": 29640.404533, "Python_Time_μs": 198364.59206417203, "Speedup": 6.692371281347519, "Performance_Ratio": "6.69x", "Category": "group_ops"}]}