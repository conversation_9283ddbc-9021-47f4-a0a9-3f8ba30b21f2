Feature Operators 性能比较汇总报告 (Python vs C++)
================================================================================

测试时间: 20250528_193151
总测试分类数: 8
总测试算子数: 91

整体性能统计:
--------------------------------------------------
C++总平均执行时间: 11155.315 μs
Python总平均执行时间: 46709.397 μs
总平均加速比: 8.50x
最大加速比: 56.60x (ts_Regression_A)
最小加速比: 0.32x (Round)

各分类性能汇总:
--------------------------------------------------------------------------------
分类              算子数      C++平均(μs)    Python平均(μs)    平均加速比     
--------------------------------------------------------------------------------
core_math       17       1321.829     2001.505        2.03      
logical_ops     4        481.720      11153.706       23.80     
comparison_ops  6        311.783      1733.588        5.61      
data_utils      3        133.534      608.633         4.74      
reduction_ops   2        299.036      11759.350       39.32     
timeseries_ops  33       21455.606    94432.669       12.15     
panel_ops       23       10057.326    25991.540       3.50      
group_ops       3        16170.934    140695.036      11.81     

前10名最快的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>      <GROUP>  2552.329     144466.173      56.60     
2    ts_Regression_B      timeseries_ops  2645.707     143077.975      54.08     
3    ts_Regression_D      timeseries_ops  3198.097     147210.395      46.03     
4    ts_Regression_C      timeseries_ops  3200.720     143813.566      44.93     
5    ts_Cov               timeseries_ops  2632.538     104523.911      39.70     
6    Min                  reduction_ops   301.673      11890.285       39.41     
7    Max                  reduction_ops   296.398      11628.416       39.23     
8    ts_Corr              timeseries_ops  5722.583     192979.945      33.72     
9    Xor                  logical_ops     501.848      15017.191       29.92     
10   Not                  logical_ops     333.859      9318.930        27.91     

后10名最慢的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>                <GROUP>       920.231      292.711         0.32      
2    Floor                core_math       446.003      301.637         0.68      
3    Ceil                 core_math       443.567      304.187         0.69      
4    pn_FillMax           panel_ops       965.669      1131.760        1.17      
5    pn_FillMin           panel_ops       966.981      1134.580        1.17      
6    Exp                  core_math       9001.112     11061.705       1.23      
7    pn_TransNorm         panel_ops       27627.120    35710.100       1.29      
8    pn_RankCentered      panel_ops       14869.373    19265.828       1.30      
9    Sqrt                 core_math       627.947      837.167         1.33      
10   Log                  core_math       4904.977     6828.512        1.39      

================================================================================
按分类详细性能列表:
================================================================================

COMPARISON_OPS (6 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
MEthan               292.232      1776.012        6.08      
LEthan               294.709      1783.082        6.05      
Lthan                303.445      1809.781        5.96      
Mthan                295.640      1753.454        5.93      
Equal                313.231      1645.045        5.25      
UnEqual              371.442      1634.155        4.40      

CORE_MATH (17 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Abs                  218.903      989.381         4.52      
Multiply             219.836      701.983         3.19      
Minus                219.099      693.967         3.17      
Add                  236.387      712.392         3.01      
Sign                 537.042      1488.392        2.77      
Power                163.584      366.899         2.24      
SignedPower          2628.861     5786.986        2.20      
Reverse              163.835      359.640         2.20      
Divide               362.563      758.289         2.09      
Softsign             1014.003     1976.084        1.95      
inv                  363.135      565.652         1.56      
Log                  4904.977     6828.512        1.39      
Sqrt                 627.947      837.167         1.33      
Exp                  9001.112     11061.705       1.23      
Ceil                 443.567      304.187         0.69      
Floor                446.003      301.637         0.68      
Round                920.231      292.711         0.32      

DATA_UTILS (3 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
getNan               103.595      561.113         5.42      
getInf               103.467      501.370         4.85      
FilterInf            193.541      763.416         3.94      

GROUP_OPS (3 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
pn_GroupNeutral      4506.146     84230.892       18.69     
pn_GroupRank         13812.662    141011.526      10.21     
pn_GroupNorm         30193.994    196842.688      6.52      

LOGICAL_OPS (4 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Xor                  501.848      15017.191       29.92     
Not                  333.859      9318.930        27.91     
Or                   498.658      9847.095        19.75     
And                  592.516      10431.609       17.61     

PANEL_OPS (23 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Tot_Sum              2922.189     31773.871       10.87     
pn_CrossFit          3033.126     27180.353       8.96      
Tot_Mean             4348.548     32373.736       7.44      
Tot_Stdev            6732.748     43578.153       6.47      
pn_Winsor            1850.334     9228.033        4.99      
Tot_Delta            279.185      1367.025        4.90      
Tot_ArgMin           11284.598    53713.569       4.76      
Tot_ArgMax           11302.650    53422.609       4.73      
Tot_Divide           736.660      2633.432        3.57      
Tot_ChgRate          738.443      2162.701        2.93      
Tot_Rank             55990.860    142987.564      2.55      
pn_TransStd          2127.756     5008.960        2.35      
Tot_Max              18405.927    32023.769       1.74      
pn_Mean              1243.014     2071.964        1.67      
pn_Rank              13088.394    20923.691       1.60      
pn_Cut               16366.961    26061.368       1.59      
pn_Stand             3130.335     4906.351        1.57      
Tot_Min              21456.510    31660.448       1.48      
pn_Rank2             11851.105    17485.564       1.48      
pn_RankCentered      14869.373    19265.828       1.30      
pn_TransNorm         27627.120    35710.100       1.29      
pn_FillMin           966.981      1134.580        1.17      
pn_FillMax           965.669      1131.760        1.17      

REDUCTION_OPS (2 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Min                  301.673      11890.285       39.41     
Max                  296.398      11628.416       39.23     

TIMESERIES_OPS (33 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
ts_Regression_A      2552.329     144466.173      56.60     
ts_Regression_B      2645.707     143077.975      54.08     
ts_Regression_D      3198.097     147210.395      46.03     
ts_Regression_C      3200.720     143813.566      44.93     
ts_Cov               2632.538     104523.911      39.70     
ts_Corr              5722.583     192979.945      33.72     
ts_Partial_corr      23382.500    596784.166      25.52     
ts_Sum               2910.715     31372.292       10.78     
ts_Mean              4451.191     32264.138       7.25      
ts_Delta             287.105      1998.403        6.96      
ts_Scale             9842.549     66910.734       6.80      
ts_Stdev             6733.531     42734.349       6.35      
ts_Argmax            9228.669     48999.693       5.31      
ts_Argmin            9284.767     49286.845       5.31      
ts_Skewness          9722.784     45662.531       4.70      
ts_Product           7509.088     34258.505       4.56      
ts_ChgRate           763.038      3245.801        4.25      
ts_Kurtosis          11976.755    46825.284       3.91      
ts_Delay             273.789      897.433         3.28      
ts_Divide            751.264      2354.595        3.13      
ts_MeanChg           14365.256    44976.968       3.13      
ts_Rank              49760.838    128681.413      2.59      
ts_Max               12932.889    31480.668       2.43      
ts_Quantile_D        61729.099    139483.832      2.26      
ts_TransNorm         65540.440    145679.602      2.22      
ts_Quantile_C        61820.776    136813.710      2.21      
ts_Quantile_B        61866.883    132754.613      2.15      
ts_Median            61722.797    127022.851      2.06      
ts_Quantile_A        61782.001    126282.931      2.04      
ts_Min               15429.138    31393.979       2.03      
ts_Decay             7369.773     11641.083       1.58      
ts_Entropy           108932.408   168954.691      1.55      
ts_Decay2            7712.988     11444.998       1.48      
