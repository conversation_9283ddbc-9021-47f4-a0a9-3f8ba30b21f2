Feature Operators 性能比较汇总报告 (Python vs C++)
================================================================================

测试时间: 20250528_163434
总测试分类数: 8
总测试算子数: 91

整体性能统计:
--------------------------------------------------
C++总平均执行时间: 95867.098 μs
Python总平均执行时间: 259154.882 μs
总平均加速比: 6.74x
最大加速比: 63.03x (getNan)
最小加速比: 0.10x (Power)

各分类性能汇总:
--------------------------------------------------------------------------------
分类              算子数      C++平均(μs)    Python平均(μs)    平均加速比     
--------------------------------------------------------------------------------
core_math       17       18660.383    28421.125       2.76      
logical_ops     4        4823.692     82636.839       17.08     
comparison_ops  6        5037.682     43076.624       8.58      
data_utils      3        1931.278     33119.368       35.22     
reduction_ops   2        4851.579     112044.025      23.08     
timeseries_ops  33       179105.491   427551.881      5.90      
panel_ops       23       88510.834    186322.713      3.00      
group_ops       3        131810.394   1264282.461     10.42     

前10名最快的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>               <GROUP>      932.254      58763.456       63.03     
2    getInf               data_utils      878.922      36563.037       41.60     
3    Min                  reduction_ops   4905.350     119311.478      24.32     
4    ts_Regression_A      timeseries_ops  19135.804    458307.137      23.95     
5    Max                  reduction_ops   4797.809     104776.572      21.84     
6    Xor                  logical_ops     5154.774     110908.891      21.52     
7    ts_Regression_B      timeseries_ops  21760.048    462710.719      21.26     
8    ts_Regression_C      timeseries_ops  24015.139    482605.084      20.10     
9    ts_Regression_D      timeseries_ops  24379.142    489677.591      20.09     
10   Not                  logical_ops     3662.402     59453.054       16.23     

后10名最慢的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>                <GROUP>       123142.808   11734.775       0.10      
2    Tot_Max              panel_ops       173257.727   92862.403       0.54      
3    Tot_Min              panel_ops       151795.274   92846.783       0.61      
4    pn_Stand             panel_ops       96388.172    59003.821       0.61      
5    Add                  core_math       4851.511     3167.302        0.65      
6    Divide               core_math       4767.222     3311.245        0.69      
7    Minus                core_math       4845.430     3497.724        0.72      
8    Multiply             core_math       4907.851     3557.920        0.72      
9    Reverse              core_math       3036.728     2492.770        0.82      
10   ts_Max               timeseries_ops  124684.122   113933.291      0.91      

================================================================================
按分类详细性能列表:
================================================================================

COMPARISON_OPS (6 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Equal                4673.850     43032.691       9.21      
Mthan                4655.528     41332.085       8.88      
UnEqual              5080.627     44280.882       8.72      
Lthan                4936.753     42888.079       8.69      
MEthan               5437.479     44019.430       8.10      
LEthan               5441.855     42906.577       7.88      

CORE_MATH (17 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Sqrt                 5050.161     54063.502       10.71     
Softsign             8438.206     89196.643       10.57     
Sign                 5560.365     31861.988       5.73      
Abs                  5074.690     26547.339       5.23      
SignedPower          22354.702    86249.182       3.86      
Log                  40203.081    62364.064       1.55      
Round                2352.565     3175.588        1.35      
Exp                  73858.182    93167.778       1.26      
Floor                2707.320     3012.286        1.11      
Ceil                 2788.665     2741.171        0.98      
inv                  3287.024     3017.849        0.92      
Reverse              3036.728     2492.770        0.82      
Multiply             4907.851     3557.920        0.72      
Minus                4845.430     3497.724        0.72      
Divide               4767.222     3311.245        0.69      
Add                  4851.511     3167.302        0.65      
Power                123142.808   11734.775       0.10      

DATA_UTILS (3 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
getNan               932.254      58763.456       63.03     
getInf               878.922      36563.037       41.60     
FilterInf            3982.658     4031.612        1.01      

GROUP_OPS (3 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
pn_GroupNeutral      55986.354    665010.602      11.88     
pn_GroupRank         117820.572   1327078.083     11.26     
pn_GroupNorm         221624.255   1800758.697     8.13      

LOGICAL_OPS (4 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Xor                  5154.774     110908.891      21.52     
Not                  3662.402     59453.054       16.23     
And                  5253.717     82011.689       15.61     
Or                   5223.876     78173.723       14.96     

PANEL_OPS (23 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Tot_Delta            3695.316     30327.152       8.21      
Tot_ChgRate          6555.897     47027.640       7.17      
pn_Winsor            26383.530    140054.924      5.31      
Tot_ArgMax           91467.129    482234.587      5.27      
Tot_ArgMin           91424.200    473866.162      5.18      
Tot_Divide           6578.810     32774.572       4.98      
pn_CrossFit          59696.991    293126.855      4.91      
Tot_Sum              26064.738    85714.709       3.29      
pn_TransStd          35792.590    89757.551       2.51      
Tot_Mean             36221.507    89390.117       2.47      
pn_Cut               130562.743   269392.724      2.06      
Tot_Stdev            55593.673    114693.681      2.06      
pn_Rank2             91912.081    188340.108      2.05      
pn_TransNorm         217900.128   430148.902      1.97      
pn_Rank              103112.513   202826.954      1.97      
pn_RankCentered      116941.908   228537.574      1.95      
Tot_Rank             464015.398   772933.182      1.67      
pn_Mean              18242.275    29326.536       1.61      
pn_FillMin           15789.104    20059.326       1.27      
pn_FillMax           16357.482    20176.138       1.23      
pn_Stand             96388.172    59003.821       0.61      
Tot_Min              151795.274   92846.783       0.61      
Tot_Max              173257.727   92862.403       0.54      

REDUCTION_OPS (2 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Min                  4905.350     119311.478      24.32     
Max                  4797.809     104776.572      21.84     

TIMESERIES_OPS (33 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
ts_Regression_A      19135.804    458307.137      23.95     
ts_Regression_B      21760.048    462710.719      21.26     
ts_Regression_C      24015.139    482605.084      20.10     
ts_Regression_D      24379.142    489677.591      20.09     
ts_Cov               23476.020    332462.005      14.16     
ts_Corr              42239.692    586963.598      13.90     
ts_Partial_corr      171948.056   1748477.400     10.17     
ts_Delta             4062.240     28311.849       6.97      
ts_Argmin            77839.902    528959.091      6.80      
ts_ChgRate           6930.500     44480.836       6.42      
ts_Argmax            78909.108    501490.556      6.36      
ts_Product           77061.306    326439.357      4.24      
ts_Divide            6819.651     28700.186       4.21      
ts_Delay             3810.754     14914.114       3.91      
ts_Sum               26159.947    91693.785       3.51      
ts_Scale             79377.076    248230.419      3.13      
ts_Mean              36499.704    94797.898       2.60      
ts_Stdev             55584.313    121151.997      2.18      
ts_Skewness          72598.075    126580.166      1.74      
ts_Rank              423326.936   734624.160      1.74      
ts_TransNorm         543130.441   916749.925      1.69      
ts_MeanChg           129826.430   218551.349      1.68      
ts_Quantile_D        511902.716   776442.810      1.52      
ts_Quantile_C        512273.007   759077.600      1.48      
ts_Median            506371.915   736877.731      1.46      
ts_Entropy           929375.633   1332834.623     1.43      
ts_Quantile_B        511864.909   725879.340      1.42      
ts_Quantile_A        514831.658   688271.824      1.34      
ts_Kurtosis          97887.416    127901.413      1.31      
ts_Min               107091.721   113982.539      1.06      
ts_Decay2            71322.644    73487.915       1.03      
ts_Decay             73985.196    73643.778       1.00      
ts_Max               124684.122   113933.291      0.91      
