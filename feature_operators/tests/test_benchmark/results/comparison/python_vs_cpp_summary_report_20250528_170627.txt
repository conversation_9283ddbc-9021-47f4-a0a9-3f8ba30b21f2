Feature Operators 性能比较汇总报告 (Python vs C++)
================================================================================

测试时间: 20250528_170627
总测试分类数: 8
总测试算子数: 91

整体性能统计:
--------------------------------------------------
C++总平均执行时间: 12139.544 μs
Python总平均执行时间: 47717.590 μs
总平均加速比: 8.83x
最大加速比: 66.14x (ts_Regression_A)
最小加速比: 0.02x (Power)

各分类性能汇总:
--------------------------------------------------------------------------------
分类              算子数      C++平均(μs)    Python平均(μs)    平均加速比     
--------------------------------------------------------------------------------
core_math       17       2145.682     1992.362        2.12      
logical_ops     4        467.635      10862.134       23.31     
comparison_ops  6        297.900      1707.297        5.76      
data_utils      3        124.227      600.694         4.88      
reduction_ops   2        302.138      11159.234       36.93     
timeseries_ops  33       23624.425    96679.234       13.22     
panel_ops       23       10391.860    26836.271       3.42      
group_ops       3        14989.405    140989.573      12.01     

前10名最快的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>      <GROUP>  2211.073     146250.817      66.14     
2    ts_Regression_B      timeseries_ops  2542.237     147642.818      58.08     
3    ts_Regression_D      timeseries_ops  2861.257     151819.723      53.06     
4    ts_Regression_C      timeseries_ops  2827.522     147921.943      52.32     
5    ts_Corr              timeseries_ops  5030.425     200057.123      39.77     
6    ts_Cov               timeseries_ops  2760.657     109277.247      39.58     
7    Min                  reduction_ops   302.640      11238.071       37.13     
8    Max                  reduction_ops   301.637      11080.397       36.73     
9    Xor                  logical_ops     502.075      14796.824       29.47     
10   ts_Partial_corr      timeseries_ops  21133.201    619935.898      29.33     

后10名最慢的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>                <GROUP>       15274.682    370.693         0.02      
2    pn_Stand             panel_ops       9791.536     4919.526        0.50      
3    pn_FillMin           panel_ops       1104.733     1118.095        1.01      
4    pn_FillMax           panel_ops       1105.210     1132.502        1.02      
5    ts_Decay2            timeseries_ops  9335.002     9636.636        1.03      
6    ts_Decay             timeseries_ops  8671.805     9692.637        1.12      
7    Exp                  core_math       9152.875     10975.097       1.20      
8    ts_Quantile_B        timeseries_ops  109300.618   134475.421      1.23      
9    Sqrt                 core_math       628.651      827.411         1.32      
10   pn_TransNorm         panel_ops       26954.787    35811.987       1.33      

================================================================================
按分类详细性能列表:
================================================================================

COMPARISON_OPS (6 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
LEthan               278.665      1763.844        6.33      
MEthan               275.117      1721.484        6.26      
Lthan                288.027      1729.417        6.00      
Mthan                297.582      1734.297        5.83      
Equal                326.181      1671.538        5.12      
UnEqual              321.829      1623.200        5.04      

CORE_MATH (17 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Abs                  222.651      965.259         4.34      
Minus                222.336      724.282         3.26      
Multiply             220.987      717.711         3.25      
Add                  241.164      737.064         3.06      
Sign                 546.491      1492.805        2.73      
Reverse              164.709      366.332         2.22      
SignedPower          2641.146     5774.403        2.19      
Divide               382.322      772.910         2.02      
Softsign             1044.241     1999.118        1.91      
Floor                161.560      308.659         1.91      
Ceil                 162.988      309.107         1.90      
Round                167.069      292.890         1.75      
inv                  370.160      566.160         1.53      
Log                  4872.559     6670.255        1.37      
Sqrt                 628.651      827.411         1.32      
Exp                  9152.875     10975.097       1.20      
Power                15274.682    370.693         0.02      

DATA_UTILS (3 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
getNan               104.912      557.223         5.31      
getInf               103.791      487.031         4.69      
FilterInf            163.977      757.828         4.62      

GROUP_OPS (3 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
pn_GroupNeutral      4594.432     83596.408       18.20     
pn_GroupRank         13423.069    140590.545      10.47     
pn_GroupNorm         26950.714    198781.767      7.38      

LOGICAL_OPS (4 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Xor                  502.075      14796.824       29.47     
Not                  366.113      8986.612        24.55     
And                  505.907      10183.159       20.13     
Or                   496.442      9481.941        19.10     

PANEL_OPS (23 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Tot_Sum              2994.263     33672.072       11.25     
Tot_Mean             4164.466     34157.547       8.20      
pn_CrossFit          3862.427     27690.845       7.17      
Tot_Stdev            6530.436     45846.630       7.02      
Tot_Delta            272.588      1426.523        5.23      
Tot_ArgMin           10802.187    54740.542       5.07      
Tot_ArgMax           10887.748    54424.812       5.00      
Tot_Divide           737.030      2657.996        3.61      
pn_Winsor            2814.900     9312.585        3.31      
Tot_ChgRate          738.800      2183.179        2.96      
Tot_Rank             58711.150    149202.824      2.54      
pn_Mean              1235.866     2671.653        2.16      
Tot_Min              19093.851    33506.430       1.75      
pn_Cut               15574.251    26670.620       1.71      
Tot_Max              20984.105    33915.402       1.62      
pn_Rank              12463.347    19898.960       1.60      
pn_TransStd          3124.640     4889.910        1.56      
pn_Rank2             10977.911    17112.066       1.56      
pn_RankCentered      14086.538    20271.535       1.44      
pn_TransNorm         26954.787    35811.987       1.33      
pn_FillMax           1105.210     1132.502        1.02      
pn_FillMin           1104.733     1118.095        1.01      
pn_Stand             9791.536     4919.526        0.50      

REDUCTION_OPS (2 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Min                  302.640      11238.071       37.13     
Max                  301.637      11080.397       36.73     

TIMESERIES_OPS (33 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
ts_Regression_A      2211.073     146250.817      66.14     
ts_Regression_B      2542.237     147642.818      58.08     
ts_Regression_D      2861.257     151819.723      53.06     
ts_Regression_C      2827.522     147921.943      52.32     
ts_Corr              5030.425     200057.123      39.77     
ts_Cov               2760.657     109277.247      39.58     
ts_Partial_corr      21133.201    619935.898      29.33     
ts_Sum               3013.815     32363.781       10.74     
ts_Mean              4168.374     35093.411       8.42      
ts_Scale             9221.430     70646.473       7.66      
ts_Stdev             6560.484     44771.254       6.82      
ts_Skewness          8646.179     47718.801       5.52      
ts_Argmin            9169.357     47468.257       5.18      
ts_Argmax            9235.766     47429.176       5.14      
ts_Delta             277.262      1390.299        5.01      
ts_Kurtosis          11703.293    49465.985       4.23      
ts_Product           8872.716     35244.148       3.97      
ts_Divide            746.559      2846.232        3.81      
ts_Delay             274.258      900.325         3.28      
ts_MeanChg           15471.459    46487.628       3.00      
ts_ChgRate           775.578      2164.874        2.79      
ts_Min               13479.396    33017.990       2.45      
ts_Rank              53587.812    129742.191      2.42      
ts_Max               15112.481    33422.228       2.21      
ts_TransNorm         67337.912    147961.394      2.20      
ts_Quantile_D        67169.720    141236.804      2.10      
ts_Quantile_C        66444.848    138535.170      2.08      
ts_Median            63778.180    129649.608      2.03      
ts_Quantile_A        65670.335    128680.331      1.96      
ts_Entropy           112215.022   167468.107      1.49      
ts_Quantile_B        109300.618   134475.421      1.23      
ts_Decay             8671.805     9692.637        1.12      
ts_Decay2            9335.002     9636.636        1.03      
