{"timestamp": "20250528_193151", "summary": {"total_categories": 8, "total_operators": 91, "overall_cpp_avg_time": 11155.314660472526, "overall_python_avg_time": 46709.39721446826, "overall_avg_speedup": 8.496464028198421, "max_speedup": 56.60170484193521, "min_speedup": 0.3180847012676058}, "category_summaries": {"core_math": {"total_operators": 17, "cpp_avg_time": 1321.8285, "python_avg_time": 2001.5050419697573, "avg_speedup": 2.031598833132018, "max_speedup": 4.519729713943649, "min_speedup": 0.3180847012676058}, "logical_ops": {"total_operators": 4, "cpp_avg_time": 481.72015, "python_avg_time": 11153.706279583275, "avg_speedup": 23.797351430506023, "max_speedup": 29.92380104725019, "min_speedup": 17.605614285904682}, "comparison_ops": {"total_operators": 6, "cpp_avg_time": 311.78328333333326, "python_avg_time": 1733.5881841265493, "avg_speedup": 5.612368405126112, "max_speedup": 6.077395824431274, "min_speedup": 4.399491990050794}, "data_utils": {"total_operators": 3, "cpp_avg_time": 133.53435533333334, "python_avg_time": 608.6328273846043, "avg_speedup": 4.73552452059172, "max_speedup": 5.416422421091183, "min_speedup": 3.944462319730689}, "reduction_ops": {"total_operators": 2, "cpp_avg_time": 299.03558350000003, "python_avg_time": 11759.350340192515, "avg_speedup": 39.32344827926298, "max_speedup": 39.414454220058204, "min_speedup": 39.23244233846776}, "timeseries_ops": {"total_operators": 33, "cpp_avg_time": 21455.606221242422, "python_avg_time": 94432.66878675931, "avg_speedup": 12.149103042410058, "max_speedup": 56.60170484193521, "min_speedup": 1.483860430528782}, "panel_ops": {"total_operators": 23, "cpp_avg_time": 10057.325543565217, "python_avg_time": 25991.540458863197, "avg_speedup": 3.5036082247703733, "max_speedup": 10.873311144570255, "min_speedup": 1.1719947897555745}, "group_ops": {"total_operators": 3, "cpp_avg_time": 16170.934089000002, "python_avg_time": 140695.0356335276, "avg_speedup": 11.806856853493272, "max_speedup": 18.692444857370244, "min_speedup": 6.519266419172952}}, "detailed_results": [{"Operator": "Abs", "C++_Time_μs": 218.9027, "Python_Time_μs": 989.3810376524925, "Speedup": 4.519729713943649, "Performance_Ratio": "4.52x", "Category": "core_math"}, {"Operator": "Multiply", "C++_Time_μs": 219.8359, "Python_Time_μs": 701.982652147611, "Speedup": 3.193212082956473, "Performance_Ratio": "3.19x", "Category": "core_math"}, {"Operator": "Minus", "C++_Time_μs": 219.098933, "Python_Time_μs": 693.9669450124105, "Speedup": 3.1673679808034962, "Performance_Ratio": "3.17x", "Category": "core_math"}, {"Operator": "Add", "C++_Time_μs": 236.386667, "Python_Time_μs": 712.3924170931181, "Speedup": 3.0136742741633484, "Performance_Ratio": "3.01x", "Category": "core_math"}, {"Operator": "Sign", "C++_Time_μs": 537.0421, "Python_Time_μs": 1488.3922102550666, "Speedup": 2.7714628150289644, "Performance_Ratio": "2.77x", "Category": "core_math"}, {"Operator": "Power", "C++_Time_μs": 163.584167, "Python_Time_μs": 366.89921592672664, "Speedup": 2.2428773068650747, "Performance_Ratio": "2.24x", "Category": "core_math"}, {"Operator": "SignedPower", "C++_Time_μs": 2628.8614, "Python_Time_μs": 5786.986214419206, "Speedup": 2.201328002464948, "Performance_Ratio": "2.20x", "Category": "core_math"}, {"Operator": "Reverse", "C++_Time_μs": 163.835133, "Python_Time_μs": 359.6397116780281, "Speedup": 2.195131807766946, "Performance_Ratio": "2.20x", "Category": "core_math"}, {"Operator": "Divide", "C++_Time_μs": 362.563333, "Python_Time_μs": 758.2893905540308, "Speedup": 2.0914673976533384, "Performance_Ratio": "2.09x", "Category": "core_math"}, {"Operator": "Softsign", "C++_Time_μs": 1014.0026, "Python_Time_μs": 1976.0835605363052, "Speedup": 1.9487953586473103, "Performance_Ratio": "1.95x", "Category": "core_math"}, {"Operator": "inv", "C++_Time_μs": 363.1347, "Python_Time_μs": 565.6522698700428, "Speedup": 1.5576926960437623, "Performance_Ratio": "1.56x", "Category": "core_math"}, {"Operator": "Log", "C++_Time_μs": 4904.976733, "Python_Time_μs": 6828.5117236276465, "Speedup": 1.3921598603488514, "Performance_Ratio": "1.39x", "Category": "core_math"}, {"Operator": "Sqrt", "C++_Time_μs": 627.9466, "Python_Time_μs": 837.1667936444283, "Speedup": 1.3331815056318934, "Performance_Ratio": "1.33x", "Category": "core_math"}, {"Operator": "Exp", "C++_Time_μs": 9001.112167, "Python_Time_μs": 11061.705152193705, "Speedup": 1.2289264867455245, "Performance_Ratio": "1.23x", "Category": "core_math"}, {"Operator": "Ceil", "C++_Time_μs": 443.567133, "Python_Time_μs": 304.18746173381805, "Speedup": 0.6857754759161068, "Performance_Ratio": "0.69x", "Category": "core_math"}, {"Operator": "Floor", "C++_Time_μs": 446.002967, "Python_Time_μs": 301.6374694804351, "Speedup": 0.6763126969970024, "Performance_Ratio": "0.68x", "Category": "core_math"}, {"Operator": "Round", "C++_Time_μs": 920.231267, "Python_Time_μs": 292.7114876608054, "Speedup": 0.3180847012676058, "Performance_Ratio": "0.32x", "Category": "core_math"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 501.8477, "Python_Time_μs": 15017.190730820099, "Speedup": 29.92380104725019, "Performance_Ratio": "29.92x", "Category": "logical_ops"}, {"Operator": "Not", "C++_Time_μs": 333.858533, "Python_Time_μs": 9318.930096924305, "Speedup": 27.91281089384139, "Performance_Ratio": "27.91x", "Category": "logical_ops"}, {"Operator": "Or", "C++_Time_μs": 498.6583, "Python_Time_μs": 9847.09495678544, "Speedup": 19.74717949502784, "Performance_Ratio": "19.75x", "Category": "logical_ops"}, {"Operator": "And", "C++_Time_μs": 592.516067, "Python_Time_μs": 10431.609333803257, "Speedup": 17.605614285904682, "Performance_Ratio": "17.61x", "Category": "logical_ops"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 292.232467, "Python_Time_μs": 1776.01237470905, "Speedup": 6.077395824431274, "Performance_Ratio": "6.08x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON><PERSON>", "C++_Time_μs": 294.7087, "Python_Time_μs": 1783.0818270643551, "Speedup": 6.050319610735465, "Performance_Ratio": "6.05x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 303.4453, "Python_Time_μs": 1809.780765324831, "Speedup": 5.964108738295934, "Performance_Ratio": "5.96x", "Category": "comparison_ops"}, {"Operator": "Mthan", "C++_Time_μs": 295.6405, "Python_Time_μs": 1753.4539724389713, "Speedup": 5.931034389533814, "Performance_Ratio": "5.93x", "Category": "comparison_ops"}, {"Operator": "Equal", "C++_Time_μs": 313.2309, "Python_Time_μs": 1645.0447961688042, "Speedup": 5.251859877709396, "Performance_Ratio": "5.25x", "Category": "comparison_ops"}, {"Operator": "UnEqual", "C++_Time_μs": 371.441833, "Python_Time_μs": 1634.1553690532844, "Speedup": 4.399491990050794, "Performance_Ratio": "4.40x", "Category": "comparison_ops"}, {"Operator": "get<PERSON><PERSON>", "C++_Time_μs": 103.594833, "Python_Time_μs": 561.1133761703968, "Speedup": 5.416422421091183, "Performance_Ratio": "5.42x", "Category": "data_utils"}, {"Operator": "getInf", "C++_Time_μs": 103.467133, "Python_Time_μs": 501.36952971418697, "Speedup": 4.845688820953288, "Performance_Ratio": "4.85x", "Category": "data_utils"}, {"Operator": "FilterInf", "C++_Time_μs": 193.5411, "Python_Time_μs": 763.4155762692293, "Speedup": 3.944462319730689, "Performance_Ratio": "3.94x", "Category": "data_utils"}, {"Operator": "Min", "C++_Time_μs": 301.6732, "Python_Time_μs": 11890.284530818462, "Speedup": 39.414454220058204, "Performance_Ratio": "39.41x", "Category": "reduction_ops"}, {"Operator": "Max", "C++_Time_μs": 296.397967, "Python_Time_μs": 11628.41614956657, "Speedup": 39.23244233846776, "Performance_Ratio": "39.23x", "Category": "reduction_ops"}, {"Operator": "ts_Regression_A", "C++_Time_μs": 2552.329, "Python_Time_μs": 144466.17271751165, "Speedup": 56.60170484193521, "Performance_Ratio": "56.60x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_B", "C++_Time_μs": 2645.706867, "Python_Time_μs": 143077.9754805068, "Speedup": 54.07930004080335, "Performance_Ratio": "54.08x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_D", "C++_Time_μs": 3198.0966, "Python_Time_μs": 147210.394932578, "Speedup": 46.03062800935344, "Performance_Ratio": "46.03x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_C", "C++_Time_μs": 3200.7195, "Python_Time_μs": 143813.565839082, "Speedup": 44.93163672701778, "Performance_Ratio": "44.93x", "Category": "timeseries_ops"}, {"Operator": "ts_Cov", "C++_Time_μs": 2632.5379, "Python_Time_μs": 104523.91073728602, "Speedup": 39.70461763809213, "Performance_Ratio": "39.70x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>rr", "C++_Time_μs": 5722.583067, "Python_Time_μs": 192979.94474569955, "Speedup": 33.722523987208305, "Performance_Ratio": "33.72x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>_corr", "C++_Time_μs": 23382.499867, "Python_Time_μs": 596784.1657499472, "Speedup": 25.522684449672372, "Performance_Ratio": "25.52x", "Category": "timeseries_ops"}, {"Operator": "ts_Sum", "C++_Time_μs": 2910.715333, "Python_Time_μs": 31372.291905184586, "Speedup": 10.778206837853142, "Performance_Ratio": "10.78x", "Category": "timeseries_ops"}, {"Operator": "ts_Mean", "C++_Time_μs": 4451.191, "Python_Time_μs": 32264.137764771778, "Speedup": 7.248428064482468, "Performance_Ratio": "7.25x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 287.1046, "Python_Time_μs": 1998.4027370810509, "Speedup": 6.960538901435403, "Performance_Ratio": "6.96x", "Category": "timeseries_ops"}, {"Operator": "ts_Scale", "C++_Time_μs": 9842.5494, "Python_Time_μs": 66910.73377927144, "Speedup": 6.798110028207879, "Performance_Ratio": "6.80x", "Category": "timeseries_ops"}, {"Operator": "ts_Stdev", "C++_Time_μs": 6733.531467, "Python_Time_μs": 42734.348929176726, "Speedup": 6.3464987337790255, "Performance_Ratio": "6.35x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON><PERSON>", "C++_Time_μs": 9228.668933, "Python_Time_μs": 48999.69308947524, "Speedup": 5.3095081690775, "Performance_Ratio": "5.31x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON><PERSON>", "C++_Time_μs": 9284.767367, "Python_Time_μs": 49286.84510911504, "Speedup": 5.308355412790499, "Performance_Ratio": "5.31x", "Category": "timeseries_ops"}, {"Operator": "ts_Skewness", "C++_Time_μs": 9722.783967, "Python_Time_μs": 45662.53079101443, "Speedup": 4.69644609465737, "Performance_Ratio": "4.70x", "Category": "timeseries_ops"}, {"Operator": "ts_Product", "C++_Time_μs": 7509.087767, "Python_Time_μs": 34258.50455338756, "Speedup": 4.562272491199604, "Performance_Ratio": "4.56x", "Category": "timeseries_ops"}, {"Operator": "ts_ChgRate", "C++_Time_μs": 763.037833, "Python_Time_μs": 3245.800950874885, "Speedup": 4.253787702915755, "Performance_Ratio": "4.25x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 11976.754633, "Python_Time_μs": 46825.284200410046, "Speedup": 3.9096805132327406, "Performance_Ratio": "3.91x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 273.789133, "Python_Time_μs": 897.4334535499414, "Speedup": 3.277827150100736, "Performance_Ratio": "3.28x", "Category": "timeseries_ops"}, {"Operator": "ts_Divide", "C++_Time_μs": 751.264033, "Python_Time_μs": 2354.594754676024, "Speedup": 3.1341774013504833, "Performance_Ratio": "3.13x", "Category": "timeseries_ops"}, {"Operator": "ts_MeanChg", "C++_Time_μs": 14365.2562, "Python_Time_μs": 44976.96831822395, "Speedup": 3.1309548324118266, "Performance_Ratio": "3.13x", "Category": "timeseries_ops"}, {"Operator": "ts_Rank", "C++_Time_μs": 49760.837767, "Python_Time_μs": 128681.41333262126, "Speedup": 2.585997726468327, "Performance_Ratio": "2.59x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 12932.8886, "Python_Time_μs": 31480.667584886152, "Speedup": 2.434155938286374, "Performance_Ratio": "2.43x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_D", "C++_Time_μs": 61729.0995, "Python_Time_μs": 139483.8317297399, "Speedup": 2.259612287552322, "Performance_Ratio": "2.26x", "Category": "timeseries_ops"}, {"Operator": "ts_TransNorm", "C++_Time_μs": 65540.4405, "Python_Time_μs": 145679.6020269394, "Speedup": 2.2227437123639624, "Performance_Ratio": "2.22x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_C", "C++_Time_μs": 61820.7757, "Python_Time_μs": 136813.7096365293, "Speedup": 2.2130700899071587, "Performance_Ratio": "2.21x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_B", "C++_Time_μs": 61866.8826, "Python_Time_μs": 132754.61342806616, "Speedup": 2.145810615453027, "Performance_Ratio": "2.15x", "Category": "timeseries_ops"}, {"Operator": "ts_Median", "C++_Time_μs": 61722.797367, "Python_Time_μs": 127022.85079285502, "Speedup": 2.0579568038302747, "Performance_Ratio": "2.06x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_A", "C++_Time_μs": 61782.001067, "Python_Time_μs": 126282.93108815949, "Speedup": 2.0440084313748743, "Performance_Ratio": "2.04x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 15429.138133, "Python_Time_μs": 31393.978993097942, "Speedup": 2.034720197750526, "Performance_Ratio": "2.03x", "Category": "timeseries_ops"}, {"Operator": "ts_Decay", "C++_Time_μs": 7369.7733, "Python_Time_μs": 11641.08250911037, "Speedup": 1.5795713158653566, "Performance_Ratio": "1.58x", "Category": "timeseries_ops"}, {"Operator": "ts_Entropy", "C++_Time_μs": 108932.4083, "Python_Time_μs": 168954.6906078855, "Speedup": 1.5510048225738668, "Performance_Ratio": "1.55x", "Category": "timeseries_ops"}, {"Operator": "ts_Decay2", "C++_Time_μs": 7712.988, "Python_Time_μs": 11444.997694343328, "Speedup": 1.483860430528782, "Performance_Ratio": "1.48x", "Category": "timeseries_ops"}, {"Operator": "Tot_Sum", "C++_Time_μs": 2922.1891, "Python_Time_μs": 31773.871307571728, "Speedup": 10.873311144570255, "Performance_Ratio": "10.87x", "Category": "panel_ops"}, {"Operator": "pn_CrossFit", "C++_Time_μs": 3033.126367, "Python_Time_μs": 27180.35311748584, "Speedup": 8.961167399157636, "Performance_Ratio": "8.96x", "Category": "panel_ops"}, {"Operator": "Tot_Mean", "C++_Time_μs": 4348.5485, "Python_Time_μs": 32373.73574326436, "Speedup": 7.444722243126496, "Performance_Ratio": "7.44x", "Category": "panel_ops"}, {"Operator": "Tot_Stdev", "C++_Time_μs": 6732.7484, "Python_Time_μs": 43578.152575840555, "Speedup": 6.472565137862652, "Performance_Ratio": "6.47x", "Category": "panel_ops"}, {"Operator": "pn_Winsor", "C++_Time_μs": 1850.333767, "Python_Time_μs": 9228.03326199452, "Speedup": 4.987226319150085, "Performance_Ratio": "4.99x", "Category": "panel_ops"}, {"Operator": "Tot_Delta", "C++_Time_μs": 279.185, "Python_Time_μs": 1367.024580637614, "Speedup": 4.89648290788407, "Performance_Ratio": "4.90x", "Category": "panel_ops"}, {"Operator": "Tot_Arg<PERSON>in", "C++_Time_μs": 11284.5981, "Python_Time_μs": 53713.56892089049, "Speedup": 4.759900923799004, "Performance_Ratio": "4.76x", "Category": "panel_ops"}, {"Operator": "Tot_ArgMax", "C++_Time_μs": 11302.650167, "Python_Time_μs": 53422.60875428716, "Speedup": 4.7265559815576275, "Performance_Ratio": "4.73x", "Category": "panel_ops"}, {"Operator": "Tot_Divide", "C++_Time_μs": 736.6605, "Python_Time_μs": 2633.4316159288087, "Speedup": 3.5748239737692042, "Performance_Ratio": "3.57x", "Category": "panel_ops"}, {"Operator": "Tot_ChgRate", "C++_Time_μs": 738.443, "Python_Time_μs": 2162.7013571560383, "Speedup": 2.9287316111819575, "Performance_Ratio": "2.93x", "Category": "panel_ops"}, {"Operator": "Tot_Rank", "C++_Time_μs": 55990.859567, "Python_Time_μs": 142987.56383359432, "Speedup": 2.553766185041185, "Performance_Ratio": "2.55x", "Category": "panel_ops"}, {"Operator": "pn_TransStd", "C++_Time_μs": 2127.755633, "Python_Time_μs": 5008.960422128439, "Speedup": 2.3541051164160818, "Performance_Ratio": "2.35x", "Category": "panel_ops"}, {"Operator": "Tot_Max", "C++_Time_μs": 18405.9267, "Python_Time_μs": 32023.769182463486, "Speedup": 1.7398618230107092, "Performance_Ratio": "1.74x", "Category": "panel_ops"}, {"Operator": "pn_Mean", "C++_Time_μs": 1243.014167, "Python_Time_μs": 2071.9644613564014, "Speedup": 1.6668872458284711, "Performance_Ratio": "1.67x", "Category": "panel_ops"}, {"Operator": "pn_Rank", "C++_Time_μs": 13088.3939, "Python_Time_μs": 20923.69142919779, "Speedup": 1.5986446915536205, "Performance_Ratio": "1.60x", "Category": "panel_ops"}, {"Operator": "pn_Cut", "C++_Time_μs": 16366.960833, "Python_Time_μs": 26061.36842320363, "Speedup": 1.5923156833525998, "Performance_Ratio": "1.59x", "Category": "panel_ops"}, {"Operator": "pn_Stand", "C++_Time_μs": 3130.3348, "Python_Time_μs": 4906.350808839004, "Speedup": 1.567356568006401, "Performance_Ratio": "1.57x", "Category": "panel_ops"}, {"Operator": "To<PERSON>_<PERSON>", "C++_Time_μs": 21456.5099, "Python_Time_μs": 31660.44841830929, "Speedup": 1.4755637597104871, "Performance_Ratio": "1.48x", "Category": "panel_ops"}, {"Operator": "pn_Rank2", "C++_Time_μs": 11851.1053, "Python_Time_μs": 17485.56423311432, "Speedup": 1.4754374204332081, "Performance_Ratio": "1.48x", "Category": "panel_ops"}, {"Operator": "pn_RankCentered", "C++_Time_μs": 14869.3735, "Python_Time_μs": 19265.82818850875, "Speedup": 1.2956718175455577, "Performance_Ratio": "1.30x", "Category": "panel_ops"}, {"Operator": "pn_TransNorm", "C++_Time_μs": 27627.120267, "Python_Time_μs": 35710.10045707226, "Speedup": 1.2925741123922787, "Performance_Ratio": "1.29x", "Category": "panel_ops"}, {"Operator": "pn_FillMin", "C++_Time_μs": 966.980567, "Python_Time_μs": 1134.579877058665, "Speedup": 1.1733223146134488, "Performance_Ratio": "1.17x", "Category": "panel_ops"}, {"Operator": "pn_FillMax", "C++_Time_μs": 965.669467, "Python_Time_μs": 1131.7595839500427, "Speedup": 1.1719947897555745, "Performance_Ratio": "1.17x", "Category": "panel_ops"}, {"Operator": "pn_GroupNeutral", "C++_Time_μs": 4506.146367, "Python_Time_μs": 84230.89248438676, "Speedup": 18.692444857370244, "Performance_Ratio": "18.69x", "Category": "group_ops"}, {"Operator": "pn_GroupRank", "C++_Time_μs": 13812.662333, "Python_Time_μs": 141011.5260941287, "Speedup": 10.20885928393662, "Performance_Ratio": "10.21x", "Category": "group_ops"}, {"Operator": "pn_GroupNorm", "C++_Time_μs": 30193.993567, "Python_Time_μs": 196842.68832206726, "Speedup": 6.519266419172952, "Performance_Ratio": "6.52x", "Category": "group_ops"}]}