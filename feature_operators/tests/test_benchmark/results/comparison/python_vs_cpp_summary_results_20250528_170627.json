{"timestamp": "20250528_170627", "summary": {"total_categories": 8, "total_operators": 91, "overall_cpp_avg_time": 12139.543980582417, "overall_python_avg_time": 47717.59023978597, "overall_avg_speedup": 8.825928043692198, "max_speedup": 66.14473663625475, "min_speedup": 0.02426847737516475}, "category_summaries": {"core_math": {"total_operators": 17, "cpp_avg_time": 2145.681790235294, "python_avg_time": 1992.362103991064, "avg_speedup": 2.1160643417361578, "max_speedup": 4.335296036538922, "min_speedup": 0.02426847737516475}, "logical_ops": {"total_operators": 4, "cpp_avg_time": 467.63450824999995, "python_avg_time": 10862.134055544933, "avg_speedup": 23.31140290442883, "max_speedup": 29.471324955972246, "min_speedup": 19.09978589377334}, "comparison_ops": {"total_operators": 6, "cpp_avg_time": 297.9000221666667, "python_avg_time": 1707.2966581003536, "avg_speedup": 5.764580868194215, "max_speedup": 6.329626107588387, "min_speedup": 5.043673262516097}, "data_utils": {"total_operators": 3, "cpp_avg_time": 124.22655566666667, "python_avg_time": 600.693995753924, "avg_speedup": 4.875104754946091, "max_speedup": 5.311330811671849, "min_speedup": 4.62156419373731}, "reduction_ops": {"total_operators": 2, "cpp_avg_time": 302.13836649999996, "python_avg_time": 11159.233904133242, "avg_speedup": 36.93385238457334, "max_speedup": 37.133512036458036, "min_speedup": 36.73419273268864}, "timeseries_ops": {"total_operators": 33, "cpp_avg_time": 23624.42522621212, "python_avg_time": 96679.23420931053, "avg_speedup": 13.218031955357445, "max_speedup": 66.14473663625475, "min_speedup": 1.032312141222825}, "panel_ops": {"total_operators": 23, "cpp_avg_time": 10391.859520347827, "python_avg_time": 26836.271341080257, "avg_speedup": 3.4182818085931372, "max_speedup": 11.245530314460014, "min_speedup": 0.5024263334808943}, "group_ops": {"total_operators": 3, "cpp_avg_time": 14989.405266666667, "python_avg_time": 140989.57344475717, "avg_speedup": 12.01490206039063, "max_speedup": 18.19515457548765, "min_speedup": 7.3757513582569265}}, "detailed_results": [{"Operator": "Abs", "C++_Time_μs": 222.651333, "Python_Time_μs": 965.2594414850076, "Speedup": 4.335296036538922, "Performance_Ratio": "4.34x", "Category": "core_math"}, {"Operator": "Minus", "C++_Time_μs": 222.336033, "Python_Time_μs": 724.281712124745, "Speedup": 3.2575993299509176, "Performance_Ratio": "3.26x", "Category": "core_math"}, {"Operator": "Multiply", "C++_Time_μs": 220.9865, "Python_Time_μs": 717.7112313608328, "Speedup": 3.247760525465731, "Performance_Ratio": "3.25x", "Category": "core_math"}, {"Operator": "Add", "C++_Time_μs": 241.164267, "Python_Time_μs": 737.0640834172567, "Speedup": 3.056274018477442, "Performance_Ratio": "3.06x", "Category": "core_math"}, {"Operator": "Sign", "C++_Time_μs": 546.490767, "Python_Time_μs": 1492.805127054453, "Speedup": 2.7316200331239133, "Performance_Ratio": "2.73x", "Category": "core_math"}, {"Operator": "Reverse", "C++_Time_μs": 164.708667, "Python_Time_μs": 366.3317300379276, "Speedup": 2.2241193296642225, "Performance_Ratio": "2.22x", "Category": "core_math"}, {"Operator": "SignedPower", "C++_Time_μs": 2641.1455, "Python_Time_μs": 5774.403146157662, "Speedup": 2.1863252691522153, "Performance_Ratio": "2.19x", "Category": "core_math"}, {"Operator": "Divide", "C++_Time_μs": 382.3223, "Python_Time_μs": 772.9103788733482, "Speedup": 2.021619923486933, "Performance_Ratio": "2.02x", "Category": "core_math"}, {"Operator": "Softsign", "C++_Time_μs": 1044.241267, "Python_Time_μs": 1999.1178686420124, "Speedup": 1.9144214386252676, "Performance_Ratio": "1.91x", "Category": "core_math"}, {"Operator": "Floor", "C++_Time_μs": 161.560167, "Python_Time_μs": 308.6586482822895, "Speedup": 1.9104873064552446, "Performance_Ratio": "1.91x", "Category": "core_math"}, {"Operator": "Ceil", "C++_Time_μs": 162.9885, "Python_Time_μs": 309.1069559256236, "Speedup": 1.8964954946246122, "Performance_Ratio": "1.90x", "Category": "core_math"}, {"Operator": "Round", "C++_Time_μs": 167.068833, "Python_Time_μs": 292.88999115427333, "Speedup": 1.753109696733641, "Performance_Ratio": "1.75x", "Category": "core_math"}, {"Operator": "inv", "C++_Time_μs": 370.1603, "Python_Time_μs": 566.15953023235, "Speedup": 1.5294982477384798, "Performance_Ratio": "1.53x", "Category": "core_math"}, {"Operator": "Log", "C++_Time_μs": 4872.5586, "Python_Time_μs": 6670.254531006019, "Speedup": 1.3689429063010177, "Performance_Ratio": "1.37x", "Category": "core_math"}, {"Operator": "Sqrt", "C++_Time_μs": 628.6511, "Python_Time_μs": 827.4106308817863, "Speedup": 1.316168270256405, "Performance_Ratio": "1.32x", "Category": "core_math"}, {"Operator": "Exp", "C++_Time_μs": 9152.874533, "Python_Time_μs": 10975.097492337227, "Speedup": 1.199087505544552, "Performance_Ratio": "1.20x", "Category": "core_math"}, {"Operator": "Power", "C++_Time_μs": 15274.681767, "Python_Time_μs": 370.69326887528103, "Speedup": 0.02426847737516475, "Performance_Ratio": "0.02x", "Category": "core_math"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 502.0753, "Python_Time_μs": 14796.824318667253, "Speedup": 29.471324955972246, "Performance_Ratio": "29.47x", "Category": "logical_ops"}, {"Operator": "Not", "C++_Time_μs": 366.113233, "Python_Time_μs": 8986.611881603798, "Speedup": 24.545990342839637, "Performance_Ratio": "24.55x", "Category": "logical_ops"}, {"Operator": "And", "C++_Time_μs": 505.907233, "Python_Time_μs": 10183.159013589224, "Speedup": 20.128510425130102, "Performance_Ratio": "20.13x", "Category": "logical_ops"}, {"Operator": "Or", "C++_Time_μs": 496.442267, "Python_Time_μs": 9481.941008319458, "Speedup": 19.09978589377334, "Performance_Ratio": "19.10x", "Category": "logical_ops"}, {"Operator": "<PERSON><PERSON><PERSON>", "C++_Time_μs": 278.6648, "Python_Time_μs": 1763.8439933458965, "Speedup": 6.329626107588387, "Performance_Ratio": "6.33x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 275.116633, "Python_Time_μs": 1721.484089891116, "Speedup": 6.2572883039431355, "Performance_Ratio": "6.26x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 288.0266, "Python_Time_μs": 1729.4172818462055, "Speedup": 6.004366547555697, "Performance_Ratio": "6.00x", "Category": "comparison_ops"}, {"Operator": "Mthan", "C++_Time_μs": 297.5821, "Python_Time_μs": 1734.2966981232166, "Speedup": 5.827960412011396, "Performance_Ratio": "5.83x", "Category": "comparison_ops"}, {"Operator": "Equal", "C++_Time_μs": 326.1811, "Python_Time_μs": 1671.538067360719, "Speedup": 5.124570575550573, "Performance_Ratio": "5.12x", "Category": "comparison_ops"}, {"Operator": "UnEqual", "C++_Time_μs": 321.8289, "Python_Time_μs": 1623.1998180349667, "Speedup": 5.043673262516097, "Performance_Ratio": "5.04x", "Category": "comparison_ops"}, {"Operator": "get<PERSON><PERSON>", "C++_Time_μs": 104.9121, "Python_Time_μs": 557.2228692471981, "Speedup": 5.311330811671849, "Performance_Ratio": "5.31x", "Category": "data_utils"}, {"Operator": "getInf", "C++_Time_μs": 103.791, "Python_Time_μs": 487.03088735540706, "Speedup": 4.692419259429113, "Performance_Ratio": "4.69x", "Category": "data_utils"}, {"Operator": "FilterInf", "C++_Time_μs": 163.976567, "Python_Time_μs": 757.8282306591669, "Speedup": 4.62156419373731, "Performance_Ratio": "4.62x", "Category": "data_utils"}, {"Operator": "Min", "C++_Time_μs": 302.6396, "Python_Time_μs": 11238.071229308844, "Speedup": 37.133512036458036, "Performance_Ratio": "37.13x", "Category": "reduction_ops"}, {"Operator": "Max", "C++_Time_μs": 301.637133, "Python_Time_μs": 11080.396578957638, "Speedup": 36.73419273268864, "Performance_Ratio": "36.73x", "Category": "reduction_ops"}, {"Operator": "ts_Regression_A", "C++_Time_μs": 2211.072633, "Python_Time_μs": 146250.81699341536, "Speedup": 66.14473663625475, "Performance_Ratio": "66.14x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_B", "C++_Time_μs": 2542.237067, "Python_Time_μs": 147642.81756555042, "Speedup": 58.07594401090935, "Performance_Ratio": "58.08x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_D", "C++_Time_μs": 2861.257033, "Python_Time_μs": 151819.7225717207, "Speedup": 53.06049782341267, "Performance_Ratio": "53.06x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_C", "C++_Time_μs": 2827.522433, "Python_Time_μs": 147921.94298158088, "Speedup": 52.31503780666234, "Performance_Ratio": "52.32x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>rr", "C++_Time_μs": 5030.425067, "Python_Time_μs": 200057.12288742265, "Speedup": 39.769427080787615, "Performance_Ratio": "39.77x", "Category": "timeseries_ops"}, {"Operator": "ts_Cov", "C++_Time_μs": 2760.657367, "Python_Time_μs": 109277.24701662858, "Speedup": 39.5837775172295, "Performance_Ratio": "39.58x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>_corr", "C++_Time_μs": 21133.2012, "Python_Time_μs": 619935.897551477, "Speedup": 29.33468960450142, "Performance_Ratio": "29.33x", "Category": "timeseries_ops"}, {"Operator": "ts_Sum", "C++_Time_μs": 3013.814733, "Python_Time_μs": 32363.781426101923, "Speedup": 10.73847740928869, "Performance_Ratio": "10.74x", "Category": "timeseries_ops"}, {"Operator": "ts_Mean", "C++_Time_μs": 4168.374033, "Python_Time_μs": 35093.411430716515, "Speedup": 8.418968919989075, "Performance_Ratio": "8.42x", "Category": "timeseries_ops"}, {"Operator": "ts_Scale", "C++_Time_μs": 9221.4301, "Python_Time_μs": 70646.47280300657, "Speedup": 7.661118941085568, "Performance_Ratio": "7.66x", "Category": "timeseries_ops"}, {"Operator": "ts_Stdev", "C++_Time_μs": 6560.483867, "Python_Time_μs": 44771.2535969913, "Speedup": 6.82438285111803, "Performance_Ratio": "6.82x", "Category": "timeseries_ops"}, {"Operator": "ts_Skewness", "C++_Time_μs": 8646.1786, "Python_Time_μs": 47718.800573299326, "Speedup": 5.51906255710463, "Performance_Ratio": "5.52x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON><PERSON>", "C++_Time_μs": 9169.357133, "Python_Time_μs": 47468.25695037842, "Speedup": 5.176835874299501, "Performance_Ratio": "5.18x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON><PERSON>", "C++_Time_μs": 9235.7657, "Python_Time_μs": 47429.17648206154, "Speedup": 5.135381085085511, "Performance_Ratio": "5.14x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 277.261933, "Python_Time_μs": 1390.299325188001, "Speedup": 5.014389498568493, "Performance_Ratio": "5.01x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 11703.2934, "Python_Time_μs": 49465.98516156276, "Speedup": 4.226672225577354, "Performance_Ratio": "4.23x", "Category": "timeseries_ops"}, {"Operator": "ts_Product", "C++_Time_μs": 8872.715967, "Python_Time_μs": 35244.148410856724, "Speedup": 3.9721939191944298, "Performance_Ratio": "3.97x", "Category": "timeseries_ops"}, {"Operator": "ts_Divide", "C++_Time_μs": 746.559133, "Python_Time_μs": 2846.232056617737, "Speedup": 3.8124669979996573, "Performance_Ratio": "3.81x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 274.258467, "Python_Time_μs": 900.3246203064919, "Speedup": 3.2827596177969296, "Performance_Ratio": "3.28x", "Category": "timeseries_ops"}, {"Operator": "ts_MeanChg", "C++_Time_μs": 15471.4589, "Python_Time_μs": 46487.62776826819, "Speedup": 3.0047345934692813, "Performance_Ratio": "3.00x", "Category": "timeseries_ops"}, {"Operator": "ts_ChgRate", "C++_Time_μs": 775.577567, "Python_Time_μs": 2164.873667061329, "Speedup": 2.7913051629835612, "Performance_Ratio": "2.79x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 13479.396133, "Python_Time_μs": 33017.99017935991, "Speedup": 2.449515531228131, "Performance_Ratio": "2.45x", "Category": "timeseries_ops"}, {"Operator": "ts_Rank", "C++_Time_μs": 53587.812, "Python_Time_μs": 129742.19148357709, "Speedup": 2.421113806318069, "Performance_Ratio": "2.42x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 15112.4812, "Python_Time_μs": 33422.22757637501, "Speedup": 2.2115645428478685, "Performance_Ratio": "2.21x", "Category": "timeseries_ops"}, {"Operator": "ts_TransNorm", "C++_Time_μs": 67337.912267, "Python_Time_μs": 147961.39361957708, "Speedup": 2.197297014984646, "Performance_Ratio": "2.20x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_D", "C++_Time_μs": 67169.7198, "Python_Time_μs": 141236.80389796695, "Speedup": 2.1026856196289647, "Performance_Ratio": "2.10x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_C", "C++_Time_μs": 66444.847533, "Python_Time_μs": 138535.1699156066, "Speedup": 2.0849648251025448, "Performance_Ratio": "2.08x", "Category": "timeseries_ops"}, {"Operator": "ts_Median", "C++_Time_μs": 63778.18, "Python_Time_μs": 129649.60808555286, "Speedup": 2.0328207560258518, "Performance_Ratio": "2.03x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_A", "C++_Time_μs": 65670.3349, "Python_Time_μs": 128680.33094952504, "Speedup": 1.95948948860203, "Performance_Ratio": "1.96x", "Category": "timeseries_ops"}, {"Operator": "ts_Entropy", "C++_Time_μs": 112215.021633, "Python_Time_μs": 167468.10705711445, "Speedup": 1.4923858198309674, "Performance_Ratio": "1.49x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_B", "C++_Time_μs": 109300.617933, "Python_Time_μs": 134475.42112941542, "Speedup": 1.2303262659671996, "Performance_Ratio": "1.23x", "Category": "timeseries_ops"}, {"Operator": "ts_Decay", "C++_Time_μs": 8671.804533, "Python_Time_μs": 9692.63706356287, "Speedup": 1.117718581718275, "Performance_Ratio": "1.12x", "Category": "timeseries_ops"}, {"Operator": "ts_Decay2", "C++_Time_μs": 9335.0022, "Python_Time_μs": 9636.636109401783, "Speedup": 1.032312141222825, "Performance_Ratio": "1.03x", "Category": "timeseries_ops"}, {"Operator": "Tot_Sum", "C++_Time_μs": 2994.2627, "Python_Time_μs": 33672.071962306894, "Speedup": 11.245530314460014, "Performance_Ratio": "11.25x", "Category": "panel_ops"}, {"Operator": "Tot_Mean", "C++_Time_μs": 4164.466067, "Python_Time_μs": 34157.546826948725, "Speedup": 8.202143150503602, "Performance_Ratio": "8.20x", "Category": "panel_ops"}, {"Operator": "pn_CrossFit", "C++_Time_μs": 3862.426733, "Python_Time_μs": 27690.845013906557, "Speedup": 7.169286805448008, "Performance_Ratio": "7.17x", "Category": "panel_ops"}, {"Operator": "Tot_Stdev", "C++_Time_μs": 6530.436233, "Python_Time_μs": 45846.629639466606, "Speedup": 7.02045437757919, "Performance_Ratio": "7.02x", "Category": "panel_ops"}, {"Operator": "Tot_Delta", "C++_Time_μs": 272.587767, "Python_Time_μs": 1426.5232098599274, "Speedup": 5.2332620262446605, "Performance_Ratio": "5.23x", "Category": "panel_ops"}, {"Operator": "Tot_Arg<PERSON>in", "C++_Time_μs": 10802.187133, "Python_Time_μs": 54740.542297561966, "Speedup": 5.067542491495363, "Performance_Ratio": "5.07x", "Category": "panel_ops"}, {"Operator": "Tot_ArgMax", "C++_Time_μs": 10887.748033, "Python_Time_μs": 54424.811558177076, "Speedup": 4.9987207081959735, "Performance_Ratio": "5.00x", "Category": "panel_ops"}, {"Operator": "Tot_Divide", "C++_Time_μs": 737.029567, "Python_Time_μs": 2657.9956834514937, "Speedup": 3.606362352965812, "Performance_Ratio": "3.61x", "Category": "panel_ops"}, {"Operator": "pn_Winsor", "C++_Time_μs": 2814.899567, "Python_Time_μs": 9312.58543084065, "Speedup": 3.3083188970630335, "Performance_Ratio": "3.31x", "Category": "panel_ops"}, {"Operator": "Tot_ChgRate", "C++_Time_μs": 738.799667, "Python_Time_μs": 2183.17915375034, "Speedup": 2.9550353786912846, "Performance_Ratio": "2.96x", "Category": "panel_ops"}, {"Operator": "Tot_Rank", "C++_Time_μs": 58711.150067, "Python_Time_μs": 149202.82425979772, "Speedup": 2.54130304191845, "Performance_Ratio": "2.54x", "Category": "panel_ops"}, {"Operator": "pn_Mean", "C++_Time_μs": 1235.866067, "Python_Time_μs": 2671.653280655543, "Speedup": 2.1617660295025667, "Performance_Ratio": "2.16x", "Category": "panel_ops"}, {"Operator": "To<PERSON>_<PERSON>", "C++_Time_μs": 19093.8506, "Python_Time_μs": 33506.429847329855, "Speedup": 1.7548283240118079, "Performance_Ratio": "1.75x", "Category": "panel_ops"}, {"Operator": "pn_Cut", "C++_Time_μs": 15574.251467, "Python_Time_μs": 26670.62000061075, "Speedup": 1.7124816596882775, "Performance_Ratio": "1.71x", "Category": "panel_ops"}, {"Operator": "Tot_Max", "C++_Time_μs": 20984.1048, "Python_Time_μs": 33915.40205727021, "Speedup": 1.61624250262371, "Performance_Ratio": "1.62x", "Category": "panel_ops"}, {"Operator": "pn_Rank", "C++_Time_μs": 12463.347367, "Python_Time_μs": 19898.95962178707, "Speedup": 1.5965983323609205, "Performance_Ratio": "1.60x", "Category": "panel_ops"}, {"Operator": "pn_TransStd", "C++_Time_μs": 3124.639833, "Python_Time_μs": 4889.910388737917, "Speedup": 1.564951690461893, "Performance_Ratio": "1.56x", "Category": "panel_ops"}, {"Operator": "pn_Rank2", "C++_Time_μs": 10977.9111, "Python_Time_μs": 17112.066068996985, "Speedup": 1.5587725126501513, "Performance_Ratio": "1.56x", "Category": "panel_ops"}, {"Operator": "pn_RankCentered", "C++_Time_μs": 14086.537733, "Python_Time_μs": 20271.535341938335, "Speedup": 1.4390715253222923, "Performance_Ratio": "1.44x", "Category": "panel_ops"}, {"Operator": "pn_TransNorm", "C++_Time_μs": 26954.7869, "Python_Time_μs": 35811.9871156911, "Speedup": 1.3285947037366896, "Performance_Ratio": "1.33x", "Category": "panel_ops"}, {"Operator": "pn_FillMax", "C++_Time_μs": 1105.2102, "Python_Time_μs": 1132.5016617774963, "Speedup": 1.0246934581109515, "Performance_Ratio": "1.02x", "Category": "panel_ops"}, {"Operator": "pn_FillMin", "C++_Time_μs": 1104.732967, "Python_Time_μs": 1118.0946913858254, "Speedup": 1.012094981126625, "Performance_Ratio": "1.01x", "Category": "panel_ops"}, {"Operator": "pn_Stand", "C++_Time_μs": 9791.5364, "Python_Time_μs": 4919.525732596715, "Speedup": 0.5024263334808943, "Performance_Ratio": "0.50x", "Category": "panel_ops"}, {"Operator": "pn_GroupNeutral", "C++_Time_μs": 4594.432433, "Python_Time_μs": 83596.4083050688, "Speedup": 18.19515457548765, "Performance_Ratio": "18.20x", "Category": "group_ops"}, {"Operator": "pn_GroupRank", "C++_Time_μs": 13423.069167, "Python_Time_μs": 140590.54516255856, "Speedup": 10.473800247427315, "Performance_Ratio": "10.47x", "Category": "group_ops"}, {"Operator": "pn_GroupNorm", "C++_Time_μs": 26950.7142, "Python_Time_μs": 198781.76686664423, "Speedup": 7.3757513582569265, "Performance_Ratio": "7.38x", "Category": "group_ops"}]}