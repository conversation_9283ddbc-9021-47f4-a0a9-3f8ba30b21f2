{"timestamp": "20250528_163434", "summary": {"total_categories": 8, "total_operators": 91, "overall_cpp_avg_time": 95867.09836778023, "overall_python_avg_time": 259154.88188936218, "overall_avg_speedup": 6.741638132378242, "max_speedup": 63.03376914544526, "min_speedup": 0.09529403679889135}, "category_summaries": {"core_math": {"total_operators": 17, "cpp_avg_time": 18660.38303735294, "python_avg_time": 28421.125108120486, "avg_speedup": 2.763661333448186, "max_speedup": 10.705303301767573, "min_speedup": 0.09529403679889135}, "logical_ops": {"total_operators": 4, "cpp_avg_time": 4823.6923335, "python_avg_time": 82636.83930660287, "avg_speedup": 17.08100778619688, "max_speedup": 21.51576216237613, "min_speedup": 14.964696207439049}, "comparison_ops": {"total_operators": 6, "cpp_avg_time": 5037.682116333333, "python_avg_time": 43076.62392449048, "avg_speedup": 8.578072073280959, "max_speedup": 9.207117868086618, "min_speedup": 7.88454958736357}, "data_utils": {"total_operators": 3, "cpp_avg_time": 1931.2777666666668, "python_avg_time": 33119.36844968133, "avg_speedup": 35.21531201415953, "max_speedup": 63.03376914544526, "min_speedup": 1.0122918495878355}, "reduction_ops": {"total_operators": 2, "cpp_avg_time": 4851.5794165, "python_avg_time": 112044.02508834997, "avg_speedup": 23.08057317609881, "max_speedup": 24.322724118367915, "min_speedup": 21.838422233829707}, "timeseries_ops": {"total_operators": 33, "cpp_avg_time": 179105.4914909697, "python_avg_time": 427551.8813998335, "avg_speedup": 5.900974185657817, "max_speedup": 23.950241548398964, "min_speedup": 0.9137754633341563}, "panel_ops": {"total_operators": 23, "cpp_avg_time": 88510.83416373913, "python_avg_time": 186322.71305752403, "avg_speedup": 2.9960095647309233, "max_speedup": 8.206916815568716, "min_speedup": 0.5359784227671733}, "group_ops": {"total_operators": 3, "cpp_avg_time": 131810.39356666667, "python_avg_time": 1264282.4607590835, "avg_speedup": 10.422304427710715, "max_speedup": 11.87808365010766, "min_speedup": 8.125278079523644}}, "detailed_results": [{"Operator": "Sqrt", "C++_Time_μs": 5050.1607, "Python_Time_μs": 54063.50201616684, "Speedup": 10.705303301767573, "Performance_Ratio": "10.71x", "Category": "core_math"}, {"Operator": "Softsign", "C++_Time_μs": 8438.2058, "Python_Time_μs": 89196.64310912292, "Speedup": 10.570569766042317, "Performance_Ratio": "10.57x", "Category": "core_math"}, {"Operator": "Sign", "C++_Time_μs": 5560.3655, "Python_Time_μs": 31861.98802043994, "Speedup": 5.73019669668117, "Performance_Ratio": "5.73x", "Category": "core_math"}, {"Operator": "Abs", "C++_Time_μs": 5074.6902, "Python_Time_μs": 26547.33903085192, "Speedup": 5.231322107278967, "Performance_Ratio": "5.23x", "Category": "core_math"}, {"Operator": "SignedPower", "C++_Time_μs": 22354.701867, "Python_Time_μs": 86249.18240432937, "Speedup": 3.8582121523011845, "Performance_Ratio": "3.86x", "Category": "core_math"}, {"Operator": "Log", "C++_Time_μs": 40203.0811, "Python_Time_μs": 62364.06425014138, "Speedup": 1.5512259892474107, "Performance_Ratio": "1.55x", "Category": "core_math"}, {"Operator": "Round", "C++_Time_μs": 2352.565367, "Python_Time_μs": 3175.587859004736, "Speedup": 1.3498404352752404, "Performance_Ratio": "1.35x", "Category": "core_math"}, {"Operator": "Exp", "C++_Time_μs": 73858.1823, "Python_Time_μs": 93167.77757679422, "Speedup": 1.2614415177232736, "Performance_Ratio": "1.26x", "Category": "core_math"}, {"Operator": "Floor", "C++_Time_μs": 2707.319767, "Python_Time_μs": 3012.2858161727586, "Speedup": 1.1126450051782002, "Performance_Ratio": "1.11x", "Category": "core_math"}, {"Operator": "Ceil", "C++_Time_μs": 2788.665133, "Python_Time_μs": 2741.170829782883, "Speedup": 0.9829688037279602, "Performance_Ratio": "0.98x", "Category": "core_math"}, {"Operator": "inv", "C++_Time_μs": 3287.024467, "Python_Time_μs": 3017.848885307709, "Speedup": 0.918109650720683, "Performance_Ratio": "0.92x", "Category": "core_math"}, {"Operator": "Reverse", "C++_Time_μs": 3036.7284, "Python_Time_μs": 2492.769807577133, "Speedup": 0.820873479359278, "Performance_Ratio": "0.82x", "Category": "core_math"}, {"Operator": "Multiply", "C++_Time_μs": 4907.850767, "Python_Time_μs": 3557.9202696681023, "Speedup": 0.7249446730514457, "Performance_Ratio": "0.72x", "Category": "core_math"}, {"Operator": "Minus", "C++_Time_μs": 4845.43, "Python_Time_μs": 3497.7241419255733, "Speedup": 0.7218604214539418, "Performance_Ratio": "0.72x", "Category": "core_math"}, {"Operator": "Divide", "C++_Time_μs": 4767.221567, "Python_Time_μs": 3311.245081325372, "Speedup": 0.6945859416828261, "Performance_Ratio": "0.69x", "Category": "core_math"}, {"Operator": "Add", "C++_Time_μs": 4851.5108, "Python_Time_μs": 3167.302471896013, "Speedup": 0.6528486903287967, "Performance_Ratio": "0.65x", "Category": "core_math"}, {"Operator": "Power", "C++_Time_μs": 123142.8079, "Python_Time_μs": 11734.775267541409, "Speedup": 0.09529403679889135, "Performance_Ratio": "0.10x", "Category": "core_math"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 5154.774, "Python_Time_μs": 110908.89138480027, "Speedup": 21.51576216237613, "Performance_Ratio": "21.52x", "Category": "logical_ops"}, {"Operator": "Not", "C++_Time_μs": 3662.401667, "Python_Time_μs": 59453.05395871401, "Speedup": 16.233351599420295, "Performance_Ratio": "16.23x", "Category": "logical_ops"}, {"Operator": "And", "C++_Time_μs": 5253.717267, "Python_Time_μs": 82011.68853168686, "Speedup": 15.610221175552052, "Performance_Ratio": "15.61x", "Category": "logical_ops"}, {"Operator": "Or", "C++_Time_μs": 5223.8764, "Python_Time_μs": 78173.72335121036, "Speedup": 14.964696207439049, "Performance_Ratio": "14.96x", "Category": "logical_ops"}, {"Operator": "Equal", "C++_Time_μs": 4673.850333, "Python_Time_μs": 43032.69091372689, "Speedup": 9.207117868086618, "Performance_Ratio": "9.21x", "Category": "comparison_ops"}, {"Operator": "Mthan", "C++_Time_μs": 4655.528333, "Python_Time_μs": 41332.084592431784, "Speedup": 8.878065309893106, "Performance_Ratio": "8.88x", "Category": "comparison_ops"}, {"Operator": "UnEqual", "C++_Time_μs": 5080.627333, "Python_Time_μs": 44280.882148693, "Speedup": 8.715632784376275, "Performance_Ratio": "8.72x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 4936.752933, "Python_Time_μs": 42888.078993807234, "Speedup": 8.687507674755096, "Performance_Ratio": "8.69x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 5437.478633, "Python_Time_μs": 44019.430254896484, "Speedup": 8.095559215211079, "Performance_Ratio": "8.10x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON><PERSON>", "C++_Time_μs": 5441.855133, "Python_Time_μs": 42906.576643387474, "Speedup": 7.88454958736357, "Performance_Ratio": "7.88x", "Category": "comparison_ops"}, {"Operator": "get<PERSON><PERSON>", "C++_Time_μs": 932.253567, "Python_Time_μs": 58763.45612729589, "Speedup": 63.03376914544526, "Performance_Ratio": "63.03x", "Category": "data_utils"}, {"Operator": "getInf", "C++_Time_μs": 878.9218, "Python_Time_μs": 36563.03705647588, "Speedup": 41.599875047445494, "Performance_Ratio": "41.60x", "Category": "data_utils"}, {"Operator": "FilterInf", "C++_Time_μs": 3982.657933, "Python_Time_μs": 4031.612165272236, "Speedup": 1.0122918495878355, "Performance_Ratio": "1.01x", "Category": "data_utils"}, {"Operator": "Min", "C++_Time_μs": 4905.350133, "Python_Time_μs": 119311.47798895836, "Speedup": 24.322724118367915, "Performance_Ratio": "24.32x", "Category": "reduction_ops"}, {"Operator": "Max", "C++_Time_μs": 4797.8087, "Python_Time_μs": 104776.57218774159, "Speedup": 21.838422233829707, "Performance_Ratio": "21.84x", "Category": "reduction_ops"}, {"Operator": "ts_Regression_A", "C++_Time_μs": 19135.804367, "Python_Time_μs": 458307.13681255776, "Speedup": 23.950241548398964, "Performance_Ratio": "23.95x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_B", "C++_Time_μs": 21760.048367, "Python_Time_μs": 462710.7189036906, "Speedup": 21.264232096350035, "Performance_Ratio": "21.26x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_C", "C++_Time_μs": 24015.139467, "Python_Time_μs": 482605.0840939085, "Speedup": 20.0958684731801, "Performance_Ratio": "20.10x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_D", "C++_Time_μs": 24379.1415, "Python_Time_μs": 489677.59121830267, "Speedup": 20.085924322573156, "Performance_Ratio": "20.09x", "Category": "timeseries_ops"}, {"Operator": "ts_Cov", "C++_Time_μs": 23476.019533, "Python_Time_μs": 332462.00466528535, "Speedup": 14.161770661246338, "Performance_Ratio": "14.16x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>rr", "C++_Time_μs": 42239.6918, "Python_Time_μs": 586963.597809275, "Speedup": 13.896019899682956, "Performance_Ratio": "13.90x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>_corr", "C++_Time_μs": 171948.056267, "Python_Time_μs": 1748477.3999080062, "Speedup": 10.16863719118162, "Performance_Ratio": "10.17x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 4062.2397, "Python_Time_μs": 28311.848764618237, "Speedup": 6.9695170288986725, "Performance_Ratio": "6.97x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON><PERSON>", "C++_Time_μs": 77839.902, "Python_Time_μs": 528959.0907904009, "Speedup": 6.795474778352121, "Performance_Ratio": "6.80x", "Category": "timeseries_ops"}, {"Operator": "ts_ChgRate", "C++_Time_μs": 6930.4996, "Python_Time_μs": 44480.836329360805, "Speedup": 6.418128402945266, "Performance_Ratio": "6.42x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON><PERSON>", "C++_Time_μs": 78909.107567, "Python_Time_μs": 501490.5559519927, "Speedup": 6.355293722289129, "Performance_Ratio": "6.36x", "Category": "timeseries_ops"}, {"Operator": "ts_Product", "C++_Time_μs": 77061.305633, "Python_Time_μs": 326439.3573005994, "Speedup": 4.2360994875333144, "Performance_Ratio": "4.24x", "Category": "timeseries_ops"}, {"Operator": "ts_Divide", "C++_Time_μs": 6819.6506, "Python_Time_μs": 28700.18556714058, "Speedup": 4.208453959084147, "Performance_Ratio": "4.21x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 3810.753967, "Python_Time_μs": 14914.113686730465, "Speedup": 3.9136910479874243, "Performance_Ratio": "3.91x", "Category": "timeseries_ops"}, {"Operator": "ts_Sum", "C++_Time_μs": 26159.947, "Python_Time_μs": 91693.78525887926, "Speedup": 3.5051212167547305, "Performance_Ratio": "3.51x", "Category": "timeseries_ops"}, {"Operator": "ts_Scale", "C++_Time_μs": 79377.0759, "Python_Time_μs": 248230.419245859, "Speedup": 3.1272305817687474, "Performance_Ratio": "3.13x", "Category": "timeseries_ops"}, {"Operator": "ts_Mean", "C++_Time_μs": 36499.703867, "Python_Time_μs": 94797.89758721988, "Speedup": 2.597223745503543, "Performance_Ratio": "2.60x", "Category": "timeseries_ops"}, {"Operator": "ts_Stdev", "C++_Time_μs": 55584.3129, "Python_Time_μs": 121151.99733525515, "Speedup": 2.1796077168969585, "Performance_Ratio": "2.18x", "Category": "timeseries_ops"}, {"Operator": "ts_Skewness", "C++_Time_μs": 72598.0754, "Python_Time_μs": 126580.16551285982, "Speedup": 1.7435746721304932, "Performance_Ratio": "1.74x", "Category": "timeseries_ops"}, {"Operator": "ts_Rank", "C++_Time_μs": 423326.936133, "Python_Time_μs": 734624.1603605449, "Speedup": 1.7353588861393459, "Performance_Ratio": "1.74x", "Category": "timeseries_ops"}, {"Operator": "ts_TransNorm", "C++_Time_μs": 543130.440567, "Python_Time_μs": 916749.9247007072, "Speedup": 1.687900099548219, "Performance_Ratio": "1.69x", "Category": "timeseries_ops"}, {"Operator": "ts_MeanChg", "C++_Time_μs": 129826.430233, "Python_Time_μs": 218551.3493294517, "Speedup": 1.6834118363819812, "Performance_Ratio": "1.68x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_D", "C++_Time_μs": 511902.715533, "Python_Time_μs": 776442.8104273975, "Speedup": 1.5167780651816913, "Performance_Ratio": "1.52x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_C", "C++_Time_μs": 512273.006767, "Python_Time_μs": 759077.5995204846, "Speedup": 1.4817833254792987, "Performance_Ratio": "1.48x", "Category": "timeseries_ops"}, {"Operator": "ts_Median", "C++_Time_μs": 506371.9151, "Python_Time_μs": 736877.7312027911, "Speedup": 1.455210506801646, "Performance_Ratio": "1.46x", "Category": "timeseries_ops"}, {"Operator": "ts_Entropy", "C++_Time_μs": 929375.6331, "Python_Time_μs": 1332834.6232883632, "Speedup": 1.434118321827092, "Performance_Ratio": "1.43x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_B", "C++_Time_μs": 511864.909067, "Python_Time_μs": 725879.3395323058, "Speedup": 1.4181072518829234, "Performance_Ratio": "1.42x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_A", "C++_Time_μs": 514831.6582, "Python_Time_μs": 688271.8242394427, "Speedup": 1.33688714218904, "Performance_Ratio": "1.34x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 97887.415667, "Python_Time_μs": 127901.41344691317, "Speedup": 1.3066175317368354, "Performance_Ratio": "1.31x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 107091.721167, "Python_Time_μs": 113982.53878578544, "Speedup": 1.0643450076597407, "Performance_Ratio": "1.06x", "Category": "timeseries_ops"}, {"Operator": "ts_Decay2", "C++_Time_μs": 71322.644367, "Python_Time_μs": 73487.91466404994, "Speedup": 1.0303588056257176, "Performance_Ratio": "1.03x", "Category": "timeseries_ops"}, {"Operator": "ts_Decay", "C++_Time_μs": 73985.195733, "Python_Time_μs": 73643.77848183115, "Speedup": 0.9953853301625237, "Performance_Ratio": "1.00x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 124684.122133, "Python_Time_μs": 113933.2914724946, "Speedup": 0.9137754633341563, "Performance_Ratio": "0.91x", "Category": "timeseries_ops"}, {"Operator": "Tot_Delta", "C++_Time_μs": 3695.3161, "Python_Time_μs": 30327.151839931805, "Speedup": 8.206916815568716, "Performance_Ratio": "8.21x", "Category": "panel_ops"}, {"Operator": "Tot_ChgRate", "C++_Time_μs": 6555.897167, "Python_Time_μs": 47027.63988946875, "Speedup": 7.17333397573543, "Performance_Ratio": "7.17x", "Category": "panel_ops"}, {"Operator": "pn_Winsor", "C++_Time_μs": 26383.5296, "Python_Time_μs": 140054.92394790053, "Speedup": 5.3084225678394645, "Performance_Ratio": "5.31x", "Category": "panel_ops"}, {"Operator": "Tot_ArgMax", "C++_Time_μs": 91467.129133, "Python_Time_μs": 482234.5873651405, "Speedup": 5.272217374002584, "Performance_Ratio": "5.27x", "Category": "panel_ops"}, {"Operator": "Tot_Arg<PERSON>in", "C++_Time_μs": 91424.2001, "Python_Time_μs": 473866.1617661516, "Speedup": 5.183158958435903, "Performance_Ratio": "5.18x", "Category": "panel_ops"}, {"Operator": "Tot_Divide", "C++_Time_μs": 6578.810133, "Python_Time_μs": 32774.57244694233, "Speedup": 4.981838932019278, "Performance_Ratio": "4.98x", "Category": "panel_ops"}, {"Operator": "pn_CrossFit", "C++_Time_μs": 59696.990733, "Python_Time_μs": 293126.8546419839, "Speedup": 4.91024507337429, "Performance_Ratio": "4.91x", "Category": "panel_ops"}, {"Operator": "Tot_Sum", "C++_Time_μs": 26064.7381, "Python_Time_μs": 85714.70932414134, "Speedup": 3.2885313865532892, "Performance_Ratio": "3.29x", "Category": "panel_ops"}, {"Operator": "pn_TransStd", "C++_Time_μs": 35792.589567, "Python_Time_μs": 89757.55053882797, "Speedup": 2.5077132340707333, "Performance_Ratio": "2.51x", "Category": "panel_ops"}, {"Operator": "Tot_Mean", "C++_Time_μs": 36221.506667, "Python_Time_μs": 89390.11658852299, "Speedup": 2.467874056436278, "Performance_Ratio": "2.47x", "Category": "panel_ops"}, {"Operator": "pn_Cut", "C++_Time_μs": 130562.7434, "Python_Time_μs": 269392.72399370867, "Speedup": 2.063320032793587, "Performance_Ratio": "2.06x", "Category": "panel_ops"}, {"Operator": "Tot_Stdev", "C++_Time_μs": 55593.672933, "Python_Time_μs": 114693.68068501353, "Speedup": 2.0630707530916204, "Performance_Ratio": "2.06x", "Category": "panel_ops"}, {"Operator": "pn_Rank2", "C++_Time_μs": 91912.0811, "Python_Time_μs": 188340.10750676194, "Speedup": 2.049133315802616, "Performance_Ratio": "2.05x", "Category": "panel_ops"}, {"Operator": "pn_TransNorm", "C++_Time_μs": 217900.128367, "Python_Time_μs": 430148.9020387332, "Speedup": 1.9740644728499266, "Performance_Ratio": "1.97x", "Category": "panel_ops"}, {"Operator": "pn_Rank", "C++_Time_μs": 103112.512867, "Python_Time_μs": 202826.9538966318, "Speedup": 1.967045009932488, "Performance_Ratio": "1.97x", "Category": "panel_ops"}, {"Operator": "pn_RankCentered", "C++_Time_μs": 116941.908033, "Python_Time_μs": 228537.57441043854, "Speedup": 1.9542829277759621, "Performance_Ratio": "1.95x", "Category": "panel_ops"}, {"Operator": "Tot_Rank", "C++_Time_μs": 464015.398433, "Python_Time_μs": 772933.182089279, "Speedup": 1.6657489917349892, "Performance_Ratio": "1.67x", "Category": "panel_ops"}, {"Operator": "pn_Mean", "C++_Time_μs": 18242.274733, "Python_Time_μs": 29326.53557509184, "Speedup": 1.6076139628596087, "Performance_Ratio": "1.61x", "Category": "panel_ops"}, {"Operator": "pn_FillMin", "C++_Time_μs": 15789.103833, "Python_Time_μs": 20059.32613586386, "Speedup": 1.2704537475989541, "Performance_Ratio": "1.27x", "Category": "panel_ops"}, {"Operator": "pn_FillMax", "C++_Time_μs": 16357.4822, "Python_Time_μs": 20176.138076931238, "Speedup": 1.2334501013200698, "Performance_Ratio": "1.23x", "Category": "panel_ops"}, {"Operator": "pn_Stand", "C++_Time_μs": 96388.171533, "Python_Time_μs": 59003.821263710655, "Speedup": 0.6121479464262871, "Performance_Ratio": "0.61x", "Category": "panel_ops"}, {"Operator": "To<PERSON>_<PERSON>", "C++_Time_μs": 151795.273767, "Python_Time_μs": 92846.78290908535, "Speedup": 0.6116579298219893, "Performance_Ratio": "0.61x", "Category": "panel_ops"}, {"Operator": "Tot_Max", "C++_Time_μs": 173257.727267, "Python_Time_μs": 92862.40339279175, "Speedup": 0.5359784227671733, "Performance_Ratio": "0.54x", "Category": "panel_ops"}, {"Operator": "pn_GroupNeutral", "C++_Time_μs": 55986.354467, "Python_Time_μs": 665010.6016236047, "Speedup": 11.87808365010766, "Performance_Ratio": "11.88x", "Category": "group_ops"}, {"Operator": "pn_GroupRank", "C++_Time_μs": 117820.5717, "Python_Time_μs": 1327078.0834058921, "Speedup": 11.26355155350084, "Performance_Ratio": "11.26x", "Category": "group_ops"}, {"Operator": "pn_GroupNorm", "C++_Time_μs": 221624.254533, "Python_Time_μs": 1800758.6972477536, "Speedup": 8.125278079523644, "Performance_Ratio": "8.13x", "Category": "group_ops"}]}