cmake_minimum_required(VERSION 3.10)
project(FeatureOperatorsTests)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Eigen3
find_package(Eigen3 REQUIRED)
add_compile_options(-Wall -O3 -g)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../include)
include_directories(${EIGEN3_INCLUDE_DIR})

# 添加feature_operators库
add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/.. feature_operators_build)

# 创建正确性测试可执行文件
add_executable(unified_correctness_test
    test_right/unified_correctness_test.cpp
)

# 创建性能测试可执行文件
add_executable(unified_benchmark
    test_benchmark/unified_benchmark.cpp
)

# 链接库
target_link_libraries(unified_correctness_test
    feature_ops_lib
    Eigen3::Eigen
)

target_link_libraries(unified_benchmark
    feature_ops_lib
    Eigen3::Eigen
)

# 创建输出目录
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_right/results/cpp)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_right/results/python)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_right/results/comparison)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_benchmark/results/cpp)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_benchmark/results/python)

# 添加正确性测试目标
add_custom_target(run_cpp_correctness_tests
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/unified_correctness_test all
    DEPENDS unified_correctness_test
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    COMMENT "Running C++ correctness tests"
)

add_custom_target(run_python_correctness_tests
    COMMAND python3 ${CMAKE_CURRENT_SOURCE_DIR}/test_right/unified_correctness_test.py --category all
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT "Running Python correctness tests"
)

add_custom_target(run_timeseries_v2_correctness
    COMMAND python3 ${CMAKE_CURRENT_SOURCE_DIR}/test_right/unified_correctness_test.py --category timeseries_v2
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT "Running Time Series V2 correctness comparison"
)

# 添加性能测试目标
add_custom_target(run_cpp_benchmarks
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/unified_benchmark all 100
    DEPENDS unified_benchmark
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    COMMENT "Running C++ performance benchmarks"
)

add_custom_target(run_python_benchmarks
    COMMAND python3 ${CMAKE_CURRENT_SOURCE_DIR}/test_benchmark/unified_benchmark_test.py --category all --iterations 100
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT "Running Python performance benchmarks"
)

add_custom_target(run_core_math_benchmark
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/unified_benchmark core_math 100
    DEPENDS unified_benchmark
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    COMMENT "Running core math performance benchmark"
)

# 添加帮助目标
add_custom_target(test_help
    COMMAND echo "Available test targets:"
    COMMAND echo ""
    COMMAND echo "=== Correctness Tests ==="
    COMMAND echo "  run_cpp_correctness_tests     - Run C++ correctness tests"
    COMMAND echo "  run_python_correctness_tests  - Run Python correctness tests"
    COMMAND echo "  run_timeseries_v2_correctness  - Run Time Series V2 correctness comparison"
    COMMAND echo ""
    COMMAND echo "=== Performance Tests ==="
    COMMAND echo "  run_cpp_benchmarks            - Run C++ performance benchmarks"
    COMMAND echo "  run_python_benchmarks         - Run Python performance benchmarks"
    COMMAND echo "  run_core_math_benchmark       - Run core math performance benchmark"
    COMMAND echo ""
    COMMAND echo "=== Manual Usage ==="
    COMMAND echo "  ./unified_correctness_test <category>"
    COMMAND echo "  ./unified_benchmark <category> <iterations>"
    COMMAND echo "  python3 test_right/unified_correctness_test.py --category <category>"
    COMMAND echo "  python3 test_benchmark/unified_benchmark_test.py --category <category> --iterations <num>"
    COMMAND echo ""
    COMMAND echo "Categories: core_math, logical_ops, comparison_ops, data_utils, reduction_ops,"
    COMMAND echo "           timeseries_ops, timeseries_v2, panel_ops, group_ops, all"
    COMMENT "Showing available test targets"
)
