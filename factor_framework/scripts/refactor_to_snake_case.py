#!/usr/bin/env python3
"""
重构脚本：将所有类名和函数名改为小写下划线命名法
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Tuple

class SnakeCaseRefactor:
    def __init__(self, base_dir: str):
        self.base_dir = Path(base_dir)
        
        # 类名映射 (CamelCase -> snake_case)
        self.class_mappings = {
            'FactorBase': 'factor_base',
            'FactorManager': 'factor_manager', 
            'FactorEngine': 'factor_engine',
            'DataInterface': 'data_interface',
            'FactorRegistry': 'factor_registry',
            'FactorFactory': 'factor_factory',
            'framework_config&': 'framework_config',
            'FrameworkVersion': 'framework_version',
            'FrameworkUtils': 'framework_utils',
            'calculation_result': 'calculation_result',
            'PerformanceStats': 'performance_stats'
        }
        
        # 函数名映射 (camelCase -> snake_case)
        self.function_mappings = {
            'getId': 'get_id',
            'getName': 'get_name', 
            'getFormula': 'get_formula',
            'getRequiredFields': 'get_required_fields',
            'validateInput': 'validate_input',
            'getDescription': 'get_description',
            'setParameters': 'set_parameters',
            'checkFieldExists': 'check_field_exists',
            'getField': 'get_field',
            'registerFactory': 'register_factory',
            'createFactor': 'create_factor',
            'getRegisteredFactors': 'get_registered_factors',
            'getInstance': 'get_instance',
            'addFactor': 'add_factor',
            'registerFactor': 'register_factor',
            'registerFactors': 'register_factors',
            'removeFactor': 'remove_factor',
            'selectFactors': 'select_factors',
            'clearSelection': 'clear_selection',
            'calculateSelectedFactors': 'calculate_selected_factors',
            'calculateFactors': 'calculate_factors',
            'calculateSingleFactor': 'calculate_single_factor',
            'printFactorInfo': 'print_factor_info',
            'printSelectedFactorInfo': 'print_selected_factor_info',
            'loadFactorsFromCSV': 'load_factors_from_csv',
            'parseCSVLine': 'parse_csv_line',
            'cleanString': 'clean_string',
            'getAllGeneratedFactorNames': 'get_all_generated_factor_names',
            'getAllGeneratedFactorIds': 'get_all_generated_factor_ids',
            'initializeAllFactors': 'initialize_all_factors',
            'registerAllFactorsToManager': 'register_all_factors_to_manager',
            'getFactorRegistrationList': 'get_factor_registration_list'
        }
        
        # 需要处理的文件扩展名
        self.file_extensions = ['.hpp', '.cpp', '.h', '.cc']
        
    def camel_to_snake(self, name: str) -> str:
        """将CamelCase转换为snake_case"""
        # 处理连续大写字母的情况，如HTTPSConnection -> https_connection
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
        
    def find_cpp_files(self) -> List[Path]:
        """查找所有C++文件"""
        files = []
        for ext in self.file_extensions:
            files.extend(self.base_dir.rglob(f'*{ext}'))
        return files
        
    def refactor_file(self, file_path: Path) -> bool:
        """重构单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            original_content = content
            
            # 替换类名
            for old_name, new_name in self.class_mappings.items():
                # 类声明/定义
                content = re.sub(rf'\bclass\s+{old_name}\b', f'class {new_name}', content)
                # 类型引用
                content = re.sub(rf'\b{old_name}\b(?=\s*[&*<>(),;:])', new_name, content)
                # 构造函数/析构函数
                content = re.sub(rf'\b{old_name}::{old_name}\b', f'{new_name}::{new_name}', content)
                content = re.sub(rf'\b~{old_name}\b', f'~{new_name}', content)
                
            # 替换函数名
            for old_name, new_name in self.function_mappings.items():
                # 函数声明/定义
                content = re.sub(rf'\b{old_name}\s*\(', f'{new_name}(', content)
                # 函数调用
                content = re.sub(rf'\.{old_name}\s*\(', f'.{new_name}(', content)
                content = re.sub(rf'->{old_name}\s*\(', f'->{new_name}(', content)
                content = re.sub(rf'::{old_name}\s*\(', f'::{new_name}(', content)
                
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✓ 重构文件: {file_path}")
                return True
            else:
                print(f"- 跳过文件: {file_path} (无需修改)")
                return False
                
        except Exception as e:
            print(f"❌ 重构文件失败: {file_path}, 错误: {e}")
            return False
            
    def refactor_all(self) -> None:
        """重构所有文件"""
        print("开始重构所有C++文件...")
        
        files = self.find_cpp_files()
        print(f"找到 {len(files)} 个C++文件")
        
        modified_count = 0
        for file_path in files:
            if self.refactor_file(file_path):
                modified_count += 1
                
        print(f"\n重构完成！修改了 {modified_count} 个文件")
        
    def generate_mapping_report(self) -> None:
        """生成映射报告"""
        print("\n=== 类名映射 ===")
        for old, new in self.class_mappings.items():
            print(f"  {old} -> {new}")
            
        print("\n=== 函数名映射 ===")
        for old, new in self.function_mappings.items():
            print(f"  {old} -> {new}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='重构C++代码为snake_case命名')
    parser.add_argument('--base-dir', default='.', help='基础目录路径')
    parser.add_argument('--dry-run', action='store_true', help='只显示映射，不实际修改文件')
    
    args = parser.parse_args()
    
    refactor = SnakeCaseRefactor(args.base_dir)
    
    if args.dry_run:
        refactor.generate_mapping_report()
    else:
        refactor.generate_mapping_report()
        print("\n" + "="*50)
        refactor.refactor_all()

if __name__ == '__main__':
    main()
