#ifndef FACTOR_FRAMEWORK_FACTOR_BASE_HPP
#define FACTOR_FRAMEWORK_FACTOR_BASE_HPP

#include "feature_operators/types.hpp"
#include <string>
#include <memory>
#include <unordered_map>
#include <functional>
#include <vector>
#include <iostream>

namespace factor_framework {

// 前向声明
class factor_manager;

/**
 * 因子基类 - 所有因子的抽象基类
 * 提供统一的接口和通用功能
 */
class factor_base {
public:
    /**
     * 构造函数
     * @param factor_id 因子ID
     * @param factor_name 因子名称
     * @param formula 因子公式
     */
    factor_base(int factor_id, const std::string& factor_name, const std::string& formula);

    /**
     * 虚析构函数
     */
    virtual ~factor_base() = default;

    /**
     * 计算因子值 - 纯虚函数，由子类实现
     * @param data_map 输入数据映射 (字段名 -> DataFrame)
     * @return 计算结果
     */
    virtual feature_operators::DataFrame calculate(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) = 0;

    /**
     * 获取因子ID
     */
    int get_id() const { return factor_id_; }

    /**
     * 获取因子名称
     */
    const std::string& get_name() const { return factor_name_; }

    /**
     * 获取因子公式
     */
    const std::string& get_formula() const { return formula_; }

    /**
     * 获取因子依赖的数据字段
     */
    virtual std::vector<std::string> get_required_fields() const = 0;

    /**
     * 验证输入数据是否有效
     * @param data_map 输入数据映射
     * @return 是否有效
     */
    virtual bool validate_input(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map) const;

    /**
     * 设置因子参数
     * @param params 参数映射
     */
    virtual void set_parameters(const std::unordered_map<std::string, double>& params) {}

    /**
     * 获取因子描述信息
     */
    virtual std::string get_description() const;

protected:
    int factor_id_;           ///< 因子ID
    std::string factor_name_; ///< 因子名称
    std::string formula_;     ///< 因子公式

    /**
     * 检查数据字段是否存在
     * @param data_map 数据映射
     * @param field_name 字段名
     * @return 是否存在
     */
    bool check_field_exists(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map,
        const std::string& field_name) const;

    /**
     * 获取数据字段
     * @param data_map 数据映射
     * @param field_name 字段名
     * @return 数据引用
     */
    const feature_operators::DataFrame& get_field(
        const std::unordered_map<std::string, feature_operators::DataFrame>& data_map,
        const std::string& field_name) const;
};

/**
 * 因子工厂基类
 */
class factor_factory {
public:
    virtual ~factor_factory() = default;

    /**
     * 创建因子实例
     * @param factor_id 因子ID
     * @param factor_name 因子名称
     * @param formula 因子公式
     * @return 因子实例指针
     */
    virtual std::unique_ptr<factor_base> create_factor(
        int factor_id,
        const std::string& factor_name,
        const std::string& formula) = 0;
};

/**
 * 因子注册器 - 用于注册因子工厂
 */
class factor_registry {
public:
    /**
     * 获取单例实例
     */
    static factor_registry& get_instance();

    /**
     * 注册因子工厂
     * @param factor_name 因子名称
     * @param factory 工厂指针
     */
    void register_factory(const std::string& factor_name,
                        std::unique_ptr<factor_factory> factory);

    /**
     * 创建因子实例
     * @param factor_id 因子ID
     * @param factor_name 因子名称
     * @param formula 因子公式
     * @return 因子实例指针
     */
    std::unique_ptr<factor_base> create_factor(
        int factor_id,
        const std::string& factor_name,
        const std::string& formula);

    /**
     * 获取所有注册的因子名称
     */
    std::vector<std::string> get_registered_factors() const;

private:
    factor_registry() = default;
    std::unordered_map<std::string, std::unique_ptr<factor_factory>> factories_;
};

/**
 * 简化的因子注册宏 - 直接注册因子实例到FactorManager
 * 使用方式: REGISTER_FACTOR_TO_MANAGER(FactorClass, factor_id, factor_name, formula, manager)
 */
#define REGISTER_FACTOR_TO_MANAGER(FactorClass, factor_id, factor_name, formula, manager) \
    do { \
        auto factor_instance = std::make_unique<FactorClass>(factor_id, factor_name, formula); \
        manager->register_factor(std::move(factor_instance)); \
    } while(0)

/**
 * 全局因子注册宏 - 注册到全局管理器（需要先设置全局管理器）
 * 使用方式: REGISTER_FACTOR_GLOBAL(FactorClass, factor_id, factor_name, formula)
 */
extern std::shared_ptr<factor_framework::factor_manager> g_global_factor_manager;

#define REGISTER_FACTOR_GLOBAL(FactorClass, factor_id, factor_name, formula) \
    namespace { \
        static bool FactorClass##_registered = []() { \
            if (factor_framework::g_global_factor_manager) { \
                auto factor_instance = std::make_unique<FactorClass>(factor_id, factor_name, formula); \
                factor_framework::g_global_factor_manager->register_factor(std::move(factor_instance)); \
                return true; \
            } \
            return false; \
        }(); \
    }

/**
 * 简单的因子注册宏 - 注册到静态列表，稍后批量注册
 * 使用方式: REGISTER_FACTOR_SIMPLE(FactorClass, factor_id, factor_name, formula)
 */
using factor_creator_function = std::function<std::unique_ptr<factor_base>()>;
std::vector<factor_creator_function>& get_factor_registration_list();

#define REGISTER_FACTOR_SIMPLE(FactorClass, factor_id, factor_name, formula) \
    namespace { \
        struct FactorClass##_Registrar { \
            FactorClass##_Registrar() { \
                factor_framework::get_factor_registration_list().emplace_back( \
                    [](){ return std::make_unique<FactorClass>(factor_id, factor_name, formula); } \
                ); \
            } \
        }; \
        static FactorClass##_Registrar FactorClass##_registrar_instance; \
    }

} // namespace factor_framework

#endif // FACTOR_FRAMEWORK_FACTOR_BASE_HPP
