#ifndef FACTOR_FRAMEWORK_HPP
#define FACTOR_FRAMEWORK_HPP

/**
 * 因子框架主头文件
 *
 * 这个框架提供了一个完整的因子计算系统，包括：
 * 1. 因子基类和工厂模式
 * 2. 因子管理器
 * 3. 数据接口
 * 4. 高性能计算引擎
 * 5. 自动代码生成支持
 *
 * 使用示例：
 * ```cpp
 * #include "factor_framework/factor_framework.hpp"
 *
 * // 创建数据接口并加载数据
 * auto data_interface = std::make_shared<data_interface>();
 * data_interface->loadDataFromCSV("Close", "close.csv");
 * data_interface->loadDataFromCSV("Volume", "volume.csv");
 *
 * // 创建因子管理器并加载因子配置
 * auto factor_manager = std::make_shared<factor_manager>();
 * factor_manager->load_factors_from_csv("feature.csv");
 *
 * // 创建计算引擎
 * FactorEngine engine(factor_manager, data_interface);
 *
 * // 选择要计算的因子
 * factor_manager->select_factors({0, 1, 2});
 *
 * // 执行计算
 * auto result = engine.calculate_selected_factors();
 *
 * if (result.success) {
 *     // 处理计算结果
 *     for (const auto& [factor_name, factor_data] : result.factor_results) {
 *         std::cout << "Factor: " << factor_name << std::endl;
 *         // 使用 factor_data...
 *     }
 * }
 * ```
 */

// 包含所有框架组件
#include "factor_base.hpp"
#include "factor_manager.hpp"
#include "data_interface.hpp"
#include "factor_engine.hpp"

// 包含feature_operators库
#include "feature_operators.hpp"

namespace factor_framework {

/**
 * 框架版本信息
 */
struct framework_version {
    static constexpr int MAJOR = 1;
    static constexpr int MINOR = 0;
    static constexpr int PATCH = 0;

    static std::string toString() {
        return std::to_string(MAJOR) + "." +
               std::to_string(MINOR) + "." +
               std::to_string(PATCH);
    }
};

/**
 * 框架配置
 */
struct framework_config {
    int default_num_threads = 0;           ///< 默认线程数（0=自动检测）
    bool enable_performance_stats = true;  ///< 默认启用性能统计
    bool verbose_logging = false;          ///< 默认禁用详细日志
    int default_timeout_ms = 30000;        ///< 默认超时时间（30秒）

    /**
     * 从环境变量加载配置
     */
    void load_from_environment();

    /**
     * 从配置文件加载配置
     * @param config_file 配置文件路径
     */
    bool load_from_file(const std::string& config_file);

    /**
     * 保存配置到文件
     * @param config_file 配置文件路径
     */
    bool save_to_file(const std::string& config_file) const;
};

/**
 * 框架工具类 - 提供便捷的静态方法
 */
class framework_utils {
public:
    /**
     * 创建完整的因子计算系统
     * @param feature_csv_path 因子配置CSV文件路径
     * @param data_directory 数据目录路径
     * @param config 框架配置
     * @return 计算引擎实例
     */
    static std::unique_ptr<factor_engine> create_factor_system(
        const std::string& feature_csv_path,
        const std::string& data_directory,
        const framework_config& config = framework_config{});

    /**
     * 创建测试系统（使用随机数据）
     * @param feature_csv_path 因子配置CSV文件路径
     * @param rows 数据行数
     * @param cols 数据列数
     * @param config 框架配置
     * @return 计算引擎实例
     */
    static std::unique_ptr<factor_engine> create_test_system(
        const std::string& feature_csv_path,
        int rows = 1000,
        int cols = 100,
        const framework_config& config = framework_config{});

    /**
     * 运行因子计算基准测试
     * @param engine 计算引擎
     * @param factor_ids 要测试的因子ID列表
     * @param num_runs 运行次数
     * @return 基准测试结果
     */
    static std::string run_benchmark(
        factor_engine& engine,
        const std::vector<int>& factor_ids,
        int num_runs = 10);

    /**
     * 验证因子计算结果的正确性
     * @param engine 计算引擎
     * @param factor_ids 要验证的因子ID列表
     * @return 验证结果
     */
    static bool validate_factor_results(
        factor_engine& engine,
        const std::vector<int>& factor_ids);

    /**
     * 导出因子计算结果到CSV
     * @param results 计算结果
     * @param output_directory 输出目录
     * @return 是否成功
     */
    static bool export_results_to_csv(
        const std::unordered_map<std::string, feature_operators::DataFrame>& results,
        const std::string& output_directory);

    /**
     * 生成因子计算报告
     * @param engine 计算引擎
     * @param results 计算结果
     * @param output_file 输出文件路径
     * @return 是否成功
     */
    static bool generat_report(
        const factor_engine& engine,
        const calculation_result& results,
        const std::string& output_file);

    /**
     * 获取系统信息
     * @return 系统信息字符串
     */
    static std::string get_system_info();

    /**
     * 获取框架信息
     * @return 框架信息字符串
     */
    static std::string get_framework_info();
};

/**
 * 框架初始化器 - 自动初始化框架
 */
class framework_initializer {
public:
    /**
     * 初始化框架
     * @param config 配置
     */
    static void initialize(const framework_config& config = framework_config{});

    /**
     * 清理框架资源
     */
    static void cleanup();

    /**
     * 检查框架是否已初始化
     */
    static bool is_initialized();

private:
    inline static bool initialized_{false};
    inline static framework_config config_;
};

} // namespace factor_framework

/**
 * 便捷宏定义
 */
#define FACTOR_FRAMEWORK_VERSION factor_framework::framework_version::toString()

#define FACTOR_FRAMEWORK_INIT() \
    factor_framework::framework_initializer::initialize()

#define FACTOR_FRAMEWORK_INIT_WITH_CONFIG(config) \
    factor_framework::framework_initializer::initialize(config)

#define FACTOR_FRAMEWORK_CLEANUP() \
    factor_framework::framework_initializer::cleanup()

#endif // FACTOR_FRAMEWORK_HPP
