#include "factor_framework/data_interface.hpp"
#include <fstream>
#include <sstream>
#include <iostream>
#include <random>
#include <algorithm>
#include <memory>

namespace factor_framework {

bool data_interface::load_data_from_csv(const std::string& field_name, const std::string& csv_file_path) {
    try {
        auto data = parse_csv_file(csv_file_path);
        if (validate_data(data)) {
            add_data(field_name, data);
            std::cout << "Loaded " << field_name << " from " << csv_file_path
                     << " (" << data.rows() << "x" << data.cols() << ")" << std::endl;
            return true;
        }
        return false;
    } catch (const std::exception& e) {
        std::cerr << "Error loading " << field_name << " from " << csv_file_path
                 << ": " << e.what() << std::endl;
        return false;
    }
}

void data_interface::add_data(const std::string& field_name, const feature_operators::DataFrame& data) {
    data_map_[field_name] = data;
}

const feature_operators::DataFrame& data_interface::get_data(const std::string& field_name) const {
    auto it = data_map_.find(field_name);
    if (it == data_map_.end()) {
        throw std::runtime_error("Field not found: " + field_name);
    }
    return it->second;
}

bool data_interface::has_field(const std::string& field_name) const {
    return data_map_.find(field_name) != data_map_.end();
}

std::vector<std::string> data_interface::get_field_names() const {
    std::vector<std::string> names;
    names.reserve(data_map_.size());

    for (const auto& [name, data] : data_map_) {
        names.push_back(name);
    }

    return names;
}

const std::unordered_map<std::string, feature_operators::DataFrame>& data_interface::get_all_data() const {
    return data_map_;
}

bool data_interface::remove_field(const std::string& field_name) {
    return data_map_.erase(field_name) > 0;
}

void data_interface::clear() {
    data_map_.clear();
}

std::pair<int, int> data_interface::get_data_dimensions(const std::string& field_name) const {
    if (!has_field(field_name)) {
        return {0, 0};
    }

    const auto& data = get_data(field_name);
    return {data.rows(), data.cols()};
}

bool data_interface::validate_data_dimensions() const {
    if (data_map_.empty()) {
        return true;
    }

    auto first_it = data_map_.begin();
    int expected_rows = first_it->second.rows();
    int expected_cols = first_it->second.cols();

    for (const auto& [name, data] : data_map_) {
        if (data.rows() != expected_rows || data.cols() != expected_cols) {
            std::cerr << "Dimension mismatch for field " << name
                     << ": expected (" << expected_rows << "x" << expected_cols
                     << "), got (" << data.rows() << "x" << data.cols() << ")" << std::endl;
            return false;
        }
    }

    return true;
}

std::string data_interface::get_data_statistics(const std::string& field_name) const {
    if (!has_field(field_name)) {
        return "Field not found: " + field_name;
    }

    const auto& data = get_data(field_name);

    std::ostringstream oss;
    oss << "Field: " << field_name << "\n";
    oss << "Dimensions: " << data.rows() << "x" << data.cols() << "\n";
    oss << "Min: " << data.minCoeff() << "\n";
    oss << "Max: " << data.maxCoeff() << "\n";
    oss << "Mean: " << data.mean() << "\n";

    return oss.str();
}

void data_interface::print_data_info() const {
    std::cout << "\n=== Data Interface Info ===" << std::endl;
    std::cout << "Fields: " << data_map_.size() << std::endl;

    for (const auto& [name, data] : data_map_) {
        std::cout << "  " << name << ": " << data.rows() << "x" << data.cols() << std::endl;
    }

    if (validate_data_dimensions()) {
        std::cout << "✓ All data dimensions are consistent" << std::endl;
    } else {
        std::cout << "❌ Data dimensions are inconsistent" << std::endl;
    }
}

void data_interface::create_standard_market_data(int rows, int cols, double fill_value) {
    std::cout << "Creating standard market data: " << rows << "x" << cols << std::endl;

    // 创建随机数生成器
    std::random_device rd;
    std::mt19937 gen(42);  // 固定种子以便重现
    std::normal_distribution<double> price_dist(100.0, 10.0);
    std::normal_distribution<double> volume_dist(1000000, 200000);
    std::normal_distribution<double> return_dist(0.0, 0.02);

    // 创建基础数据
    feature_operators::DataFrame close_data(rows, cols);
    feature_operators::DataFrame open_data(rows, cols);
    feature_operators::DataFrame high_data(rows, cols);
    feature_operators::DataFrame low_data(rows, cols);
    feature_operators::DataFrame volume_data(rows, cols);
    feature_operators::DataFrame amount_data(rows, cols);

    // 生成第一行数据
    for (int j = 0; j < cols; ++j) {
        double base_price = std::abs(price_dist(gen));
        close_data(0, j) = base_price;
        open_data(0, j) = base_price * (1.0 + return_dist(gen) * 0.1);
        high_data(0, j) = std::max(open_data(0, j), close_data(0, j)) * (1.0 + std::abs(return_dist(gen)) * 0.1);
        low_data(0, j) = std::min(open_data(0, j), close_data(0, j)) * (1.0 - std::abs(return_dist(gen)) * 0.1);
        volume_data(0, j) = std::abs(volume_dist(gen));
        amount_data(0, j) = volume_data(0, j) * close_data(0, j);
    }

    // 生成时间序列数据
    for (int i = 1; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            // 价格随机游走
            double return_rate = return_dist(gen);
            close_data(i, j) = close_data(i-1, j) * (1.0 + return_rate);
            open_data(i, j) = close_data(i-1, j) * (1.0 + return_dist(gen) * 0.5);

            double day_high = std::max(open_data(i, j), close_data(i, j)) * (1.0 + std::abs(return_dist(gen)) * 0.2);
            double day_low = std::min(open_data(i, j), close_data(i, j)) * (1.0 - std::abs(return_dist(gen)) * 0.2);

            high_data(i, j) = day_high;
            low_data(i, j) = day_low;

            // 成交量变化
            double volume_change = return_dist(gen) * 0.3;
            volume_data(i, j) = volume_data(i-1, j) * (1.0 + volume_change);
            volume_data(i, j) = std::max(volume_data(i, j), 1000.0);

            amount_data(i, j) = volume_data(i, j) * close_data(i, j);
        }
    }

    // 计算VWAP（简化为Close的加权平均）
    auto vwap_data = close_data;

    // 添加到数据映射
    add_data("Close", close_data);
    add_data("Open", open_data);
    add_data("High", high_data);
    add_data("Low", low_data);
    add_data("Volume", volume_data);
    add_data("Amount", amount_data);
    add_data("VWAP", vwap_data);

    std::cout << "✓ Standard market data created successfully" << std::endl;
}

feature_operators::DataFrame data_interface::parse_csv_file(const std::string& csv_file_path) const {
    std::ifstream file(csv_file_path);
    if (!file.is_open()) {
        throw std::runtime_error("Cannot open file: " + csv_file_path);
    }

    std::vector<std::vector<double>> data;
    std::string line;

    while (std::getline(file, line)) {
        if (line.empty()) continue;

        std::vector<double> row;
        std::stringstream ss(line);
        std::string cell;

        while (std::getline(ss, cell, ',')) {
            try {
                row.push_back(std::stod(cell));
            } catch (const std::exception&) {
                row.push_back(std::numeric_limits<double>::quiet_NaN());
            }
        }

        if (!row.empty()) {
            data.push_back(row);
        }
    }

    if (data.empty()) {
        throw std::runtime_error("No valid data found in file: " + csv_file_path);
    }

    int rows = data.size();
    int cols = data[0].size();

    feature_operators::DataFrame result(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols && j < (int)data[i].size(); ++j) {
            result(i, j) = data[i][j];
        }
    }

    return result;
}

bool data_interface::validate_data(const feature_operators::DataFrame& data) const {
    if (data.rows() == 0 || data.cols() == 0) {
        return false;
    }

    // 检查是否包含过多的NaN或无穷大
    int invalid_count = 0;
    int total_count = data.rows() * data.cols();

    for (int i = 0; i < data.rows(); ++i) {
        for (int j = 0; j < data.cols(); ++j) {
            if (!std::isfinite(data(i, j))) {
                invalid_count++;
            }
        }
    }

    // 如果超过50%的数据无效，则认为数据不可用
    return (double)invalid_count / total_count < 0.5;
}

// data_loader_factory implementations
std::unique_ptr<data_interface> data_loader_factory::create_from_test_data(const std::string& test_data_dir) {
    auto interface = std::make_unique<data_interface>();

    // 尝试加载标准测试数据文件
    std::vector<std::string> standard_fields = {"close", "open", "high", "low", "volume"};

    for (const auto& field : standard_fields) {
        std::string file_path = test_data_dir + "/" + field + ".csv";
        std::ifstream test_file(file_path);
        if (test_file.good()) {
            std::string field_name = field;
            field_name[0] = std::toupper(field_name[0]);  // 首字母大写
            interface->load_data_from_csv(field_name, file_path);
        }
    }

    return interface;
}

std::unique_ptr<data_interface> data_loader_factory::create_random_test_data(int rows, int cols, int seed) {
    auto interface = std::make_unique<data_interface>();
    interface->create_standard_market_data(rows, cols, 1.0);
    return interface;
}

} // namespace factor_framework
