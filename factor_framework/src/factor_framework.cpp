#include "factor_framework/factor_framework.hpp"
#include <iostream>
#include <fstream>
#include <thread>
#include <cstdlib>

namespace factor_framework {

// framework_config& implementation
void framework_config::load_from_environment() {
    // 从环境变量加载配置
    const char* threads_env = std::getenv("FACTOR_FRAMEWORK_THREADS");
    if (threads_env) {
        default_num_threads = std::atoi(threads_env);
    }

    const char* stats_env = std::getenv("FACTOR_FRAMEWORK_ENABLE_STATS");
    if (stats_env) {
        enable_performance_stats = (std::string(stats_env) == "true" || std::string(stats_env) == "1");
    }

    const char* verbose_env = std::getenv("FACTOR_FRAMEWORK_VERBOSE");
    if (verbose_env) {
        verbose_logging = (std::string(verbose_env) == "true" || std::string(verbose_env) == "1");
    }

    const char* timeout_env = std::getenv("FACTOR_FRAMEWORK_TIMEOUT");
    if (timeout_env) {
        default_timeout_ms = std::atoi(timeout_env);
    }
}

bool framework_config::load_from_file(const std::string& config_file) {
    // 简化实现：这里可以实现JSON或INI文件解析
    std::ifstream file(config_file);
    if (!file.is_open()) {
        return false;
    }

    // 简单的键值对解析
    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == '#') continue;

        size_t pos = line.find('=');
        if (pos != std::string::npos) {
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);

            if (key == "default_num_threads") {
                default_num_threads = std::stoi(value);
            } else if (key == "enable_performance_stats") {
                enable_performance_stats = (value == "true" || value == "1");
            } else if (key == "verbose_logging") {
                verbose_logging = (value == "true" || value == "1");
            } else if (key == "default_timeout_ms") {
                default_timeout_ms = std::stoi(value);
            }
        }
    }

    return true;
}

bool framework_config::save_to_file(const std::string& config_file) const {
    std::ofstream file(config_file);
    if (!file.is_open()) {
        return false;
    }

    file << "# Factor Framework Configuration\n";
    file << "default_num_threads=" << default_num_threads << "\n";
    file << "enable_performance_stats=" << (enable_performance_stats ? "true" : "false") << "\n";
    file << "verbose_logging=" << (verbose_logging ? "true" : "false") << "\n";
    file << "default_timeout_ms=" << default_timeout_ms << "\n";

    return true;
}

// FrameworkUtils implementation
std::unique_ptr<factor_engine> framework_utils::create_factor_system(
    const std::string& feature_csv_path,
    const std::string& data_directory,
    const framework_config& config) {

    // 创建因子管理器
    auto factor_manager = std::make_shared<factor_framework::factor_manager>();
    if (!factor_manager->load_factors_from_csv(feature_csv_path)) {
        std::cerr << "Failed to load factors from " << feature_csv_path << std::endl;
        return nullptr;
    }

    // 创建数据接口
    auto data_interface_unique = data_loader_factory::create_from_test_data(data_directory);
    if (!data_interface_unique || data_interface_unique->get_field_names().empty()) {
        std::cerr << "Failed to load data from " << data_directory << std::endl;
        return nullptr;
    }

    // 转换为shared_ptr
    std::shared_ptr<factor_framework::data_interface> data_interface(data_interface_unique.release());

    // 创建计算引擎
    auto engine = std::make_unique<factor_engine>(factor_manager, data_interface);

    // 应用配置
    engine->set_num_threads(config.default_num_threads);
    engine->enable_performance_stats(config.enable_performance_stats);
    engine->enable_verbose_logging(config.verbose_logging);
    engine->set_timeout(config.default_timeout_ms);

    return engine;
}

std::unique_ptr<factor_engine> framework_utils::create_test_system(
    const std::string& feature_csv_path,
    int rows,
    int cols,
    const framework_config& config) {

    // 创建因子管理器
    auto factor_manager = std::make_shared<factor_framework::factor_manager>();
    if (!factor_manager->load_factors_from_csv(feature_csv_path)) {
        std::cerr << "Failed to load factors from " << feature_csv_path << std::endl;
        return nullptr;
    }

    // 创建随机测试数据
    auto data_interface_unique = data_loader_factory::create_random_test_data(rows, cols);

    // 转换为shared_ptr
    std::shared_ptr<factor_framework::data_interface> data_interface(data_interface_unique.release());

    // 创建计算引擎
    auto engine = std::make_unique<factor_engine>(factor_manager, data_interface);

    // 应用配置
    engine->set_num_threads(config.default_num_threads);
    engine->enable_performance_stats(config.enable_performance_stats);
    engine->enable_verbose_logging(config.verbose_logging);
    engine->set_timeout(config.default_timeout_ms);

    return engine;
}

std::string framework_utils::run_benchmark(
    factor_engine& engine,
    const std::vector<int>& factor_ids,
    int num_runs) {

    std::ostringstream report;
    report << "=== Factor Calculation Benchmark ===\n";
    report << "Factors: " << factor_ids.size() << "\n";
    report << "Runs: " << num_runs << "\n\n";

    std::vector<std::chrono::milliseconds> run_times;
    run_times.reserve(num_runs);

    for (int run = 0; run < num_runs; ++run) {
        auto result = engine.calculate_factors(factor_ids);
        if (result.success) {
            run_times.push_back(result.calculation_time);
        } else {
            report << "Run " << (run + 1) << " failed: " << result.error_message << "\n";
        }
    }

    if (!run_times.empty()) {
        auto total_time = std::chrono::milliseconds(0);
        auto min_time = run_times[0];
        auto max_time = run_times[0];

        for (const auto& time : run_times) {
            total_time += time;
            min_time = std::min(min_time, time);
            max_time = std::max(max_time, time);
        }

        auto avg_time = total_time / run_times.size();

        report << "Results:\n";
        report << "  Successful runs: " << run_times.size() << "/" << num_runs << "\n";
        report << "  Average time: " << avg_time.count() << " ms\n";
        report << "  Min time: " << min_time.count() << " ms\n";
        report << "  Max time: " << max_time.count() << " ms\n";
        report << "  Total time: " << total_time.count() << " ms\n";
    }

    return report.str();
}

bool framework_utils::validate_factor_results(
    factor_engine& engine,
    const std::vector<int>& factor_ids) {

    auto result = engine.calculate_factors(factor_ids);
    if (!result.success) {
        std::cerr << "Validation failed: " << result.error_message << std::endl;
        return false;
    }

    bool all_valid = true;

    for (const auto& [factor_name, factor_data] : result.factor_results) {
        // 检查数据维度
        if (factor_data.rows() == 0 || factor_data.cols() == 0) {
            std::cerr << "Factor " << factor_name << " has invalid dimensions" << std::endl;
            all_valid = false;
            continue;
        }

        // 检查数据有效性
        int valid_count = 0;
        int total_count = factor_data.rows() * factor_data.cols();

        for (int i = 0; i < factor_data.rows(); ++i) {
            for (int j = 0; j < factor_data.cols(); ++j) {
                if (std::isfinite(factor_data(i, j))) {
                    valid_count++;
                }
            }
        }

        double valid_ratio = (double)valid_count / total_count;
        if (valid_ratio < 0.1) {  // 至少10%的数据应该是有效的
            std::cerr << "Factor " << factor_name << " has too few valid values: "
                     << valid_ratio * 100 << "%" << std::endl;
            all_valid = false;
        }
    }

    return all_valid;
}

std::string framework_utils::get_system_info() {
    std::ostringstream info;
    info << "=== System Information ===\n";
    info << "Hardware concurrency: " << std::thread::hardware_concurrency() << "\n";
    info << "Eigen version: " << EIGEN_WORLD_VERSION << "." << EIGEN_MAJOR_VERSION << "." << EIGEN_MINOR_VERSION << "\n";

    return info.str();
}

std::string framework_utils::get_framework_info() {
    std::ostringstream info;
    info << "=== Framework Information ===\n";
    info << "Version: " << framework_version::toString() << "\n";
    info << "Build date: " << __DATE__ << " " << __TIME__ << "\n";

    return info.str();
}

void framework_initializer::initialize(const framework_config& config) {
    if (initialized_) {
        return;
    }

    config_ = config;
    config_.load_from_environment();  // 允许环境变量覆盖

    std::cout << "Initializing Factor Framework v" << framework_version::toString() << std::endl;
    std::cout << "Configuration:" << std::endl;
    std::cout << "  Threads: " << config_.default_num_threads << std::endl;
    std::cout << "  Performance stats: " << (config_.enable_performance_stats ? "enabled" : "disabled") << std::endl;
    std::cout << "  Verbose logging: " << (config_.verbose_logging ? "enabled" : "disabled") << std::endl;

    initialized_ = true;
}

void framework_initializer::cleanup() {
    if (!initialized_) {
        return;
    }

    std::cout << "Cleaning up Factor Framework" << std::endl;
    initialized_ = false;
}

bool framework_initializer::is_initialized() {
    return initialized_;
}

} // namespace factor_framework
