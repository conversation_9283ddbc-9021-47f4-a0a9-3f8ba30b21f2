import os
import time
import pandas as pd
import numpy as np

def Power(s1, n):
    n = int(n)
    return np.power(s1, n)

def load_csv(filepath):
    """加载 CSV 文件到 pandas DataFrame。"""
    try:
        df = pd.read_csv(filepath, index_col=0)
        return df
    except Exception as e:
        print(f"加载 {filepath} 出错: {e}")
        return None

def measure_execution_time(func, iterations=100):
    """测量函数执行时间（微秒）"""
    # 预热
    func()

    start_time = time.perf_counter()

    for _ in range(iterations):
        result = func()
        if isinstance(result, pd.DataFrame):
            dummy = result.iloc[0, 0] if not result.empty else 0

    end_time = time.perf_counter()

    # 返回平均执行时间（微秒）
    return (end_time - start_time) * 1000000 / iterations
  
def main():
    # 从命令行参数中获取文件路径
    data_dir = "/home/<USER>/git/feature_operators/test_right/test_data"

    print("加载测试数据...")
    close = load_csv(os.path.join(data_dir, "close.csv"))
    
    res = measure_execution_time(lambda: Power(close, 2))
    
    print(res)

main()
    
    