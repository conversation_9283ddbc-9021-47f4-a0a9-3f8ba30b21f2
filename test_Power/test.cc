#include <Eigen/Dense>      
#include <iostream>
#include <iomanip>
#include <vector>
#include <string>
#include <cmath>
#include <fstream>
#include <sstream>
#include <chrono>
#include <map>
using DataFrame = Eigen::ArrayXXd;
               // For Eigen::ArrayBase and core Eigen functionalities
// 从CSV文件加载数据
DataFrame loadCsv(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filepath << std::endl;
        return DataFrame();
    }

    std::vector<std::vector<double>> data;
    std::string line;

    // 跳过标题行
    std::getline(file, line);

    // 读取数据行
    while (std::getline(file, line)) {
        std::vector<double> row;
        std::stringstream ss(line);
        std::string cell;

        // 跳过第一列（日期）
        std::getline(ss, cell, ',');

        // 读取数据列
        while (std::getline(ss, cell, ',')) {
            try {
                double value = std::stod(cell);
                row.push_back(value);
            } catch (const std::exception& e) {
                row.push_back(std::numeric_limits<double>::quiet_NaN());
            }
        }

        if (!row.empty()) {
            data.push_back(row);
        }
    }

    // 创建Eigen矩阵
    int rows = data.size();
    int cols = rows > 0 ? data[0].size() : 0;

    DataFrame matrix(rows, cols);
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            matrix(i, j) = data[i][j];
        }
    }

    return matrix;
}


template<typename Derived>
auto Power(const Eigen::ArrayBase<Derived>& base, double exponent) {
    // Eigen's pow() function for arrays takes the exponent as the argument.
    // The scalar type of 'base' should be compatible with raising to a double 'exponent'.
    return base.pow((int)exponent);
}

// 测量函数执行时间的辅助函数（微秒）
template<typename Func>
double measureExecutionTime(Func func, int iterations) {
    // 预热
    func();

    auto start = std::chrono::high_resolution_clock::now();

    // 多次执行以获得更准确的时间
    for (int i = 0; i < iterations; ++i) {
        func();
    }

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::micro> duration = end - start;

    // 返回平均执行时间（微秒）
    return duration.count() / iterations;
}

int main() {
    std::string data_path =
        "/home/<USER>/git/feature_operators/test_right/test_data/close.csv";
    DataFrame close = loadCsv(data_path);
    std::cout << "Data loaded. Rows: " << close.rows()
              << ", Cols: " << close.cols() << std::endl;
    double execution_time = measureExecutionTime([&]() {
        DataFrame result = Power(close, 2);
        volatile double dummy = result(0, 0);
    }, 100);

    std::cout << "Execution time: " << execution_time << " microseconds" << std::endl;

    return 0;
}